<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11" height="11" viewBox="0 0 11 11">
  <defs>
    <style>
      .cls-1 {
        fill: none;
      }

      .cls-2 {
        clip-path: url(#clip-path);
      }

      .cls-3 {
        clip-path: url(#clip-path-2);
      }

      .cls-4 {
        fill: #999;
      }
    </style>
    <clipPath id="clip-path">
      <path id="路径_6157" data-name="路径 6157" class="cls-1" d="M265.5-55a5.5,5.5,0,0,1,5.5,5.5,5.5,5.5,0,0,1-5.5,5.5,5.5,5.5,0,0,1-5.5-5.5A5.5,5.5,0,0,1,265.5-55Zm0,.737a4.763,4.763,0,0,0-4.763,4.763,4.763,4.763,0,0,0,4.763,4.763,4.763,4.763,0,0,0,4.763-4.763A4.763,4.763,0,0,0,265.5-54.263Zm0,6.606a.51.51,0,0,1,.51.51.51.51,0,0,1-.51.51.51.51,0,0,1-.51-.51A.51.51,0,0,1,265.5-47.657Zm0-4.9a.369.369,0,0,1,.368.36v3.439a.369.369,0,0,1-.369.369.369.369,0,0,1-.368-.36v-3.439a.369.369,0,0,1,.369-.369Z"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="路径_6156" data-name="路径 6156" class="cls-1" d="M-31,637H344V-175H-31Z"/>
    </clipPath>
  </defs>
  <g id="组_4655" data-name="组 4655" class="cls-2" transform="translate(-260 55)">
    <g id="组_4654" data-name="组 4654" class="cls-3">
      <path id="路径_6155" data-name="路径 6155" class="cls-4" d="M255-60h21v21H255Z"/>
    </g>
  </g>
</svg>
