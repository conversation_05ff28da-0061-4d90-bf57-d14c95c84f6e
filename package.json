{"name": "huzhoushi-jiankang<PERSON>uan", "version": "1.0.0", "private": true, "description": "躯体健康指数系统", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build:prod": "umi build", "build:test": "cross-env UMI_ENV=dev umi build", "deploy": "npm run site && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "prettier": "prettier -c --write \"src/**/*\"", "start": "cross-env UMI_ENV=dev umi dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev umi dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev umi dev", "start:no-ui": "cross-env UMI_UI=none UMI_ENV=dev umi dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev umi dev", "pretest": "node ./tests/beforeTest", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.5.0", "@ant-design/pro-descriptions": "^1.6.8", "@ant-design/pro-form": "^1.18.3", "@ant-design/pro-layout": "^6.15.3", "@ant-design/pro-table": "^2.30.8", "@umijs/route-utils": "^1.0.36", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "antd": "^4.14.0", "classnames": "^2.2.6", "dayjs": "^1.11.10", "echarts": "^5.4.3", "lodash": "^4.17.11", "moment": "^2.25.3", "nanoid": "^3.2.0", "omit.js": "^2.0.2", "postcss-plugin-px2rem": "^0.8.1", "react": "^17.0.0", "react-dev-inspector": "^1.1.1", "react-dom": "^17.0.0", "react-helmet-async": "^1.0.4", "umi": "^v3.4.0", "umi-request": "^1.4.0", "umi-plugin-keep-alive": "^0.0.1-beta.35", "react-activation": "^0.13.0", "html-react-parser": "^3.0.16"}, "devDependencies": {"@ant-design/pro-cli": "^2.0.2", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.144", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@umijs/fabric": "^2.6.2", "@umijs/openapi": "^1.1.14", "@umijs/plugin-blocks": "^2.0.5", "@umijs/plugin-esbuild": "^1.0.1", "@umijs/plugin-openapi": "^1.2.0", "@umijs/preset-ant-design-pro": "^1.2.0", "@umijs/preset-dumi": "^1.1.7", "@umijs/preset-react": "^1.7.4", "@umijs/yorkie": "^2.0.3", "carlo": "^0.9.46", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "enzyme": "^3.11.0", "eslint": "^7.1.0", "express": "^4.17.1", "gh-pages": "^3.0.0", "jsdom-global": "^3.0.2", "lint-staged": "^10.0.0", "mockjs": "^1.0.1-beta3", "prettier": "^2.0.1", "puppeteer-core": "^8.0.0", "stylelint": "^13.0.0", "typescript": "^4.2.2"}, "engines": {"node": ">=10.0.0"}}