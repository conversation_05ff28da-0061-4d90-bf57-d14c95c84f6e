import { Settings as LayoutSettings } from '@ant-design/pro-layout';

const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
  // 当前发性版本，用来check前端版本
  version?: string;
  // 是否报告前端错误
  reportError?: boolean;
  // 前端报告错误的网址
  reportUrl: string;
} = {
  navTheme: 'light',
  primaryColor: 'rgba(0, 102, 255, 1)',
  layout: 'side',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: '湖州市健康集团',
  pwa: true,
  logo: '../public/logo_IMG.png',
  iconfontUrl: '',
  menu: {
    locale: false,
    defaultOpenAll: true,
  },
  headerHeight: '3rem',
  headerRender: undefined,
  splitMenus: false,
  reportError: false,
  reportUrl: '/frontendError/report',
  version: 'haiyul-2021.01',
};

export default Settings;
