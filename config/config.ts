import { defineConfig } from 'umi';
import px2rem from 'postcss-plugin-px2rem';
import defaultSettings from './defaultSettings';
import routes from './routes';

// const { REACT_APP_ENV } = process.env;

export default defineConfig({
  hash: true,
  antd: {},
  dva: { hmr: true },
  layout: {
    locale: false,
    siderWidth: '14.75rem',
    ...defaultSettings,
  },
  // https://umijs.org/zh-CN/plugins/plugin-locale
  locale: {
    default: 'zh-CN',
    antd: false,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: false,
  },
  dynamicImport: { loading: '@ant-design/pro-layout/es/PageLoading' },
  targets: { ie: 8 },
  routes,
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: { 'primary-color': defaultSettings.primaryColor },
  history: { type: 'browser' },
  nodeModulesTransform: {
    type: 'none',
    exclude: [],
  },
  extraPostCSSPlugins: [
    px2rem({
      rootValue: 16, //开启hd后需要换算：rootValue=designWidth*100/750,此处设计稿为1920，所以1920*100/750=256
      // propBlackList: ['border', 'border-top', 'border-left', 'border-right', 'border-bottom', 'border-radius', 'font-size', 'margin'],//这些属性不需要转换
      selectorBlackList: ['ant-pro-basicLayout'], //以包含t_npx的class不需要转换
    }),
  ],
  esbuild: {},
  title: false,
  ignoreMomentLocale: true,
  // 路由前缀
  base: '/web/',
  publicPath: '/web/',
  proxy: {
    '/api/': {
      target: 'http://************:9028',
      // target: 'http://*************:8080',
      // target: 'http://***************:8079',
      changeOrigin: true,
      ws: true,
      pathRewrite: { '^': '' },
    },
  },
  exportStatic: {},
  manifest: { basePath: '/web' },
  // publicPath: process.env.NODE_ENV == 'production' ? '/web' : '/web',
  // Fast Refresh 热更新
  fastRefresh: {},
  scripts: [{ src: '/web/font.js', defer: true }],
  define: {
    'process.env.STATIC_RESOURCES_URL': 'http://***************:8077/',
  },
});
