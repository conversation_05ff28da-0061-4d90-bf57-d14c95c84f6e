export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        path: '/user',
        routes: [
          {
            name: 'login',
            path: '/user/login',
            component: './user/Login',
          },
        ],
      },
    ],
  },
  {
    path: '/Dataoverview',
    name: '数据总览',
    icon: 'AnalysisIcon',
    // access: 'company_user',
    component: './Analysis',
  },
  {
    path: '/IndexReport',
    name: '健康指数报告',
    icon: 'DataoverViewIcon',
    access: 'company_user',
    routes: [
      {
        path: '/IndexReport/personal',
        name: '个人列表',
        icon: 'PersonalIcon',
        routes: [
          {
            path: '/IndexReport/personal',
            component: './PersonalList',
          },
          {
            path: '/IndexReport/personal/report/',
            hideChildrenInMenu: false,
            component: './PersonalReport',
          },
        ],
      },
      {
        name: '团体列表',
        icon: 'TeamIcon',
        access: 'company_user',
        path: '/IndexReport/team',
        routes: [
          {
            path: '/IndexReport/team',
            component: './TableList',
          },
          {
            path: '/IndexReport/team/report/',
            hideChildrenInMenu: false,
            component: './TeamReport',
          },
        ],
      },
    ],
  },
  {
    path: '/Usermanagement',
    name: '用户管理',
    access: 'company_user',
    icon: 'TeamManagementIcon',
    routes: [
      {
        name: '注册用户',
        icon: 'TeamIcon',
        path: '/Usermanagement/RegisteredUser',
        routes: [
          {
            path: '/Usermanagement/RegisteredUser',
            component: './RegisteredUser',
          },
        ],
      },
      {
        name: '后台用户',
        icon: 'TeamIcon',
        path: '/Usermanagement/BackgroundUser',
        routes: [
          {
            path: '/Usermanagement/BackgroundUser',
            component: './BackgroundUser',
          },
        ],
      },
    ],
  },
  {
    path: '/patientManage',
    name: '就诊人管理',
    icon: 'UserOutlinedIcon',
    // access: 'comm_user',
    routes: [
      {
        path: '/patientManage/indexManage',
        name: '就诊人管理',
        component: './PatientManage',
      },
      {
        path: '/patientManage/teamManage',
        name: '团队管理',
        component: './PatientManage/TeamManage',
      },
      {
        path: '/patientManage/batchManage',
        name: '批次管理',
        component: './PatientManage/BatchManage',
      },
    ],
  },
  {
    path: '/healthRecords',
    name: '健康档案',
    icon: 'UnorderedListIcon',
    // access: 'comm_user',
    component: './HealthRecords',
  },
  {
    path: '/numberSourceManage',
    name: '号源管理',
    // access: 'company_user',
    icon: 'TeamOutlinedIcon',
    routes: [
      {
        path: '/numberSourceManage/indexManage',
        name: '号源管理',
        component: './NumberSourceManage',
      },
      {
        path: '/numberSourceManage/lookOver',
        name: '号源查看',
        component: './NumberSourceManage/NumberSourceLookOver',
      },
    ],
  },
  {
    path: '/packageManage',
    name: '套餐管理',
    // access: 'company_user',
    icon: 'AppstoreOutlinedIcon',
    routes: [
      /*{
        path: '/packageManage/indexManage',
        name: '指标管理',
        component: './PackageManage/IndexManage',
      },*/
      {
        path: '/packageManage/projectManage',
        name: '项目管理',
        component: './PackageManage/ProjectManage',
      },
      {
        path: '/packageManage/packageManage',
        name: '套餐管理',
        component: './PackageManage/PackageManage',
      },
    ],
  },
  {
    path: '/appointmentManage',
    name: '预约管理',
    // access: 'company_user',
    icon: 'ProfileOutlinedIcon',
    routes: [
      {
        path: '/appointmentManage/appointmentRecords',
        name: '预约记录',
        component: './AppointmentManage/AppointmentRecords',
      },
    ],
  },
  {
    path: '/paymentManage',
    name: '支付账单',
    // access: 'company_user',
    icon: 'MoneyCollectOutlinedIcon',
    routes: [
      {
        path: '/paymentManage/paymentHistory',
        name: '支付记录',
        component: './PaymentManage/PaymentHistory',
      },
      {
        path: '/paymentManage/refundHistory',
        name: '退款记录',
        component: './PaymentManage/RefundHistory',
      },
    ],
  },
  {
    path: '/personalDetail/',
    icon: 'PersonalIcon',
    access: 'comm_user',
    name: '个人详情',
    component: './PersonalReport',
  },
  {
    path: '/healthScience',
    name: '健康科普',
    icon: 'RobotOutlinedIcon',
    routes: [
      {
        path: '/healthScience/articleManage',
        name: '文章发布',
        routes: [
          {
            path: '/healthScience/articleManage',
            component: './HealthScience/ArticleManage',
          },
          {
            path: '/healthScience/articleManage/editor',
            component: './HealthScience/ArticleManage/ArticleEditor',
          },
          {
            path: '/healthScience/articleManage/editor/:id',
            component: './HealthScience/ArticleManage/ArticleEditor',
          },
          {
            path: '/healthScience/articleManage/detail/:id',
            component: './HealthScience/ArticleManage/ArticleDetail',
          },
        ],
      },
      {
        path: '/healthScience/videoManage',
        name: '视频发布',
        routes: [
          {
            path: '/healthScience/videoManage',
            component: './HealthScience/VideoManage',
          },
          {
            path: '/healthScience/videoManage/editor',
            component: './HealthScience/VideoManage/VideoEditor',
          },
          {
            path: '/healthScience/videoManage/editor/:id',
            component: './HealthScience/VideoManage/VideoEditor',
          },
          {
            path: '/healthScience/videoManage/detail/:id',
            component: './HealthScience/VideoManage/VideoDetail',
          },
        ],
      },
      {
        path: '/healthScience/tagsManage',
        name: '标签管理',
        component: './HealthScience/TagsManage',
      }
    ],
  },
  {
    path: '/',
    component: './RouterComponent',
  },
  {
    component: './404',
  },
];
