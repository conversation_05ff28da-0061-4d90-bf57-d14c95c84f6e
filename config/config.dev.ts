import { defineConfig } from 'umi';

export default defineConfig({
  plugins: [
    // https://github.com/zthxxx/react-dev-inspector
    'react-dev-inspector/plugins/umi/react-inspector',
  ],
  locale: {
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: false,
  },
  nodeModulesTransform: {
    type: 'none',
    exclude: [],
  },
  // mfsu: {},
  // proxy: {
  //   '/api/': {
  //     target: 'https://preview.pro.ant.design',
  //     changeOrigin: true,
  //     ws: true,
  //     pathRewrite: { '^': '' },
  //   },
  // },
  // https://github.com/zthxxx/react-dev-inspector#inspector-loader-props
  inspectorConfig: {
    exclude: [],
    babelPlugins: [],
    babelOptions: {},
  },
  // mfsu: {},
  // webpack5: {
  //   lazyCompilation: {},
  // },
  define: {
    'process.env.STATIC_RESOURCES_URL': 'http://43.143.39.65:9028/',
  },
});
