import { TeamList_URL } from '@/services/Url';
import request from '@/utils/request';
/**
 * @function 团队列表初始化请求
 * @param params
 * @returns
 */
function teamList(params?: any): any {
  return request(TeamList_URL.init, {
    method: 'GET',
    params: { ...params },
    signal: new AbortController().signal,
  });
}
/**
 * @function 团体下拉选项数据
 * @param params
 * @returns
 */
function teamSelect(params?: any) {
  return request(TeamList_URL.select, {
    method: 'GET',
    params: { ...params },
    signal: new AbortController().signal,
  });
}

function add_report() {
  return request(TeamList_URL.add, {
    method: 'GET',
    params: {},
    signal: new AbortController().signal,
  });
}
export function exportWordcCompanyList(params: any) {
  return request('/word/word/export_word_company/', {
    method: 'POST',
    data: { ...params },
    responseType: 'blob',
    headers: { 'Content-Type': 'application/json;application/octet-stream' }
  });
}

export { add_report, teamSelect, teamList };
