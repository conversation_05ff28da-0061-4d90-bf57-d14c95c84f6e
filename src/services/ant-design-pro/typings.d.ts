// @ts-ignore
/* eslint-disable */

declare namespace API {
  interface common {
    min: string;
    max: string;
    avg: string;
    percent: string;
  }

  type average = {
    company: common;
    sex_age: common;
  };
  type CurrentUser = {
    name?: string;
    avatar?: string;
    userid?: string;
    email?: string;
    signature?: string;
    title?: string;
    group?: string;
    tags?: { key?: string; label?: string }[];
    notifyCount?: number;
    unreadCount?: number;
    country?: string;
    access?: string;
    geographic?: {
      province?: { label?: string; key?: string };
      city?: { label?: string; key?: string };
    };
    address?: string;
    phone?: string;
  };
  type Token = {
    access_token?: string;
    refresh_token?: string;
  };
  type LoginResult = {
    status?: string;
    type?: string;
    currentAuthority?: string;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };
  interface ItemProps {
    name1: string;
    name2: string;
    checkupId?: string;
    defaultId?: string;
    dispatch?: any;
    checkup_id?: any;
    user_id?: any;
  }

  type FeedState = {
    back: boolean;
    keyword?: string;
    item_name?: string;
    index_name?: string;
    cancel?: boolean;
    star: number;
    flag?: boolean;
    marks?: number;
    item_coding?: string;
    index_coding?: string;
    changeBack: (e: boolean) => void;
    data?: any;
    checkup_id?: string;
  };
  type feedData = Omit<FeedState, 'changeBack'>;
  interface UserModelShowState {
    hoverKey: string;
    modal1Visible: boolean;
    modal2Visible: boolean;
    modal3Visible?: boolean;
    modal4Visible?: boolean;
    GradingFlag: boolean;
    flag: number;
    name?: string;
    cardData?: any;
  }
  interface params {
    checkup_id?: string;
    l0_name?: string;
    l1_name?: string;
    l3_name: any;
    l5_name: any;
  }
  interface detail {
    changeGrading: any;
    detailModal?: any[];
  }

  type TeamList = {
    data?: any[];
    /** 列表的内容总数 */
    total?: number;
    page_num?: any;
    current: any;
    page_size: number;
    company_id?: string;
    score_avg_gte?: string;
    score_avg_lt?: string;
    /**团体排序字段  */
    sorter?: any;
    ascending?: any;
    city_district?: string;
    /**下拉选项 */
    option?: any[];
    flag: boolean;
    success?: boolean;
  };
  type Team_choose = {
    commercial_name: string;
    commercial_code: string;
    score_avg_gte: any;
    score_avg_lt: any;
    age_avg_gte: any;
    age_avg_lt: any;
  };
  type FakeCaptcha = {
    code?: number;
    status?: string;
  };

  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  interface dataType {
    user_id?: string;
    company?: string;
    name?: string;
    gender?: string;
    birthday?: string;
    last_check_score?: string;
    last_check_time?: string;
    last_check_id?: string;
  }
  type list = {
    page_size: any;
    current: any;
    total: any;
    data: dataType[];
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };
  type SorterType = 'ascend' | 'descend' | undefined;
  //个人列表排序字段
  interface Sorter {
    last_check_time: SorterType;
    name: SorterType;
    age: SorterType;
    commercial_name: SorterType;
    last_check_score: SorterType;
  }
  //团体列表排序字段
  interface teamSorter {
    commercial_name: SorterType;
    commercial_code: SorterType;
    last_check_time: SorterType;
    age_avg: SorterType;
    score_avg: SorterType;
  }

  //团体列表筛选字段
  interface teamFilter {
    commercial_name?: string | undefined;
    commercial_code?: string | undefined;
    score_avg_gte?: string | undefined;
    score_avg_lt?: string | undefined;
    age_avg_gte?: string | undefined;
    age_avg_lt?: string | undefined;
  }
  interface pageType {
    page_size: number;
    page_num: number;
  }
}
