// import { PersonList_URL } from '@/services/Url';
import request from '@/utils/request';
/**
 * @name 个人列表初始化请求
 * @param params
 */
export function List(params: any) {
  return request('/api/health/health/user_list/', {
    method: 'GET',
    params,
    signal: new AbortController().signal,
  });
}
export function exportWordList(params: any) {
  return request('/word/word/export_word/', {
    method: 'POST',
    data:{...params},
    responseType:'blob',
    headers: { 'Content-Type': 'application/json;application/octet-stream' }
  });
}
