// import { PersonList_URL } from '@/services/Url';
import request from '@/utils/request';
/**
 * @name 后台用户
 * @param params
 */
// 后台用户列表
export function BackgroundUserList(params: any) {
  return request('/api/user/backend_users/', {
    method: 'GET',
    params,
    signal: new AbortController().signal,
  });
}
// 重置密码
export function BackgroundUserPwd(params: any) {
  return request('/api/user/backend_user/pwd/', {
    method: 'POST',
    data: { ...params },
    signal: new AbortController().signal,
  });
}
// 后台用户删除
export function BackgroundUserDel(params: any) {
  return request('/api/user/backend_users/', {
    method: 'DELETE',
    data: { ...params },
    signal: new AbortController().signal,
  });
}
//新增用户
export function BackgroundUserAdd(params: any) {
  return request('/api/user/backend_user/', {
    method: 'POST',
    data: { ...params },
    signal: new AbortController().signal,
  });
}