import request from '@/utils/request';
/**
 * @function 登录初始化请求
 * @param params
 * @returns
 */
export function login(params?: any) {
  return request('/api/user/backend_login/', {
    method: 'POST',
    data: { ...params },
    signal: new AbortController().signal,
  });
}

export function refresh(params?: any) {
  return request('/api/user/token/obtain/', {
    method: 'POST',
    params: { ...params },
    signal: new AbortController().signal,
  });
}
