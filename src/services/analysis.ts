import { Analysis_URL } from '@/services/Url';
import request from '@/utils/request';

export function analysisRadar(params?: any) {
  return request(Analysis_URL.radar, {
    method: 'GET',
    params: { ...params },
    signal: new AbortController().signal,
  });
}

export function analysisCloud(params?: any) {
  return request(Analysis_URL.cloud, {
    method: 'GET',
    params: { ...params },
    signal: new AbortController().signal,
  });
}

export function analysisReportSum(params?: any){
  return request('/api/health/analyse/get_health_report_count',{
    method:'GET',
    params:{ ...params },
    signal: new AbortController().signal
  })
}