// import { PersonList_URL } from '@/services/Url';
import request from '@/utils/request';
/**
 * @name 注册用户
 * @param params
 */
// 注册用户列表
export function RegisteredUserList(params: any) {
  return request('/api/user/register_users/', {
    method: 'GET',
    params,
    signal: new AbortController().signal,
  });
}
// 注册用户删除
export function RegisteredUserDel(params: any) {
  return request('/api/user/register_users/', {
    method: 'DELETE',
    data: { ...params },
    signal: new AbortController().signal,
  });
}
//重置用户证件
export function RestUserCertificate(params: any) {
  return request('/api/user/register_user/identity_info/', {
    method: 'POST',
    data: { ...params },
    signal: new AbortController().signal,
  });
}