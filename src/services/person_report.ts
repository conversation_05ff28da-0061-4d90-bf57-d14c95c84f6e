// import { TeamList_URL } from '@/utils/Url';
import request from '@/utils/request';

// 反馈接口
function FeedbackRequest(params: any) {
  return request('/api/health/health/feedback/', {
    method: 'POST',
    data: { ...params },
    signal: new AbortController().signal,
  });
}

function handleColor(star: number) {
  if (star > 3) {
    return '#F00F00';
  }
  if (star > 2) {
    return '#FFB52B';
  }
  return '#06f';
}

export { handleColor, FeedbackRequest };
