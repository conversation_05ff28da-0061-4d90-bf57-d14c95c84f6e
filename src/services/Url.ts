/**
 * 分析页面网络请求地址
 * @interface url.analysis
 */
export const Analysis_URL: url.analysis = {
  radar: '/api/health/analyse/overall_score_radar_chart/',
  cloud: '/api/health/check/word_cloud/',
  age_group: '/api​/health​/check​/age_group_score_chart​/',
  partAbnormal: '/api/health/check/abnormal_organ_rank/',
  history: '/api/health​/check​/year_group_score_chart​/',
};
/**
 * 个人列表页面网络请求地址
 */
export const PersonList_URL: url.person_list = {
  init: '/api/health​/health​/user_list​/',
};
/**
 * 团队列表页面网络请求地址
 */
export const TeamList_URL: url.team_list = {
  init: '/api/health/company/company_list/',
  select: '/api/health/company/company_select/',
  add: '/api/health/analyse/get_health_view_count/',
};
/**
 * 团队报告页面网络请求地址
 */
export const Team_URL: url.team_report = {
  radar: '/api/health/company/company_score_radar_chart/',
  dashboard: '/api/health/company/company_details/',
  score: '/api/health/company/company_details_age_group_list/',
  proportion: '/api/health/company/company_details_lowest_score_and_user/',
  Word_cloud: '/api/health/company/company_details_word_cloud/',
  compare: '/api/health/company/avg_score_common/',
  details_year: '/api/health/company/company_details_year_list/',
};
/**
 * 登陆页面
 */
