// @ts-ignore
/* eslint-disable */
declare namespace url {
  type analysis = {
    history?: string;
    index?: string;
    radar: string;
    cloud: string;
    age_group: string;
    checked?: string;
    partAbnormal: string;
  };

  type person_list = {
    init: string;
    sort?: string;
    choose?: string;
    reset?: string;
  };

  type person_report = {
    base_message?: string;
    data?: string;
    message_list?: string;
    home: string;
    everyYear: string;
    avgScore: string;
    radar: string;
    scoreDetail: string;
    scoreLeftBottom: string;
    scoreData: string;
    perScoreDetail: string;
    abNormal: string;
    QUANTIFY: string;
    QUALITATIVE: string;
    CHECKUP_ILLNESS: string;
    AbNormal_Quantify: string;
    AbNORMAL_COUNT: string;
    KEY_WORD: string;
  };
  type team_list = {
    init: string;
    select: string;
    choose?: string;
    reset?: string;
    add: string;
  };
  type team_report = {
    base_message?: string;
    dashboard: string;
    compare: string;
    proportion: string;
    message_list?: string;
    history?: string;
    part?: string;
    abnormal?: string;
    radar: string;
    word_cloud?: string;
    score: string;
    proportion: string;
    Word_cloud: string;
    details_year: string;
  };
}
