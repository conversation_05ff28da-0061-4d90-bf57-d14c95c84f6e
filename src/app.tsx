import type { MenuDataItem } from '@ant-design/pro-layout';
import { PageLoading } from '@ant-design/pro-layout';
import type { RunTimeLayoutConfig } from 'umi';
import {
  Analysis, ReportIcon, PersonalIcon, TeamIcon, ListIcon,
  TeamManagementIcon, DataoverViewIcon, IndexReportIcon
} from '@/components/MenuIcon';
import { Judge } from '@/utils/utils';
import RightContent from '@/components/RightContent';
import { getToken } from '@/utils/storage';
import styles from '@/app.less';
import LOGO from '@/../public/logo_IMG.png';
import { history, Link } from 'umi';
import computer from '@/../public/computer.svg';
import {
  AppstoreOutlined,
  MoneyCollectOutlined,
  ProfileOutlined,
  RobotOutlined,
  TeamOutlined,
  UnorderedListOutlined,
  UserOutlined,
} from '@ant-design/icons';
const loginPath = '/user/login';
/** 获取用户信息比较慢的时候会展示一个 loading */
export const initialStateConfig = {
  loading: <PageLoading />,
};
// 获取初始信息
export async function getInitialState(): Promise<any> {
  const fetchUserInfo = async () => {
    return undefined;
  };
  // 如果是登录页面，不执行
  if (history.location.pathname !== loginPath) {
    const currentUser = await fetchUserInfo();
    return { fetchUserInfo, currentUser, settings: {} };
  }
  return { fetchUserInfo, settings: {} };
}

/**
 * @IconMap 菜单自定义icon组件
 */
const iconMap = {
  TeamListIcon: <ListIcon />,
  PersonalListIcon: <ListIcon />,
  TeamReportIcon: <ReportIcon />,
  PersonalReportIcon: <ReportIcon />,
  AnalysisIcon: <Analysis />,
  DataoverViewIcon: <DataoverViewIcon />,
  IndexReportIcon: <IndexReportIcon />,
  PersonalIcon: <PersonalIcon />,
  TeamIcon: <TeamIcon />,
  TeamManagementIcon: <TeamManagementIcon />,
  UserOutlinedIcon: <UserOutlined />,
  UnorderedListIcon: <UnorderedListOutlined />,
  AppstoreOutlinedIcon: <AppstoreOutlined />,
  ProfileOutlinedIcon: <ProfileOutlined />,
  MoneyCollectOutlinedIcon: <MoneyCollectOutlined />,
  TeamOutlinedIcon :<TeamOutlined />,
  RobotOutlinedIcon: <RobotOutlined />
};
/**
 * @name 开启二级菜单
 * @param menuItemProps
 * @param defaultDom
 * @returns
 */
const menuItemRender = (menuItemProps: any, defaultDom: any) => {
  // 如果url没有匹配到当前的菜单项，则按照浏览的形式呈现菜单项
  if (menuItemProps.isUrl || !menuItemProps.path || window.location.pathname === menuItemProps.path) {
    return defaultDom;
  }
  return <Link to={menuItemProps.path}> {defaultDom}</Link>;
  // if (menuItemProps.name === '数据总览') {
  //   return <Link to={menuItemProps.path}> {defaultDom}</Link>;
  // }
  // return (
  //   <Link to={menuItemProps.path}>
  //     {menuItemProps.icon}
  //     {defaultDom}
  //   </Link>
  // );
};
/**
 * @name 重新渲染菜单
 * @param menuList
 * @returns
 */
const menuDataRender = (menuList: MenuDataItem[]): MenuDataItem[] => {
  const access = localStorage.getItem('groups');
  if (access === 'comm_user') {
    const result = menuList.filter((item) => item.access === 'comm_user');
    return result.map((item) => {
      const localItem = {
        ...item,
        icon: item.icon && iconMap[item.icon as string],
        children: item.children ? menuDataRender(item.children) : undefined,
      };
      return localItem as MenuDataItem;
    });
  }
  if (access === 'company_user') {
    const result = menuList.filter((item) => item.access === 'company_user');
    return result.map((item) => {
      const localItem = {
        ...item,
        icon: item.icon && iconMap[item.icon as string],
        children: item.children ? menuDataRender(item.children) : undefined,
      };
      return localItem as MenuDataItem;
    });
  }
  const result = menuList.filter((item) => item.access !== 'comm_user');
  return result.map((item) => {
    const localItem = {
      ...item,
      icon: item.icon && iconMap[item.icon as string],
      children: item.children ? menuDataRender(item.children) : undefined,
    };
    return localItem as MenuDataItem;
  });
};
/**
 * @name 菜单顶部内容
 * @returns ReactNode
 */
const menuHeaderRender = () => {
  return (
    <><div className={styles.menuHeader}>
      <div className={styles.logo}>
        <div className={styles.logo_box}>
          <img className={styles.logo_right} src={LOGO} alt={'logo'}></img>
        </div>
      </div>
      {/* <div className={styles.menuHeaderText}>躯体健康评估系统</div> */}
    </div></>
  );
};
/**
 * @name 右侧内容区顶部
 * @returns
 */
const headerContentRender = () => {
  if (Judge() === true) {
    return <div className={styles.headerContent}>欢迎湖州市健康集团!</div>;
  }
  return '';
};
/**
 * 菜单栏底部logo
 * @returns ReactNode
 */
const menuFooterRender = () => (
  <div className={styles.menuFooter}>
    <img src={computer} alt="footerLogo" />
  </div>
);
// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }: any) => {
  return {
    // 关闭菜单国际化
    menu: { locale: false },
    disableMobile: true,
    defaultCollapsed: false,
    breakpoint: false,
    pure: false,
    disableContentMargin: false,
    rightContentRender: () => <RightContent />,
    // 内容页脚
    footerRender: () => {
      return (
        <div className={styles.footer}>
          <div className={styles.footer_content}>复旦大学技术攻关团队技术支持</div>
        </div>
      );
    },

    // 页面刷新触发事件
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      if (getToken() === null && !initialState?.currentUser && location.pathname !== loginPath) {
        history.push(loginPath);
      }
    },
    // 关闭菜单底部抽屉效果
    collapsedButtonRender: () => { return false; },
    // menu左下角logo
    menuFooterRender,
    // 整体顶部栏
    headerContentRender,
    // 菜单数据渲染
    menuItemRender,
    // 菜单样式渲染
    menuDataRender,
    // 菜单顶部
    menuHeaderRender,
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    /**
     * 初始化值
     * TODO: 权限可能需要
     */
    ...initialState?.settings,
  };
};
