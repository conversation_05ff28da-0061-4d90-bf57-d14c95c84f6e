@import '~antd/es/style/themes/default.less';

.headerContent {
  height: 48px;
  overflow: hidden;
  color: #303133;
  font-weight: 500;
  font-size: 16px;
  font-family: 'PingFang SC';
  line-height: 48px;
  white-space: nowrap;
  text-overflow: ellipsis;
  opacity: 1;
}
.menuHeader {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  text-align: center;
}
.menuHeaderText {
  flex: 32px;
  height: 32px;
  z-index: 66;
  overflow: hidden;
  color: #06f;
  font-weight: 500;
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.menuFooter {
  width: 200px;
  height: 150px;
  margin-left: 4px;
  text-align: center;
}
.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 50px;
  margin-top: 32px;
  color: #909399;
  text-align: center;
}
.footer_content {
  flex: 1;
  height: 32px;
  font-size: 16px;
}
.menuFooter > img {
  width: 116px;
  height: 116px;
}

.logo {
  display: flex;
  flex: 30px;
  align-items: center;
  .logo_box {
    flex: 1;
  }
  .logo_left {
    margin-right: 24px;
  }
  .logo_line {
    height: 26px;
  }
  .logo_right {
    width: 130px;
    height: 50px;
    position: absolute;
    top: 10px;
    left: 52px;
    z-index: 0;
    // margin-left: 24px;
  }
}
