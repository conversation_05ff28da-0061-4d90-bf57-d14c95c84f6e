@import '~antd/es/style/themes/default.less';
html,
body,
#root {
  min-width: 1280px;
  height: 100%;
  font-size: 16px;
  font-family: 'PingFang SC';
}
@font-face {
  font-family: 'PingFang SC';
  src: url('../public/font/PingFang_Sc.ttf') format('woff');
}

.base-content {
  margin: 16px !important;
}

.ant-pro-basicLayout .ant-layout-header.ant-pro-header-light {
  background: #f7f7f8;
}
.ant-pro-basicLayout {
  min-width: 1280px;
}
.ant-layout-content .ant-pro-table {
  .ant-pro-table-search{
    margin-bottom: unset;
    border-bottom: 1px solid #e8e8e8;
  }

  .ant-table-thead > tr > th {
    color: #606266;
    background: rgba(235, 238, 245, 1);
    border-bottom: none;
  }
}



/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width:200px) {
  html {
    font-size: 12px;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1260px) {
  html {
    font-size: 12px;
  }
}

@media (min-width: 1420px) {
  html {
    font-size: 12px;
  }
}
@media only screen and (min-width: 1670px) {
  html {
    font-size: 14px;
  }
}
@media only screen and (min-width: 1900px) {
  html {
    font-size: 16px;
  }
}
@media only screen and (min-width: 2300px) {
  html {
    font-size: 20px;
  }
}
.colorWeak {
  filter: invert(80%);
}

// .ant-menu-submenu-selected{
//   path{
//     stroke: #0066ff;
//   }
// }
.ant-layout {
  min-height: 100vh;
}
.ant-menu-submenu{}
.ant-pro-sider-links {
  display: none;
}
.ant-pro-basicLayout-content {
  margin: 0 !important;
}
.ant-spin-nested-loading {
  min-height: inherit;
}
// .ant-spin-container {
//   height: 760px;
//   min-height: inherit;
// }
.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}
.ant-menu {
  color: rgba(159, 172, 184, 1);
}

.ant-pro-sider.ant-layout-sider-light .ant-menu-item a {
  color: inherit !important;
}
canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;
        > span {
          display: block;
        }
      }
    }
  }
}

// 兼容IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

.editable-cell {
  position: relative;
}

.not-editable-cell-value-wrap {
  padding: 5px 12px;
}

.editable-cell-value-wrap {
  padding: 5px 12px;
  cursor: pointer;
}

.ant-table-cell .editable-cell-value-wrap {
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}

[data-theme='dark'] .ant-table-cell:hover .editable-cell-value-wrap {
  border: 1px solid #434343;
}
