@import '~antd/es/style/themes/default.less';

.score_card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  // height: 100%;
  // background: #f2f9f9;
  background-color: #eff4ff;
  border-radius: 4px;
}
.score_title {
  display: flex;
  flex: 30px;
  justify-content: space-between;
  height: 30px;
  margin:16px;
  margin-bottom: 5px;
  align-items: center;
  .title_left {
    // flex: 40px;
    margin-right: 5px;
    .image {
      width: 30px;
      height: 30px;
      background-repeat: no-repeat;
      background-position: center;
      border-radius: 15px;
    }
  }
  .title_right {
    // flex: 350px;
    flex:1;
    margin-left: 4px;
    color: #333333;
    font-size:16px;
    // line-height: 32px;
  }
}
.spin{
  flex: 120px;
  height: 120px;
  margin: 0 1.25rem;
  display: flex;
  align-items: center;
}
.score_description {
  flex: 26px;
  height: 26px;
  margin: 0 1.25rem;
  .all_description {
    width: 100%;
    // margin: 0 1.25rem;
    overflow: hidden;
    color: #909399;
    font-size: 0.85rem;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .description {
    width: 100%;
    // margin: 0 1.25rem;
    overflow: hidden;
    color: #909399;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
  }
  .score_grid {
    margin: 0 0.3rem;
  }
}

.score_content {
  flex: 65px;
  height: 65px;
  margin: 0 1.25rem;
  .score_position {
    position: relative;
    width: 100%;
    margin-bottom: 10px;
    // height:100%;
    .position {
      position: relative;
      width: 1.5rem;
      // height: 2rem;
    }
    .score_position_mark{
      height: 24px;
      width: 20px;
    }
    .slider {
      position: relative;
      width: 100%;
      height:8px;
      background-color: #dfefef;
      border-radius: 0.375rem;
    }
    .inside {
      height: 100%;
      background-color: #00959e;
      background-image: repeating-linear-gradient(
        307deg,
        hsla(0, 0%, 100%, 0.2),
        hsla(0, 0%, 100%, 0.3) 6px,
        transparent 0,
        transparent 16px
      );
      border-radius: 0.375rem 0 0 0.375rem;
    }
  }
  .position_left {
    position: absolute;
    z-index: 999;
    display: inline-block;
    width: 0.7rem;
    height: 0.7rem;
    background-color: #ffb52b;
    border-radius: 0.35rem;
    box-shadow: 0 0 0 0.2rem #fff;
    // outline: 0.2rem solid #fff;
  }
  .position_right {
    position: absolute;
    z-index: 999;
    display: inline-block;
    width: 0.7rem;
    height: 0.7rem;
    background-color: #ff574b;
    border-radius: 0.35rem;
    box-shadow: 0 0 0 0.2rem #fff;
  }
  .slider_pie {
    position: absolute;
    display: inline-block;
    height: 0.7rem;
    background: linear-gradient(70deg, #ffb52b, #ff574b);
  }
  .score_footer {
    width: 100%;
    overflow: hidden;
    color: #909399;
    font-size: 0.85rem;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .slider_text {
    position: relative;
    top: 0.75rem;
    width: 100%;
    height: 0.7rem;
    color: #909399;
    font-size: 0.8rem;
    border-radius: 0.375rem;
  }
  .value_right {
    position: absolute;
    width: 3rem;
  }
  .value_left {
    position: absolute;
    width: 3rem;
  }
}
