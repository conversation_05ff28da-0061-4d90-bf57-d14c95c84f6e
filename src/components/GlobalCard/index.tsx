import React from 'react';
import styles from './index.less';
// import scoreCard from '@/assets/Team/partTwo/scoreCard3.svg';
// import averageValue from '@/assets/HealthList/averageValue.svg';
import { Popover, Spin, Tooltip } from 'antd';
import exclamatory from '@/../public/exclamatory.svg';

interface cardData {
  min: any;
  max: any;
  avg: any;
  percent: any;
}
type globalCard = {
  title: string;
  img: any;
  mark: any;
  background: '#589AFF' | '#98D34C';
  data: cardData;
  tooltip?: any;
  height?: any;
};
const GlobalCard: React.FC<globalCard> = (props: any) => {
  const { data, img } = props;
  if (props.loading) {
    return (
      <div className={styles.score_card} style={{ height: props.height ? props.height : '100%' }}>
        <div className={styles.score_title}>
          <div className={styles.title_left}>
            <img className={styles.image} src={img} alt="img" />
            {/* <div className={styles.image} style={{ backgroundImage: `url(${img})` }}></div> */}
          </div>
          <div className={styles.title_right}>{props.title}</div>
        </div>
        <div className={styles.spin}>
          <Spin style={{ flex: '1' }}></Spin>
        </div>
      </div>
    );
  }
  return (
    <div className={styles.score_card} style={{ height: props.height ? props.height : '100%' }}>
      <div className={styles.score_title}>
        <div className={styles.title_left}>
          <img className={styles.image} src={img} alt="img" />
          {/* <div className={styles.image} style={{ backgroundImage: `url(${img})` }}></div> */}
        </div>
        <div className={styles.title_right}>{props.title}</div>
        {props.tooltip ? (<div>
          <Popover
            placement="topRight"
            content={<div style={{ width: 300 }}>
              基于近50万人次体检数据，按＜30岁、30-39岁、40-49岁、50-59岁、≥60岁年龄段区间得出的同年龄同性别人群的躯体健康得分分布。
            </div>}>
            <img style={{ marginLeft: '2rem' }} src={exclamatory} alt="提示" />
          </Popover>
        </div>) : ('')}
      </div>
      <div className={styles.score_description}>
        <Tooltip title={`均分${data.avg},最低分${data.min},最高分${data.max}`}>
          <div className={props.mark === '全人群' ? styles.all_description : styles.description}>
            {props.sign ? ('') : (<>
              <span>均分</span>
              <span style={{ color: '#FFB52B', fontSize: '18px' }} className={styles.score_grid}>{data.avg},</span>
            </>)}
            <span>最低分</span>
            <span style={{ color: '#FFB52B', fontSize: '18px' }} className={styles.score_grid}>{data.min},</span>
            <span>最高分</span>
            <span style={{ color: '#FFB52B', fontSize: '18px' }} className={styles.score_grid}>{data.max}</span>
          </div>
        </Tooltip>
      </div>
      <div className={styles.score_content}>
        <div className={styles.score_position}>
          <div className={styles.position} style={{ left: `${data.percent - 3}%` }}>
            <img className={styles.score_position_mark} src={props.mark} alt="平均分" />{/* // style={{ height: '2rem' }} */}
          </div>
          <div className={styles.slider}>
            <div style={{ width: `${data.percent}%`, backgroundColor: props.background }} className={styles.inside} ></div>
          </div>
        </div>
        <div className={styles.score_footer}>
          <Tooltip title={`在该人群在处于${data.percent}%的位置`}>
            <span>在该人群中处于 </span>
            <span>{data.percent}%</span>
            <span> 的位置</span>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default GlobalCard;
