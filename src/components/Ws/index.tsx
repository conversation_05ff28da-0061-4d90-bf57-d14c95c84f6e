/* WebSocket封装
 * @param url: WebSocket接口地址与携带参数必填
 * @param onOpenFunc: WebSocket的onopen回调函数，如果不需要可传null
 * @param onMessageFunc: WebSocket的onmessage回调函数，如果不需要可传null
 * @param onCloseFunc: WebSocket的onclose回调函数，如果不需要可传null
 * @param onErrorFunc: WebSocket的onerror回调函数，如果不需要可传null
 * @param heartMessage: 发送后台的心跳包参数,必填 (给服务端的心跳包就是定期给服务端发送消息)
 * @param timer: 给后台传送心跳包的间隔时间，不传时使用默认值3000毫秒
 * @param isReconnect: 是否断掉立即重连，不传true时不重连
*/
// 本地
// const url = 'ws://122.51.155.188:8002/ws/screen/Pppses/'
// 打包
// const url = 'ws://111.229.63.2:8877/ws/msg/card/'
// const useWebSocket = ( onOpenFunc: any, onMessageFunc: any, onCloseFunc: any, onErrorFunc: any, heartMessage: object, timer: number|null, isReconnect: boolean) => {
//     let isConnected = false // 设定已链接webSocket标记
//     // websocket对象
//     let ws: any = null
//     // 创建并链接webSocket
//     const connect = () => {
//         // 如果未链接webSocket，则创建一个新的webSocket
//         if (!isConnected) {
//             ws = new WebSocket(url)
//             isConnected = true
//         }
//     }
//     // 向后台发送心跳消息
//     const heartCheck = () => {
//         ws.send(JSON.stringify(heartMessage))
//     }
//     // 初始化事件回调函数
//     const initEventHandle = () => {
//         // eslint-disable-next-line consistent-return
//         ws.addEventListener('open', (e: any) => {
//             // 给后台发心跳请求，在onmessage中取得消息则说明链接正常
//             heartCheck()
//             // 如果传入了函数，执行onOpenFunc
//             if (!onOpenFunc) {
//                 return false
//             // eslint-disable-next-line no-else-return
//             } else {
//                 onOpenFunc(e)
//             }
//         })
//         // eslint-disable-next-line consistent-return
//         ws.addEventListener('message', (e: any) => {
//             // 接收到任何后台消息都说明当前连接是正常的
//             if (!e) {
//                 // console.log('get nothing from service')
//                 return false
//             }
//             // 如果传入了函数，执行onMessageFunc
//             if (!onMessageFunc) {
//                 return false
//             // eslint-disable-next-line no-else-return
//             } else {
//                 onMessageFunc(e)
//             }
//         })
//         // eslint-disable-next-line consistent-return
//         ws.addEventListener('close', (e: any) => {
//             // console.log('onclose', e)
//             // 如果传入了函数，执行onCloseFunc
//             if (!onCloseFunc) {
//                 return false
//             // eslint-disable-next-line no-else-return
//             } else {
//                 onCloseFunc(e)
//             }
//             if (isReconnect) { // 如果断开立即重连标志为true
//                 // 重新链接webSocket
//                 connect()
//             }
//         })
//         // eslint-disable-next-line consistent-return
//         ws.addEventListener('error', (e: any) => {
//             // console.log('onerror', e)
//             // 如果传入了函数，执行onErrorFunc
//             if (!onErrorFunc) {
//                 return false
//             // eslint-disable-next-line no-else-return
//             } else {
//                 onErrorFunc(e)
//             }
//             if (isReconnect) { // 如果断开立即重连标志为true
//                 // 重新链接webSocket
//                 connect()
//             }
//         })
//     }
//     // 初始化webSocket
//     (() => {
//         // 1.创建并链接webSocket
//         connect()
//         // 2.初始化事件回调函数
//         initEventHandle()
//         // 3.返回是否已连接
//         return ws
//     })()
// }

// // WebSocket函数使用实例
// useWebSocket(url, // url
//     null, // onopen回调
//     (e) => { // onmessage回调
//         let res = JSON.parse(e.data) // 后端返回的数据
//         console.log(res)
//     },
//     null, // onclose回调
//     null, // onerror回调
//     { // 心跳包消息
//         "action": "9999",
//         "eventType": "100",
//         "requestId": ""
//     },
//     null, // 传送心跳包的间隔时间
//     true // 断掉立即重连
// )

// export default useWebSocket
