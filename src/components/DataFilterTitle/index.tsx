import { Popover } from 'antd';
import React from 'react';
import exclamatory from '../../../public/exclamatory.svg';
import Note from '../Note';
import styles from './index.less';
/**
 * @name 通用卡片标题
 * @param props 卡片title
 * @returns ReactNodes
 */
const CardTitle: React.FC<any> = (props: any) => {
  return (
    <div className={styles.title}>
      {props.title}
      <span>{props.tooltip ? (<div>
        <Popover placement="topRight" content={Note}>
          <img style={{ marginLeft: '2rem' }} src={exclamatory} alt="提示" />
        </Popover>
      </div>) : ('')}</span>
    </div>
  );
};

export default CardTitle;
