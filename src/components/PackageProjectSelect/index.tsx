import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Checkbox, Input, List, Spin, Tabs } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { getProjectOptions } from '@/pages/PackageManage/PackageManage/services';
import type { ProjectListItem } from '@/pages/PackageManage/ProjectManage/type';
import { MarriageLimitEnum } from '@/pages/PackageManage/ProjectManage/type.d';
import { GenderLimitEnum } from '@/pages/PackageManage/IndexManage/type.d';
import { createCaseInsensitiveRegex } from '@/utils/utils';

type Props = {
  showSearch?: boolean;
  defaultSelectedProjects?: number[];
  institutionCode: string | null;
  onChange?: (selectedProjectIds: number[], selectedProjects: ProjectListItem[]) => void;
};

export type RefType = {
  reset: () => void;
};

type DepartmentProjectData = {
  department: string;
  selectedCount?: number;
  projects: ProjectListItem[];
};

const { Search } = Input;
let selectedProjects: ProjectListItem[] = [];

const PackageProjectSelector = forwardRef<RefType, Props>((props, ref) => {
  const [loading, setLoading] = useState(false);
  const [activeKey, setActiveKey] = useState('');
  const { defaultSelectedProjects, onChange, showSearch = true, institutionCode } = props;
  const [inputValue, setInputValue] = useState<string>();
  const [searchValue, setSearchValue] = useState('');
  const [selectedProjectIds, setSelectedProjectIds] = useState<number[]>([]);
  const [departmentProjectData, setDepartmentProjectData] = useState<DepartmentProjectData[]>([]);
  const [departmentProjectOptions, setDepartmentProjectOptions] = useState<DepartmentProjectData[]>(
    [],
  );

  useEffect(() => {
    const newData: DepartmentProjectData[] = [];
    departmentProjectData.forEach((departmentInfo) => {
      let selectedCount = 0;
      const matchedProjects = departmentInfo.projects.filter((project) => {
        if (selectedProjectIds.includes(project.id)) {
          selectedCount++;
        }
        if (searchValue) {
          const regex = createCaseInsensitiveRegex(searchValue);
          return regex.test(project.project_code) || regex.test(project.project_name);
        }
        return true;
      });
      if (matchedProjects.length > 0) {
        newData.push({
          department: departmentInfo.department,
          selectedCount,
          projects: matchedProjects,
        });
      }
    });
    if (newData.length > 0 && newData.findIndex((item) => item.department === activeKey) < 0) {
      setActiveKey(newData[0].department);
    }
    setDepartmentProjectOptions(newData);
  }, [searchValue, departmentProjectData, selectedProjectIds]);

  useEffect(() => {
    selectedProjects = [];
    if (defaultSelectedProjects) {
      setSelectedProjectIds(defaultSelectedProjects);
      if (departmentProjectData) {
        departmentProjectData.forEach((departmentInfo) => {
          departmentInfo.projects.forEach((project) => {
            if (defaultSelectedProjects.includes(project.id)) {
              selectedProjects.push(project);
            }
          });
        });
      }
    }
  }, [defaultSelectedProjects, departmentProjectData]);

  useEffect(() => {
    if (!institutionCode) return;
    setLoading(true);
    const departmentProjectsMap: Record<string, ProjectListItem[]> = {};
    selectedProjects = [];
    getProjectOptions(institutionCode)
      .then((res) => {
        res.data.forEach((item) => {
          if (departmentProjectsMap[item.department]) {
            departmentProjectsMap[item.department].push(item);
          } else {
            departmentProjectsMap[item.department] = [item];
          }
        });
        const initData = Object.entries(departmentProjectsMap).map(([department, projects]) => {
          if (defaultSelectedProjects) {
            projects.forEach((project) => {
              if (defaultSelectedProjects.includes(project.id)) {
                selectedProjects.push(project);
              }
            });
          }
          return { department, projects };
        });
        setDepartmentProjectData(initData);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [institutionCode]);

  const projectSelectedChange = (e: CheckboxChangeEvent, project: ProjectListItem) => {
    let values: number[] = [];
    if (e.target.checked) {
      values = [...selectedProjectIds, project.id];
      selectedProjects.push(project);
    } else {
      values = selectedProjectIds.filter((item) => item !== project.id);
      selectedProjects = selectedProjects.filter((item) => item.id !== project.id);
    }
    setSelectedProjectIds(values);
    onChange?.(values, selectedProjects);
  };

  useImperativeHandle(ref, () => ({
    reset: () => {
      setSearchValue('');
      setInputValue(undefined);
      selectedProjects = [];
      setSelectedProjectIds([]);
      if (departmentProjectData.length > 0) {
        setActiveKey(departmentProjectData[0].department);
      }
    },
  }));

  return (
    <>
      {showSearch && (
        <Search
          style={{ maxWidth: 200 }}
          placeholder="项目编码或项目名称"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onSearch={setSearchValue}
        />
      )}
      <Spin spinning={loading}>
        <Tabs
          style={{ maxHeight: 320, minHeight: 200, marginTop: 10 }}
          size="small"
          type="card"
          tabPosition="left"
          activeKey={activeKey}
          onChange={setActiveKey}
          items={departmentProjectOptions.map((item) => {
            return {
              label: `${item.department}${
                item.selectedCount && item.selectedCount > 0 ? '(' + item.selectedCount + ')' : ''
              }`,
              key: item.department,
              children: (
                <div style={{ maxHeight: 320, overflowY: 'auto' }}>
                  <List
                    size="small"
                    dataSource={item.projects}
                    renderItem={(project) => (
                      <List.Item extra={`￥${project.price}`}>
                        <Checkbox
                          checked={selectedProjectIds.includes(project.id)}
                          onChange={(e) => projectSelectedChange(e, project)}
                          style={{ paddingRight: '15px' }}
                        />
                        <List.Item.Meta
                          title={`${project.project_code} ${project.project_name}
                      ${
                        GenderLimitEnum[project.gender_limit]
                          ? '性别' + GenderLimitEnum[project.gender_limit]
                          : ''
                      }
                      ${
                        MarriageLimitEnum[project.marriage_limit]
                          ? '婚姻' + MarriageLimitEnum[project.marriage_limit]
                          : ''
                      }`}
                          description={project.indicators
                            .map((indicator) => indicator.indicator_name)
                            .join('、')}
                        />
                      </List.Item>
                    )}
                  />
                </div>
              ),
            };
          })}
        />
      </Spin>
    </>
  );
});

export default PackageProjectSelector;
