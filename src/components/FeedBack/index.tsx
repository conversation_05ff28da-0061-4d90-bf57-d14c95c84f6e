import { handleColor, FeedbackRequest } from '@/services/person_report';
import { message, Modal, notification, Rate } from 'antd';
import React, { useEffect, useState } from 'react';
import { Flash } from '../MenuIcon';
import styles from './index.less';

const FeedBack: React.FC<API.FeedState> = (props: API.FeedState) => {
  const [color, setColor] = useState<any>('');
  const [state, setState] = useState<{ care_level: number; reason: string }>({ care_level: 0, reason: '' });
  const [flag, setFlag] = useState<any>(false);
  // Rate组件悬浮改变颜色
  function hover(e: any) {
    if (e > 3) {
      setColor('#F00F00');
    } else if (e === undefined) {
      hover(state.care_level);
    } else if (e > 2) {
      setColor('#FFB52B');
    } else {
      setColor('#06f');
    }
  }
  function handleChange(e: any, params: string) {
    if (params === 'care_level') {
      setColor(handleColor(e));
      setState({ ...state, [params]: e });
    } else {
      setState({ ...state, [params]: e.target.value });
    }
  }
  // 反馈确定和关闭事件
  function handleFinish(params: string) {
    if (params === 'finish') {
      const { item_coding, star, index_coding, item_name, index_name, keyword } = props.data;
      const { reason, care_level } = state;
      if (reason.length < 1) {
        message.warning('请先输入反馈的原因');
        return;
      }
      const real_params = {
        raw_data: {
          item_coding,
          index_coding,
          care_level: star,
          keyword,
          item_name,
          index_name,
        },
        checkup_id: props.checkup_id,
        score_feedback: { care_level, reason },
      };
      FeedbackRequest(real_params).then((res) => {
        if (res === '反馈提交成功') {
          notification.success({ message: '反馈提交成功' });
        } else {
          notification.error({ message: '反馈提交失败' });
        }
      });
      setState({ ...state, reason: '' });
      props.changeBack(false);
    } else if (params === 'cancel') {
      props.changeBack(false);
      setFlag(false);
      setColor('rgba(244,244,244,1)');
    }
  }
  useEffect(() => {
    setFlag(props.back);
    setColor(handleColor(props.star));
    setState({ care_level: props.star, reason: '' });
    return () => {
      setFlag(props.back);
    };
  }, [props.back, props.star]);
  return (
    <Modal
      onCancel={() => { handleFinish('cancel'); }}
      title={'反馈'}
      visible={flag}
      onOk={() => { handleFinish('finish'); }}>
      <div className={styles.concern}>
        <label className={styles.label}>关心程度:</label>
        <Rate
          character={<Flash />}
          style={{ color }}
          onHoverChange={(e) => hover(e)}
          onChange={(e) => { handleChange(e, 'care_level'); }}
          allowHalf={true}
          defaultValue={props.star}
          value={state.care_level}
        ></Rate>
      </div>
      <div className={styles.feed}>
        <label className={styles.label} htmlFor={'fb'}>
          反馈原因:
        </label>
        <textarea
          name="feedback"
          placeholder={'请输入反馈原因'}
          id="fb"
          value={state.reason}
          onChange={(e) => handleChange(e, 'reason')}
          required={true}
          maxLength={500}
          spellCheck={true}
          cols={50}
          rows={10}
          autoComplete={'on'}
        ></textarea>
      </div>
    </Modal>
  );
};

export default FeedBack;
