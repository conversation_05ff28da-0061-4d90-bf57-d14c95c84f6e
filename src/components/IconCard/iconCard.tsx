import React from 'react';
import styles from './IconCard.less';
import type { CardProps } from 'antd/lib/card';
import { Card } from 'antd';

interface IconCardProps extends CardProps {
  icon: string;
  title: string;
  iconBackgroundColor?: string;
  titleStyle?: any;
  style?: any;
  iconBorderColor?: string;
  name?: any;
}

const IconCard: React.FC<IconCardProps> = (props: any) => {
  const { children } = props;
  return (
    <Card
      bodyStyle={{ padding: 12, height: '100%', ...props.bodyStyle }}
      style={{ height: '100%', ...props.style }}
      bordered={props.bordered}>
      <div className={styles.titleWrapper}>
        <span
          className={styles.photo}
          style={{
            backgroundImage: `url('${props.icon}')`,
            backgroundColor: props.iconBackgroundColor,
            borderColor: props.iconBorderColor || props.bodyStyle.backgroundColor || props.iconBackgroundColor,
          }}></span>
        <span className={styles.title} style={{ ...props.titleStyle }}>{props.title}</span>
        <span className={styles.name}>{props.name}</span>
      </div>
      {children}
    </Card>
  );
};
IconCard.defaultProps = {
  bordered: true,
};

export default IconCard;
