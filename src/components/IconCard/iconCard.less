@import '~antd/es/style/themes/default.less';
.titleWrapper {
  position: relative;
  .photo {
    display: inline-block;
    width: 30px;
    height: 30px;
    vertical-align: top;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 16px;
    border-style: solid;
    border-width: 2px;
    border-radius: 15px;
  }
  .name {
    flex:2 ;
    display: inline-block;
    width:3rem;
    height: 23px;
    color: #06f;
  }
  .title {
    display: inline-block;
    margin-top: 5px;
    margin-left: 6px;
    color: rgba(48, 49, 51, 1);
    font-size: 14px;
    vertical-align: top;
  }
  // .name {
  //   display: inline-block;
  //   width: 50px;
  //   height: 20px;
  //   margin-left: 14px;
  //   color: #06f;
  // }
}
