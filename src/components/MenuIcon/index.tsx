import Icon from '@ant-design/icons';
import React from 'react';

const analysisLogo = () => (
  <svg width="1rem" height="1rem" fill="currentColor" viewBox="0 0 16 16">
    <path
      id="路径_3"
      data-name="路径 3"
      d="M83.679,185.2a.636.636,0,0,1,.577.682v.091a.636.636,0,0,1-.577.682H68.833a.636.636,0,0,1-.577-.682v-.091a.636.636,0,0,1,.577-.682ZM72.6,175.02a.636.636,0,0,1,.577.682v8.091a.636.636,0,0,1-.577.682H70.064a.636.636,0,0,1-.577-.682V175.7a.636.636,0,0,1,.577-.682Zm9.846,2.182a.636.636,0,0,1,.577.682v5.909a.636.636,0,0,1-.577.682H79.91a.636.636,0,0,1-.577-.682v-5.909a.636.636,0,0,1,.577-.682h2.538Zm-4.923-6.545a.636.636,0,0,1,.577.682v12.455a.636.636,0,0,1-.577.682H74.987a.636.636,0,0,1-.577-.682V171.338a.636.636,0,0,1,.577-.682Z"
      transform="translate(-68.256 -170.656)"/>
  </svg>
);

const LIST = () => (
  <svg
    id="组_4"
    data-name="组 4"
    fill="currentColor"
    width="1rem"
    height="1rem"
    viewBox="0 0 16 15">
    <path
      id="路径_4"
      data-name="路径 4"
      d="M3.658,98.439a1.219,1.219,0,0,1-1.219,1.219H1.219A1.219,1.219,0,0,1,0,98.439V97.219A1.219,1.219,0,0,1,1.219,96H2.439a1.219,1.219,0,0,1,1.219,1.219Z"
      transform="translate(0 -96)"/>
    <path
      id="路径_5"
      data-name="路径 5"
      d="M3.658,418.439a1.219,1.219,0,0,1-1.219,1.219H1.219A1.219,1.219,0,0,1,0,418.439v-1.219A1.219,1.219,0,0,1,1.219,416H2.439a1.219,1.219,0,0,1,1.219,1.219Z"
      transform="translate(0 -410.329)"/>
    <path
      id="路径_6"
      data-name="路径 6"
      d="M3.658,738.439a1.219,1.219,0,0,1-1.219,1.219H1.219A1.219,1.219,0,0,1,0,738.439v-1.219A1.219,1.219,0,0,1,1.219,736H2.439a1.219,1.219,0,0,1,1.219,1.219Z"
      transform="translate(0 -724.658)"/>
    <path
      id="路径_7"
      data-name="路径 7"
      d="M331,98.439a1.126,1.126,0,0,1-1,1.219h-9a1.126,1.126,0,0,1-1-1.219V97.219A1.126,1.126,0,0,1,321,96h9a1.126,1.126,0,0,1,1,1.219Z"
      transform="translate(-314.998 -96)"/>
    <path
      id="路径_8"
      data-name="路径 8"
      d="M331,418.439a1.126,1.126,0,0,1-1,1.219h-9a1.126,1.126,0,0,1-1-1.219v-1.219A1.126,1.126,0,0,1,321,416h9a1.126,1.126,0,0,1,1,1.219Z"
      transform="translate(-314.998 -410.329)"/>
    <path
      id="路径_9"
      data-name="路径 9"
      d="M331,738.439a1.126,1.126,0,0,1-1,1.219h-9a1.126,1.126,0,0,1-1-1.219v-1.219A1.126,1.126,0,0,1,321,736h9a1.126,1.126,0,0,1,1,1.219Z"
      transform="translate(-314.998 -724.658)"/>
  </svg>
);

const Report = () => (
  <svg width="1rem" fill="currentColor" height="1.06rem" viewBox="0 0 16 17">
    <path
      id="减去_1"
      data-name="减去 1"
      d="M13.866,17H2.133A2.115,2.115,0,0,1,0,14.908V2.092A2.115,2.115,0,0,1,2.133,0H13.866A2.115,2.115,0,0,1,16,2.092V14.908A2.116,2.116,0,0,1,13.866,17ZM4.488,11.393a.83.83,0,0,0-.7.914.831.831,0,0,0,.7.914h5.853a.831.831,0,0,0,.7-.914.83.83,0,0,0-.7-.914Zm0-3.81a.831.831,0,0,0-.7.914.831.831,0,0,0,.7.914h7.023a.831.831,0,0,0,.7-.914.831.831,0,0,0-.7-.914Zm0-3.809a.831.831,0,0,0-.7.914.831.831,0,0,0,.7.914H8a.831.831,0,0,0,.7-.914A.831.831,0,0,0,8,3.775Z"
      transform="translate(0 0.297)"/>
  </svg>
);
const DataoverView = () => {
  return <svg width="1rem" fill="currentColor" height="1.06rem" viewBox="0 0 16 17">
    <g id="Frame">
      <path id="Vector" d="M1.771 2.83333C1.771 2.44213 2.08813 2.125 2.47933 2.125H6.72933L8.50016 4.25H14.521C14.9122 4.25 15.2293 4.56712 15.2293 4.95833V14.1667C15.2293 14.5579 14.9122 14.875 14.521 14.875H2.47933C2.08813 14.875 1.771 14.5579 1.771 14.1667V2.83333Z" strokeWidth="2" strokeLinejoin="round" />
      <path id="Vector_2" d="M6.375 9.5625H10.625" stroke="white" strokeWidth="2" strokeLinecap="round" />
      <path id="Vector_3" d="M8.5 7.4375V11.6875" stroke="white" strokeWidth="2" strokeLinecap="round" />
    </g>
  </svg>
}

const IndexReport = () => {
  return <svg width="1rem" fill="currentColor" height="1.06rem" viewBox="0 0 16 17">
    <g id="Frame">
      <path id="Vector" d="M4.24984 11.6876H1.4165V2.47925H15.5832V11.6876H12.7498H4.24984Z"  />
      <path id="Vector_2" d="M5.6665 7.79175V9.20841" stroke="white"  />
      <path id="Vector" d="M8.5 11.6875V13.8125" stroke="#9EACB8"  />
      <path id="Vector_4" d="M8.5 6.375V9.20833" stroke="white"  />
      <path id="Vector_5" d="M11.3335 4.95825V9.20825" stroke="white"  />
      <path id="Vector" d="M4.25 14.5208H12.75" stroke="#9EACB8"  />
    </g>
  </svg>
}

const TeamManagement = () => {
  return <svg width="1rem" fill="currentColor" height="1.06rem" viewBox="0 0 16 17">
    <g id="组_3766" data-name="组 3766" transform="translate(0 0)">
      <path id="路径_5818"
        data-name="路径 5818"
        transform="translate(0 0.297)" d="M11.5864 13.442V14L0 14V13.442C0 11.3797 4.44918 11.8211 4.44918 9.81372C4.44918 9.63114 4.45497 9.45199 4.45249 9.23857C4.2398 8.99772 3.75483 8.378 3.562 7.532C3.36504 7.43772 3.20199 7.23457 3.09772 6.95085C2.95289 6.55142 2.94213 6.00027 3.22435 5.64285C3.22352 5.63598 3.2227 5.63171 3.22187 5.62484C3.16973 5.12427 3.02904 3.79399 3.80285 2.92228C4.24893 2.42 4.91679 2.024 5.78661 2C6.65641 2.024 7.32429 2.42 7.77037 2.92228C8.54418 3.79399 8.40349 5.12427 8.35052 5.62484C8.34968 5.63171 8.34887 5.63598 8.3472 5.64285C8.62942 6.00027 8.61866 6.55142 8.47383 6.95085C8.37038 7.23457 8.20487 7.4377 8.00789 7.532C7.81505 8.37714 7.32677 8.99772 7.11408 9.23857C7.1116 9.45115 7.10912 9.65257 7.10912 9.83515C7.1091 11.8049 11.5864 11.3634 11.5864 13.442ZM16 12.3249V12.75H12.6088C12.6088 12.75 12.1638 11.973 11.3025 11.544C10.3351 11.0621 8.9647 10.9115 9.49 10.6931C10.0858 10.4453 10.5625 10.1342 10.5625 9.56064C10.5625 9.42156 10.5669 9.28508 10.565 9.12247C10.403 8.93897 10.0335 8.46685 9.88659 7.8223C9.73653 7.75048 9.61233 7.59571 9.53286 7.37956C9.42252 7.07525 9.41433 6.65537 9.62933 6.38306C9.6287 6.37784 9.62806 6.37457 9.62743 6.36935C9.58771 5.988 9.48053 4.97452 10.0701 4.31039C10.4099 3.92772 10.9187 3.62602 11.5814 3.60775C12.2441 3.62602 12.7529 3.92772 13.0927 4.31039C13.6822 4.9745 13.575 5.98798 13.5347 6.36935C13.5341 6.37457 13.5334 6.37784 13.5322 6.38306C13.7472 6.65537 13.739 7.07525 13.6286 7.37956C13.5498 7.59571 13.4237 7.75048 13.2737 7.8223C13.1268 8.46618 12.7548 8.93897 12.5927 9.12247C12.5908 9.28442 12.5889 9.43787 12.5889 9.57696C12.5889 11.0776 16 10.7413 16 12.3249Z" />
    </g>
  </svg>
};

const personal = () => (
  <svg width="1rem" fill="currentColor" height="1.06rem" viewBox="0 0 16 17">
    <g id="组_3766" data-name="组 3766" transform="translate(0 0)">
      <path
        id="减去_1"
        data-name="减去 1"
        d="M13.866,17H2.133A2.115,2.115,0,0,1,0,14.908V2.092A2.115,2.115,0,0,1,2.133,0H13.866A2.115,2.115,0,0,1,16,2.092V14.908A2.116,2.116,0,0,1,13.866,17ZM4.488,11.393a.83.83,0,0,0-.7.914.831.831,0,0,0,.7.914h5.853a.831.831,0,0,0,.7-.914.83.83,0,0,0-.7-.914Zm0-3.81a.831.831,0,0,0-.7.914.831.831,0,0,0,.7.914h7.023a.831.831,0,0,0,.7-.914.831.831,0,0,0-.7-.914Zm0-3.809a.831.831,0,0,0-.7.914.831.831,0,0,0,.7.914H8a.831.831,0,0,0,.7-.914A.831.831,0,0,0,8,3.775Z"
        transform="translate(0 0)"/>
    </g>
  </svg>
);

const Team = () => (
  <svg fill="currentColor" width="1rem" height="1.06rem" viewBox="0 0 16 17">
    <g id="组_3763" data-name="组 3763" transform="translate(0 0)">
      <path
        id="路径_5812"
        data-name="路径 5812"
        d="M292.932,57.124m-4.5,0a4.5,4.5,0,1,0,4.5-4.5A4.5,4.5,0,0,0,288.432,57.124Z"
        transform="translate(-284.592 -52.624)"/>
      <path
        id="路径_5813"
        data-name="路径 5813"
        d="M182.255,516.811c0-1.826-2.021-3.307-4.515-3.307l-4.108,5.547-3.716-5.547c-2.493,0-4.514,1.48-4.514,3.307l-.074,5.238h17Z"
        transform="translate(-165.328 -504.049)"/>
    </g>
  </svg>
);
const Sd = () => (
  <svg width="11" height="12" viewBox="0 0 11 12" fill="currentColor">
    <path d="M1.10292 7.14271H4.1268L3.05607 11.5416L3.06001 11.5424C3.05292 11.5744 3.04268 11.6056 3.04386 11.6391C3.05253 11.8468 3.23689 12.0076 3.45552 11.9998C3.56686 11.995 3.67076 11.9456 3.74113 11.864L10.0268 4.79697C10.0323 4.79176 10.0367 4.7858 10.0418 4.78022L10.0446 4.7765C10.1081 4.70602 10.1419 4.61545 10.1387 4.52264C10.1301 4.32385 9.95769 4.16653 9.74713 4.16531C9.73807 4.16494 9.73334 4.16233 9.72743 4.1627C9.72349 4.1627 9.72113 4.16456 9.71759 4.16456L7.20228 4.14L8.91946 0.597939C8.94499 0.544603 8.95849 0.486687 8.95728 0.428205C8.95318 0.328835 8.90746 0.235077 8.83018 0.16757C8.7529 0.100062 8.65039 0.0643405 8.54522 0.0682673C8.54128 0.0682673 8.53813 0.0705006 8.53419 0.0705006L3.94598 0.0690117C3.94007 0.0690117 3.93534 0.0664062 3.92943 0.0664062C3.77674 0.0725989 3.64162 0.16168 3.58277 0.29495C3.57883 0.303884 3.57528 0.310211 3.57253 0.317283L0.713707 6.6242C0.689835 6.67487 0.67826 6.72994 0.679828 6.78538C0.688495 6.99233 0.872858 7.1535 1.09149 7.14531C1.09583 7.14494 1.09898 7.14308 1.10292 7.14271Z" />
  </svg>
);

export const Analysis: React.FC<any> = (props?: any) => {
  return <Icon component={analysisLogo} {...props} />;
};
export const ReportIcon: React.FC<any> = (props?: any) => {
  return <Icon component={Report} {...props} />;
};
export const PersonalIcon: React.FC<any> = (props?: any) => {
  return <Icon component={personal} {...props} />;
};
export const TeamIcon: React.FC<any> = (props?: any) => {
  return <Icon component={Team} {...props} />;
};
export const TeamManagementIcon: React.FC<any> = (props?: any) => {
  return <Icon component={TeamManagement} {...props} />;
};
export const DataoverViewIcon: React.FC<any> = (props?: any) => {
  return <Icon component={DataoverView} {...props} />;
};
export const IndexReportIcon: React.FC<any> = (props?: any) => {
  return <Icon component={IndexReport} {...props} />;
};
export const ListIcon: React.FC<any> = (props: any) => {
  return <Icon component={LIST} {...props} />;
};
export function Flash(props?: any) {
  return <Icon key={props.index} component={Sd} />;
}
// export default MenuIcon;
