import { Spin, Empty } from 'antd';
import { useEffect, useState } from 'react';

type general = {
  loading?: boolean;
  flag?: boolean;
  size?: 'large' | 'small' | 'default';
  loading_className?: any;
  spinning?: boolean;
  children: JSX.Element;
};
/**
 * @describe 通用模组
 * @function GeneralModule
 */
function GeneralModule(props: general): JSX.Element {
  const [state, setState] = useState<any>({ loading: true, flag: false });
  useEffect(() => {
    setState({ loading: props.loading, flag: props.flag });
  }, [props.flag, props.loading]);
  if (state.loading) {
    return (<div style={{ width: '100%', height: '100%', display: 'flex', alignItems: 'center' }}>
      <Spin
        style={{ flex: '1' }}
        size={props.size ? props.size : 'default'}
        wrapperClassName={props.loading_className}
        spinning={props.spinning ? props.spinning : true}
      ></Spin>
    </div>);
  }
  if (state.flag) {
    return props.children;
  }
  return <Empty></Empty>;
}

export default GeneralModule;
