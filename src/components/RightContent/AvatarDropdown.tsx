import React from 'react';
import { LogoutOutlined } from '@ant-design/icons';
import type { MenuProps} from 'antd';
import { Avatar, Spin } from 'antd';
import { history } from 'umi';
// import { stringify } from 'querystring';
import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';
import defaultPhoto from '@/../public/analysis/account.svg';
import { clearAll, getUser } from '@/utils/storage';
export type GlobalHeaderRightProps = {
  menu?: boolean;
};
// 退出登录，并且将当前的 url 保存
const loginOut = async () => {
  if (window.location.pathname !== '/user/login') {
    clearAll();
    history.push({ pathname: '/user/login' });
  }
};

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = () => {
  const onMenuClick: MenuProps['onClick'] = (event) => {
    const { key } = event;
    if (key === 'logout') {
      // setInitialState({ ...initialState, currentUser: undefined });
      loginOut();
    }
  };

  const loading = (
    <span className={`${styles.action} ${styles.account}`}>
      <Spin size="small" style={{ marginLeft: 8, marginRight: 8 }} />
    </span>
  );

  if (!getUser()) {
    return loading;
  }

  const menuProps = {
    items: [
      {
        label: '退出登录',
        key: 'logout',
        icon: <LogoutOutlined />,
      },
    ],
    onClick: onMenuClick,
  };

  return (
    <HeaderDropdown menu={menuProps}>
      <span className={`${styles.action} ${styles.account}`}>
        <Avatar size="small" className={styles.avatar} src={defaultPhoto} alt="avatar" />
        <span className={`${styles.name} anticon`}>{getUser().username}</span>
      </span>
    </HeaderDropdown>
  );
};

export default AvatarDropdown;
