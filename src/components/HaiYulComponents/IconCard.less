@import '~antd/es/style/themes/default.less';
.titleWrapper {
  position: relative;
  .photo {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 35px;
    vertical-align: top;
    background-repeat: no-repeat;
    background-position: left;
    background-size: 10px;
    border-radius: 15px;
  }
  .span {
    display: block;
    float: left;
    width: 20px;
    height: 21px;
    margin-top: 5px;
  }
  .title {
    display: inline-block;
    width: 7rem;
    margin-top: 5px;
    margin-left: 4px;
    overflow: hidden;
    color: rgba(144, 147, 153, 1);
    // font-weight: bold;
    font-size: 0.875rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
    cursor: pointer;
  }
  .name {
    display: inline-block;
    min-width: 0.1rem;
    height: 20px;
    // margin-left: 2rem;
    color: #06f;
  }
}
