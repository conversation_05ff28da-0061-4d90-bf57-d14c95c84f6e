@import '~antd/es/style/themes/default.less';

.empty {
  position: relative;
  height: 6.43rem;
  margin: 0 0 2.6rem 0;
  background: #fafafa;
  border-radius: 4px;
  opacity: 1;
  .emptyTop {
    height: 4.86rem;
    padding: 1rem 1rem 0 1rem;
    .emptyContentTop {
      position: relative;
      display: flex;
      height: 2rem;
      margin-bottom: 0.5rem;
      .leftPhoto {
        display: inline-block;
        width: 30px;
        height: 30px;
        vertical-align: top;
        background-repeat: no-repeat;
        background-position: left;
        background-size: 16px;
      }
      .text {
        z-index: 99;
        display: inline-block;
        width: 6rem;
        height: 100%;
        padding: 6px 0 0 4px;
        color: #aeb0b3;
        // font-weight: bold;
        font-size: 0.5em;
        text-align: left;
        vertical-align: top;
      }
      .rightPhoto {
        flex: 1;
        margin: -4px 0 0 6px;
        vertical-align: top;
        background-repeat: no-repeat;
        background-position: right;
        background-size: 14px;
      }
    }
    .emptyContentBottom {
      position: relative;
      z-index: 999;
      height: 1.5rem;
      color: #aeb0b3;
      // background-color: rgb(195, 214, 214);
      .proStyle {
        width: 75%;
        margin: 0 8px;
      }
      .score {
        float: right;
        margin-top: -24px;
      }
    }
  }
}