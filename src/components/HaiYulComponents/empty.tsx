import React from 'react';
import styles from './empty.less';
import { Progress } from 'antd';
import emptyIcon from '../../../public/personalreport/empty.svg';
import disabledTop from '../../../public/personalreport/disabledTop.svg';

const EmptyCard: React.FC<any> = () => {
  return (
    <div className={styles.empty}>
      <div className={styles.emptyTop}>
        <div className={styles.emptyContentTop}>
          <span className={styles.leftPhoto} style={{ backgroundImage: `url(${emptyIcon})`, marginLeft: '2px' }}></span>
          <span className={styles.text}>空</span>
        </div>
        <div className={styles.emptyContentBottom}>
          <span>得分</span>
          <Progress
            strokeColor={'#7B98C4'}
            strokeWidth={5}
            className={styles.proStyle}
            showInfo={false}
            percent={100}
          ></Progress>
          <span className={styles.score}>0</span>
        </div>
      </div>
    </div>
  );
};

export default EmptyCard;
