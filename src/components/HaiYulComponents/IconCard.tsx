import React from 'react';
import styles from './IconCard.less';
import type { CardProps } from 'antd/lib/card';
import { Card, Tooltip } from 'antd';
// import IconFont from '../../pages/HealthCheckList/HealthCheckPerson/components/userModelShow/img';
import Icon from '../../components/Icon';
interface IconCardProps extends CardProps {
  icon?: string;
  title?: string;
  iconBackgroundColor?: string;
  titleStyle?: any;
  imgA?: any;
  style?: any;
  iconBorderColor?: string;
  iconBackgroundImage?: string;
  name?: any;
  desc?: any;
}

const IconCard: React.FC<IconCardProps> = (props: any) => {
  const { children } = props;
  return (
    <Card
      bodyStyle={{ padding: '1rem', height: '100%', ...props.bodyStyle }}
      style={{ height: '100%', ...props.style }}
      bordered={props.bordered}
      onClick={props.onClick}>
      <div className={styles.titleWrapper}>
        <span className={styles.photo}>
          <Icon icon={props.icon} iconStyle={{ fontSize: '20px' }}></Icon>
        </span>
        <Tooltip title={`${props.title}:${props.desc}`} >
          <span className={styles.title} style={{ ...props.titleStyle }}>
            {props.title === undefined || null ? '' : props.title}
          </span>
        </Tooltip>
        <span className={styles.name}>{props.name}</span>
      </div>
      {children}
    </Card>
  );
};
IconCard.defaultProps = { bordered: true };

export default IconCard;
