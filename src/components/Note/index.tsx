import React, { useState, useEffect } from 'react';
import { RadarInterpretation } from '../../pages/PersonalList/services';
import styles from './index.less';

const Note: React.FC<any> = () => {
  const [radar, setRadar] = useState<any>([{ l0_name: '', desc: '' }])
  // 雷达图解释接口
  useEffect(() => {
    RadarInterpretation().then((res: any) => {
      setRadar(res)
    })
  }, [])

  return (
    <div className={styles.note}>
      {radar.length > 0 ? radar.map((v: any) => {
        return (<div className={styles.note_grid} key={v.l0_name}>
          <div className={styles.note_name}>{v.l0_name}</div>
          <div className={styles.note_docs}>{v.desc}</div>
        </div>);
      }) : ''}
    </div>
  );
};
export default Note;
