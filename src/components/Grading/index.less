@import '~antd/es/style/themes/default.less';
.icon {
  width: 16px;
  height: 16px;
}
.arrow {
  margin: 0 2px;
  margin-right: 5px;
  color: #939399;
}
.top {
  width: 100%;
  height: 28px;

  .top_left {
    display: flex;
    flex: auto;
    flex-direction: row;
    float: left;
    width: 80%;
    overflow: hidden;
    white-space: nowrap;
  }
}
.table {
  width: 100%;
  margin: auto;
  padding-top: 10px;
  padding-right: 16px;
  background: #fff;
  border-radius: 5px;
  overflow-y: auto;

}
.current_color {
  margin-right: 16px;
  color: #06f;
  border-bottom: 2px solid #06f;
  cursor: pointer;
}
.default_color {
  margin-right: 16px;
  color: #909399;
  cursor: pointer;
}
.spin {
  display: flex;
  align-items: center;
  width: 100%;
  height: 24.5rem;
  :global .ant-spin-spinning {
    flex: 1;
  }
}
.sug {
  color: #939399;
  border: 1px solid #c4c4cc;
  td {
    border: 1px solid rgba(228, 231, 237, 1);
  }
  .sug_tr {
    width: 60px;
    margin: 0 auto;
    border: 1px solid #c4c4cc;
    td :first-child {
      width: 40px;
    }
  }
}
.concern {
  height: 48px;
  label {
    margin-right: 12px;
  }
}
.feed {
  height: 230px;
}
.label {
  float: left;
  margin-right: 12px;
}
textarea {
  border: 1px solid #bdbdc2;
  border-radius: 4px;
  outline: 0;
  resize: none;
}
textarea:hover {
  border: 2px solid #06f;
  border-radius: 4px;
  // animation: test 1s infinite ;
}
textarea:focus {
  border: 2px solid #06f;
  border-radius: 4px;
}
.evenRow {
  background: rgba(255, 255, 255, 1);
}
.oddRow {
  background: rgba(235, 238, 245, 1);
}
.arrows {
  position: relative;
  top: 3px;
  left: 4px;
  display: inline-block;
  width: 16px;
  height: 16px;
  background-repeat: no-repeat;
  background-position: center;
}
.images {
  width: 12px;
  height: 16px;
}
.table {
  :global {
    height: 410px;
    .ant-table-body {
      overflow: auto;
      max-height: 387px;
      &::-webkit-scrollbar {
        width: 4px;
        height: 2px;
      }
      &::-webkit-scrollbar-thumb {
        background: rgba(129, 129, 128, 0.85);
        border-radius: 5px;
      }
      &::-webkit-scrollbar-track {
        background: rgba(0, 4, 40, 0.06);
        border-radius: 0;
      }
    }
    .ant-table-thead > tr > th {
      background-color: rgba(239, 241, 247, 1);
    }
    .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
      width: 0;
    }
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td,
    .ant-table tfoot > tr > th,
    .ant-table tfoot > tr > td {
      padding: 6px;
    }
  }
}
