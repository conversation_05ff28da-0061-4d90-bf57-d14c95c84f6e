import { Popover, Rate, Table } from 'antd';
import { useState, useEffect } from 'react';
import styles from './index.less';
import lt from '@/../public/personalreport/lt.svg';
import gt from '@/../public/personalreport/gt.svg';
import { connect } from '@/.umi/plugin-dva/exports';
import Edit from '@/../public/personalreport/edit.svg';
import { compare, handleKeys } from '@/pages/PersonalReport/service';
import { InfoCircleOutlined } from '@ant-design/icons';
import Content from './content';
import { Flash } from '../MenuIcon';
import FeedBack from '../FeedBack';

const getRowClassName = (_: any, index: number) => {
  let className = '';
  className = index % 2 === 0 ? styles.evenRow : styles.oddRow;
  return className;
};
/**
 * @name l5级别定量
 * @param props
 * @returns
 */
const Low = (props: any) => {
  const { dataOption } = props;
  const [state, setState] = useState<any>({
    back: false,
    keyword: '',
    project: '',
    target: '',
    cancel: false,
    star: 0,
    flag: true,
  });
  const [data, set] = useState<any[]>([]);

  function handleColor(star: number) {
    if (star > 3) {
      return '#F00F00';
    }
    if (star > 2) {
      return '#FFB52B';
    }
    return '#06f';
  }
  function callback(star: number, keyword: string, project?: string, target?: string) {
    setState({ ...state, back: true, keyword, star, project, target });
  }

  const columns: any = [
    {
      title: '检查项目', dataIndex: 'item_name', align: 'left', width: 170,
      render: (_: any, v: any) => { return <div key={v.key}>{v.item_name + v.index_name}</div>; },
    },
    {
      title: '正常值范围', dataIndex: 'reference_value', align: 'center', width: 120,
      // render: (_: any, v: any) => {return <div>{v.reference_value ? v.reference_value : '-'}</div>; },
    },
    {
      title: '结果', dataIndex: 'checkup_res', align: 'left', width: 80,
      render: (_: any, v: any) => {
        switch (v.checkup_res_contrast) {
          case 'normal': { return (<div key={v.key}><span style={{ color: '#909399' }}>{v.checkup_res}</span></div>); }
          case 'gt': {
            return (<div key={v.key}>
              <span style={{ color: '#FF574B' }}>
                {v.checkup_res}
                <img src={gt} style={{ marginLeft: '8px' }} />
              </span>
            </div>);
          }
          case 'lt': {
            return (<div key={v.key}>
              <span style={{ color: '#1677FF' }}>
                {v.checkup_res}
                <img src={lt} style={{ marginLeft: '8px' }} />
              </span>
            </div>);
          }
          default: {
            return (<div key={v.key}>
              <span style={{ color: '#ff7a45' }}>
                {v.checkup_res}
                {/* <img src={exc} className={styles.images} style={{ marginLeft: '8px' }} /> */}
              </span>
            </div>);
          }
        }
      },
    },
    {
      title: () => {
        return (<div>
          <span>关心程度</span>
          <Popover content={Content}>
            <InfoCircleOutlined className={styles.icon} style={{ position: 'relative', left: 4, top: 1 }} />
          </Popover>
        </div>);
      },
      dataIndex: '关爱程度', align: 'left', width: 120,
      render: (level: any) => {
        return (<Rate
          character={<Flash />}
          style={{ color: handleColor(Number(level)), }}
          disabled={true}
          value={Number(level)}
          allowHalf={true}
        ></Rate>);
      },
    },
    {
      title: '反馈', width: 60,
      render: (_: any, v: any) => {
        return (<div onClick={() => { callback(Number(v.关爱程度), v.关键词, v.检查项目, v.检查指标); }} className={styles.call}>
          <img src={Edit} alt="编辑" />
        </div>);
      },
    },
  ];
  useEffect(() => {
    set(dataOption.sort(compare('关爱程度')));
  }, [dataOption]);
  return (
    <div className={styles.table}>
      <Table
        columns={columns}
        key={'index'}
        rowKey={'keys'}
        rowClassName={getRowClassName}
        dataSource={handleKeys(data)}
        pagination={false}
      ></Table>
      <FeedBack
        changeBack={(e: boolean) => { setState({ ...state, back: e }); }}
        data={state}
        back={state.back}
        star={state.star}
      ></FeedBack>
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(Low);
