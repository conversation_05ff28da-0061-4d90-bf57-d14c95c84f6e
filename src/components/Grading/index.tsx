/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import styles from './index.less';
import type { TableColumnsType } from 'antd';
import { Rate, Popover, Modal, Spin, Table } from 'antd';
import Edit from '@/../public/personalreport/edit.svg';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Flash } from '../../components/MenuIcon/index';
import Low from './Low';
import { connect } from '@/.umi/plugin-dva/exports';
import Content from './content';
import { compare, handleKeys } from '@/pages/PersonalReport/service';

function handleColor(star: number) {
  if (star > 3) {
    return '#F00F00';
  }
  if (star > 2) {
    return '#FFB52B';
  }
  return '#06f';
}

type gradingState = { List?: any; changeGrading: any; name: string; };
// 详情页
const Grading: React.FC<gradingState> = (props: any) => {
  const [state, setState] = useState<any>({
    back: false,
    keyword: undefined,
    index_coding: '',
    index_name: '',
    item_name: '',
    item_coding: '',
    cancel: false,
    star: 0,
    flag: true,
  });
  const [loading, setLoad] = useState(true);
  const [list, setList] = useState<any>([]);
  const [color, setColor] = useState('rgba(245, 244, 244, 1)');
  const [options, set_opt] = useState({ current: styles.current_color, default: styles.default_color, flag: true });

  function callback(
    star: number,
    keyword: string,
    item_coding: string,
    item_name: string,
    index_coding: string,
    index_name: string,
  ) {
    setState({ ...state, back: true, keyword, star, item_coding, item_name, index_coding, index_name, });
    const currentColor = handleColor(star);
    setColor(currentColor);
  }
  /**
   * @description Rate组件 鼠标悬浮颜色
   * @param e
   */
  function hover(e: any) {
    if (e > 3) {
      setColor('#F00');
    } else if (e === undefined) {
      hover(state.star);
    } else if (e > 2) {
      setColor('#FFB52B');
    } else {
      setColor('#06f');
    }
  }
  /**
   * @description Rate组件鼠标点击变色
   * @param e
   */
  function handleChange(e: any) {
    setColor(handleColor(e));
    setState({ ...state, star: e });
  }
  /**
   * @description 确定和关闭模态框触发
   * @param params
   */
  function handleFinish(params: string) {
    // console.log(state);
    return;
    if (params === 'finish') {
      setState({
        ...state,
        back: false,
      });
    } else if (params === 'cancel') {
      setState({
        back: false,
      });
      setColor('rgba(244,244,244,1)');
    }
  }
  // 点击返回事件
  function handleClick() {
    setList([]);
    props.changeGrading(true);
  }
  /**
   * @description 点击切换组件
   */
  function changIndex(index: any) {
    if (index === '0') {
      set_opt({ current: styles.current_color, default: styles.default_color, flag: true });
      return;
    }
    set_opt({ current: styles.default_color, default: styles.current_color, flag: false });
  }
  const columns: TableColumnsType<any> | undefined = [
    {
      title: '项目名称', dataIndex: '项目名称',
      render: (value: string, data: any) => {
        return (<div>
          {value}
          {data.指标名称}
        </div>);
      },
    },
    { title: '关键词', dataIndex: '关键词' },
    {
      title: () => {
        return (<div>
          <span>关心程度</span>
          <Popover content={Content}>
            <InfoCircleOutlined className={styles.icon} style={{ position: 'relative', left: 4, top: 1 }} />
          </Popover>
        </div>);
      },
      dataIndex: '关爱程度', width: 140,
      render: (star: any) => {
        return (<Rate
          character={<Flash />}
          style={{ color: handleColor(Number(star)), }}
          // color: star > 3 ? '#F00' : '#06F',
          disabled={true}
          value={Number(star)}
          allowHalf={true}
        ></Rate>);
      },
    },
    {
      title: '反馈', width: 60,
      render: (_: any, v: any) => {
        return (
          <div
            onClick={() => { callback(Number(v.关爱程度), v.关键词, v.项目编码, v.项目名称, v.指标编码, v.指标名称); }}
            className={styles.call}>
            <img src={Edit} alt="编辑" />
          </div>
        );
      },
    },
  ];

  const getRowClassName = (_: any, index: number) => {
    let className = '';
    className = index % 2 === 0 ? styles.evenRow : styles.oddRow;
    return className;
  };

  useEffect(() => {
    setTimeout(() => {
      setLoad(false);
    }, 500);

    if (props.List.length > 0) {
      setList(props.List.sort(compare('关爱程度')));
    }
    return () => {
      setList([]);
      setLoad(true);
    };
  }, [props.List, props.load, props]);

  return (
    <div>
      <div className={styles.top}>
        <div className={styles.top_left}>
          <span onClick={() => changIndex('0')} className={options.current}>
            {props.name}报告类检查结果
          </span>
          <span onClick={() => changIndex('1')} className={options.default}>
            {props.name}实验室检验结果
          </span>
        </div>
        <span onClick={handleClick} style={{ float: 'right', marginRight: 8, cursor: 'pointer' }}>
          返回上一级
        </span>
      </div>
      {loading ? (<div className={styles.spin}>
        <Spin size={'large'} spinning={loading}></Spin>
      </div>) : options.flag ? (<div className={styles.table}>
        <Table
          columns={columns}
          key={'index'}
          rowKey={'keys'}
          rowClassName={getRowClassName}
          dataSource={handleKeys(list)}
          pagination={false} />
      </div>) : (<Low />)}
      <Modal
        onCancel={() => { handleFinish('cancel'); }}
        title={'反馈'}
        visible={state.back}
        onOk={() => { handleFinish('finish'); }}>
        <div className={styles.concern}>
          <label>关心程度:</label>
          <Rate
            character={<Flash />}
            style={{ color, }}
            onHoverChange={(e) => hover(e)}
            onChange={(e) => { handleChange(e); }}
            allowHalf={true}
            // defaultValue={state.star}
            value={state.star}
          ></Rate>
        </div>
        <div className={styles.feed}>
          <label className={styles.label} htmlFor={'fb'}>
            反馈原因:
          </label>
          <textarea
            name="feedback"
            placeholder={'请输入反馈原因'}
            id="fb"
            required={true}
            maxLength={50}
            spellCheck={true}
            cols={50}
            rows={10}
            autoComplete={'on'}
          ></textarea>
        </div>
      </Modal>
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(Grading);
