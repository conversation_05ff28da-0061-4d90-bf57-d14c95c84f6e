import styles from './index.less';
import React from 'react';
/**
 * @description 等级介绍
 */
const level = [
  { lev: '0', sug: '机体生理功能正常。' },
  { lev: '1', sug: '机体生理功能轻微改变，无需治疗和观察' },
  { lev: '2', sug: '机体生理功能代偿性变化，需随访观察。' },
  { lev: '3', sug: '机体生理功能出现异常，需进行治疗。' },
  { lev: '4', sug: '机体生理功能严重异常，需立即就医。' },
  { lev: '5', sug: '机体生理功能完全丧失。' },
];

const Content: React.FC<any> = () => (
  <div>
    <table className={styles.sug}>
      <thead>
        <tr>
          <td>等级</td>
          <td>建议</td>
        </tr>
      </thead>
      <tbody>
        {level.map((v: any) => {
          return (<tr key={v.lev} className={styles.sug_tr}>
            <td>{v.lev}</td>
            <td>{v.sug}</td>
          </tr>);
        })}
      </tbody>
    </table>
  </div>
);
export default Content;
