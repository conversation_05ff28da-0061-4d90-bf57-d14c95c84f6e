import { DoubleLeftOutlined } from '@ant-design/icons';
import React from 'react';
import { history } from 'umi';
import styles from './index.less';

const Header: React.FC<any> = (props: any) => {
  function handleHistory() {
    if (props.flag === 'personal') {
      history.push({ pathname: '/IndexReport/personal' });
    } else {
      history.push({ pathname: '/IndexReport/team' });
    }
  }
  return (
    <div className={styles.header}>
      <div onClick={handleHistory} className={styles.header_click}>
        <DoubleLeftOutlined />
        <span>{props.title} </span>
      </div>
    </div>
  );
};
export default Header;
