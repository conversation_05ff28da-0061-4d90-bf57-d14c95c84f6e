import { useState } from 'react';
import { getHospitalList } from '@/pages/AppointmentManage/AppointmentRecords/services';
import type { HospitalItem } from '@/pages/AppointmentManage/AppointmentRecords/type';

export default function useHospitalModel() {
  const [hospitalList, setHospitalList] = useState<HospitalItem[]>([]);

  const initOptions = () => {
    getHospitalList().then((res) => {
      setHospitalList(res.data);
    });
  };

  const getHospitalOptionsByKey = (key: keyof HospitalItem) => {
    if (hospitalList.length < 1) {
      initOptions();
    }
    return hospitalList.map((item) => ({label: item.name, value: item[key]}));
  };

  return { getHospitalOptionsByKey, hospitalList };
}
