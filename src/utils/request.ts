// import { request } from 'umi';
/* eslint-disable consistent-return */
/** Request 网络请求工具 更详细的 api 文档: https://github.com/umijs/umi-request */
import {extend} from 'umi-request';
import {message, notification} from 'antd';
// import axios from 'axios';
import {getToken} from '@/utils/storage';
// import { errorReport } from '@/utils/utils';
// import proSettings from '../../config/defaultSettings';
const codeMessage: { [status: number]: string } = {
  // 200: '服务器成功返回请求的数据。',
  // 201: '新建或修改数据成功。',
  // 202: '一个请求已经进入后台排队（异步任务）。',
  // 204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};
// 网络异常提示信息标志位
let networkFlag = true;
/** 异常处理程序 */
const errorHandler = (error: { response: Response; data: any; request: any }) => {
  /*const { response } = error;
  // 异常响应，报告错误
  if (!response) {
    if (networkFlag) {
      networkFlag = false;
      notification.error({ description: '您的网络发生异常，无法连接服务器', message: '网络异常' });
      setTimeout(() => {
        networkFlag = true;
      }, 1000);
    }
  }else {
    /!**
     * @listens 504-连接超时
     *!/
    if (response.status === 504) {
      notification.error({ description: '您的网络发生异常，无法连接服务器', message: '网络异常' });
    }else {
    }
  }*/
  throw error;
};

/** 配置request请求时的默认参数 */
const request = extend({
  errorHandler, // 默认错误处理
  credentials: 'include', // 默认请求是否带上cookie
});

request.interceptors.request.use((url: any, options: any) => {
  if (getToken()) {
    return { url, options: { ...options, headers: { Authorization: `Bearer ${getToken()}` } } };
  }
  return { url, options };
});

// 登录失效提示信息标志位
let hadShowError = false
request.interceptors.response.use(async (res: any, options): Promise<any> => {
  if (res && res.status === 401) {
    let errorResponse: { detail?: any; messages?: any[] } = {};
    await res.json().then((r: any) => {
      errorResponse = r;
    });
    if (
      (errorResponse?.messages && errorResponse?.messages[0].token_type === 'access') ||
      errorResponse.detail === 'Token is invalid or expired'
    ) {
      if (!hadShowError)
        notification.error({ message: `会话已超时，请重新登录` });
      hadShowError = true
    } else {
      return res;
    }
    const wait = () => new Promise((resolve) => setTimeout(resolve, 1000));
    await wait()
    hadShowError = false
    window.location.href = '/user/login';
    return {};
  }
  if (res) {
    let msg = '操作失败'
    if (codeMessage[res.status]){
      msg = codeMessage[res.status]
    }else if (options.responseType === 'blob') {
      return res
    } else {
      const resData = await res.clone().json()
      if (resData.code && resData.code !== 2000){
        msg = resData?.message || resData?.msg
      }else {
        return res
      }
    }
    if (networkFlag) {
      networkFlag = false;
      message.error(msg.toString())
      setTimeout(() => {
        networkFlag = true;
      }, 1000);
    }
    throw await res.clone().json();
  }
  return res;
});

export default request;
