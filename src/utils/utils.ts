import moment from 'moment';

const reg =
  /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;
export const isUrl = (path: string): boolean => reg.test(path);
export const isAntDesignPro = (): boolean => {
  if (ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION === 'site') {
    return true;
  }
  return window.location.hostname === 'preview.pro.ant.design';
};
// 给官方演示站点用，用于关闭真实开发环境不需要使用的特性
export const isAntDesignProOrDev = (): boolean => {
  const { NODE_ENV } = process.env;
  if (NODE_ENV === 'development') {
    return true;
  }
  return isAntDesignPro();
};

export const Judge = () => {
  const userAgentInfo = navigator.userAgent;
  const Agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod']; //  判断是否是这几个系统
  let isPC = true;
  for (let i = 0; i < Agents.length; i += 1) {
    if (userAgentInfo.indexOf(Agents[i]) > 0) {
      isPC = false;
      break;
    }
  }
  return isPC;
};

export const setTableRowClass = (_record: any, index: number) => {
  let className = '';
  if (index % 2 !== 0) {
    className = 'divide-table-row';
  }
  return className;
};

export function validateIDCard(idCard: string): boolean {
  const idCardPattern = /^\d{15}$|^\d{17}[\dXx]$/;
  return idCardPattern.test(idCard);
}

export function validatePhoneNumber(phoneNumber: string): boolean {
  // 手机号码校验正则表达式（适用于中国大陆）
  const phoneNumberPattern = /^1[3-9]\d{9}$/;
  return phoneNumberPattern.test(phoneNumber);
}

/**
 * 从身份证号中提取性别和年龄
 * @param idCard
 */
export function extractInfoFromID(idCard: string | number): {
  gender: string;
  genderCode: string;
  age: number;
  birthday: string;
} {
  const id = idCard.toString();
  // 提取性别信息
  const genderDigit = parseInt(id.toString().charAt(16), 10);
  let genderCode, gender;
  if (genderDigit % 2 === 0) {
    genderCode = 'female';
    gender = '女';
  } else {
    genderCode = 'male';
    gender = '男';
  }

  // 提取出生日期
  const birthYear = parseInt(id.substring(6, 10), 10);
  const birthMonth = parseInt(id.substring(10, 12), 10);
  const birthDay = parseInt(id.substring(12, 14), 10);

  // 计算年龄
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  const currentDay = currentDate.getDate();

  let age = currentYear - birthYear;
  if (currentMonth < birthMonth || (currentMonth === birthMonth && currentDay < birthDay)) {
    age--;
  }

  return {
    gender,
    genderCode,
    age,
    birthday: `${id.substring(6, 10)}-${id.substring(10, 12)}-${id.substring(12, 14)}`,
  };
}

export function calculateAge(birthday: string): number {
  const today = moment();
  const birthDate = moment(birthday, 'YYYY-MM-DD');
  return today.diff(birthDate, 'years');
}

export function createCaseInsensitiveRegex(search: string) {
  return new RegExp(search, 'i');
}
