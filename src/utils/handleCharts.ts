/**
 * @function echarts__Y轴数据范围自动处理
 * @param {接收数据值的形式} params
 * @param {至少有一组有效数据} data1
 * @param {可选数据} data2
 * @param {可选数据} data3
 * @returns 返回数组[min: 最小值,max: 最大值]
 */
function range(params: string, data1: any, data2?: any, data3?: any) {
  let min: any;
  let min1: any;
  let min2: any;
  let min3: any;
  let max: any;
  let max1: any;
  let max2: any;
  let max3: any;

  if (data1) {
    const arr1: any = data1.map((v: any) => { return v[params]; });
    [min1] = arr1.sort((a: any, b: any) => a - b);
    max1 = arr1.sort((a: any, b: any) => a - b)[arr1.length - 1];
  }
  if (data2) {
    const arr2: any = data2.map((v: any) => { return v[params]; });
    [min2] = arr2.sort((a: any, b: any) => a - b);
    max2 = arr2.sort((a: any, b: any) => a - b)[arr2.length - 1];
  }
  if (data3) {
    const arr3: any = data3.map((v: any) => { return v[params]; });
    [min3] = arr3.sort((a: any, b: any) => a - b);
    max3 = arr3.sort((a: any, b: any) => a - b)[arr3.length - 1];
  }
  if (data1 && data2 && data3) {
    if (min1 < min2 && min1 < min3) {
      min = min1;
    } else if (min2 < min1 && min2 < min3) {
      min = min2;
    } else {
      min = min3;
    }
    if (max1 > max2 && max2 > max3) {
      max = max1;
    } else if (max2 > max3 && max2 > max1) {
      max = max2;
    } else {
      max = max3;
    }
  } else if (data2 && data2) {
    min = min1 < min2 ? min1 : min2;
    max = max1 > max2 ? max1 : max2;
  } else {
    min = min1;
    max = max1;
  }

  let currentMin = Math.floor((min - max * 0.1) / 10) * 10;
  let currentMax = Math.round((max * 0.1 + (max - 0)) * 0.1) * 10;
  if (currentMax >= 100) {
    currentMax = 100;
  }
  if (currentMin <= 0) {
    currentMin = 0;
  }
  return [currentMin, currentMax];
}
/**
 * @name 预加载echarts前loading
 */
const loadingOption = {
  text: 'loading',
  textColor: '#06F',
  color: ' #06F',
  maskColor: 'rgba(255, 255, 255, 0.8)',
  zlevel: 0,
};
/**
 * @description 返回数据异常|无数据组件
 */
const emptyOption = {
  title: {
    text: '暂无数据',
    x: 'center',
    y: 'center',
    textStyle: { color: '#06F', fontWeight: 'normal', fontSize: 24 },
  },
};

export { loadingOption, emptyOption, range };
