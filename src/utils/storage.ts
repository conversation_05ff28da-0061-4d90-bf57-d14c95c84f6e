const KEY_SET = {
  TOKEN_KEY: 'HAIYUL-TOKEN-NEW',
  AUTHORITY_KEY: 'HAIYUL-AUTHORITY-NEW',
  ACCOUNT_KEY: 'HAIYUL-ACCOUNT-NEW',
  REFRESH_KEY: 'HAIYUL-REFRESH-NEW',
};

export function setAccount(account: string) {
  window.localStorage.setItem(KEY_SET.ACCOUNT_KEY, account);
}

export function getAccount() {
  return localStorage.getItem(KEY_SET.ACCOUNT_KEY);
}
export function clearAccount() {
  localStorage.removeItem(KEY_SET.ACCOUNT_KEY);
}

export function setToken(token: string) {
  window.localStorage.setItem(KEY_SET.TOKEN_KEY, token);
}

export function getToken() {
  return localStorage.getItem(KEY_SET.TOKEN_KEY);
}

export function clearToken() {
  localStorage.removeItem(KEY_SET.TOKEN_KEY);
}

export function setRefresh(refresh: string) {
  localStorage.setItem(KEY_SET.REFRESH_KEY, refresh);
}

export function getRefresh() {
  return localStorage.getItem(KEY_SET.REFRESH_KEY);
}

export function clearRefresh() {
  localStorage.removeItem(KEY_SET.REFRESH_KEY);
}

export function setUser(total: any) {
  const user = JSON.stringify({
    user_id: total.user_id,
    username: total.username,
    institution_code: total.institution_code,
  });
  localStorage.setItem('user', user);
}

export const getUser = () => {
  const user: any = localStorage.getItem('user');
  return JSON.parse(user);
};

export const clearUser = () => {
  localStorage.removeItem('user');
};

export const clearAll = () => {
  localStorage.clear();
};
