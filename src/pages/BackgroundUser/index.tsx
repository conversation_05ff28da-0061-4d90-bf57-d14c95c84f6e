import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Button, Form, Input, Modal, Select, Table, DatePicker, message } from 'antd';
import {
  BackgroundUserAdd,
  BackgroundUserDel,
  BackgroundUserList,
  BackgroundUserPwd,
} from '@/services/backgroundUser';
import useInstitution from '@/hooks/useInstitution';
import { useModel } from '@@/plugin-model/useModel';

const formItemLayout = {
  labelCol: { xs: { span: 20 }, sm: { span: 6 } },
  wrapperCol: { xs: { span: 15 }, sm: { span: 20 } },
};
const BackgroundUser: React.FC = () => {
  const { isInstitution, institutionCode } = useInstitution();
  const { getHospitalOptionsByKey } = useModel('useHospitalModel');
  // 获取角色
  const groups = localStorage.getItem('groups');
  // 筛选表单
  const [form] = Form.useForm();
  // 编辑表单
  const [editForm] = Form.useForm();
  // 新增表单
  const [addForm] = Form.useForm();
  // 搜索信息
  const [searchDate, setSearchDate] = useState('');
  // 分页器 数据
  const [paginationData, setPaginationData] = useState({
    pageSize: 14, // 每页多少条
    total: 0, // 列表长度
    current: 1, // 当前页
    data: [], // 列表数据
  });
  const [currentpage, setcurrentpage] = useState(1);
  // 重置密码的账户id
  const [UserId, setUserId] = useState('');
  // 弹框显示控制
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 弹框名称
  const [modalTitle, setModalTitle] = useState('');
  // 列表所需参数
  const columns: any = [
    { title: '编号', dataIndex: 'id', width: '10%' },
    { title: '姓名', dataIndex: 'name', width: '10%' },
    { title: '用户名', dataIndex: 'username', width: '11%' },
    { title: '角色', dataIndex: 'role', width: '12%' },
    {
      title: '创建时间',
      dataIndex: 'register_time',
      width: '12%',
      render: (target: any) => {
        return <span>{target.split('T')[0]}</span>;
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: '15%',
      align: 'center',
      render: (_: any, e: any) => {
        return (
          <div key={e.commercial_code} className={styles.operation}>
            <a
              style={{ color: groups == 'super_admin' ? '#06f' : '#ccc' }}
              onClick={() => {
                if (groups == 'super_admin') {
                  editForm.resetFields();
                  setModalTitle('重置密码');
                  setIsModalOpen(true);
                  setUserId(e.id);
                } else {
                  message.info('无操作权限！');
                }
              }}
            >
              重置密码
            </a>
            <a
              style={{ color: groups == 'super_admin' ? '#06f' : '#ccc' }}
              onClick={() => {
                if (groups == 'super_admin') {
                  setModalTitle('警告');
                  setIsModalOpen(true);
                  setUserId(e.id);
                } else {
                  message.info('无操作权限！');
                }
              }}
            >
              删除
            </a>
          </div>
        );
      },
    },
  ];
  // 调用列表数据
  const BackgroundUserListFun = (current: any, date: any) => {
    const obj = { register_time: date, page: current, num: paginationData.pageSize };
    BackgroundUserList({ ...obj }).then((res) => {
      setPaginationData({
        ...paginationData,
        pageSize: paginationData.pageSize,
        total: res.count,
        data: res.results,
      });
    });
  };
  useEffect(() => {
    BackgroundUserListFun('1', '');
  }, []);
  return (
    <div className={styles.BackgroundUser}>
      <Form
        onFinish={(e) => {
          // 获取日期
          let date = e.create_time
            ? e.create_time._d.toLocaleDateString().replaceAll('/', '-')
            : '';
          // 年份为单数前面加0
          if (date && date.split('-')[1].length == 1) {
            date = date.split('-');
            date[1] = '0' + date[1];
            date = date.toString().replaceAll(',', '-');
          }
          // 月份为单数前面加0
          if (date && date.split('-')[2].length == 1) {
            date = date.split('-');
            date[2] = '0' + date[2];
            date = date.toString().replaceAll(',', '-');
          }
          BackgroundUserListFun(paginationData.current, date);
          setSearchDate(date);
        }}
        form={form}
      >
        <div className={styles.ListTitle}>
          <div className={styles.titleLeft}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <Form.Item name={'create_time'} label={'创建时间:'}>
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </div>
            </div>
          </div>
          <div className={styles.titleRight}>
            <div className={styles.button}>
              <Button type="primary" className={styles.choose} htmlType="submit">
                筛选
              </Button>
              <Button
                className={styles.reset}
                onClick={() => {
                  form.resetFields();
                  BackgroundUserListFun(currentpage, '');
                  setSearchDate('');
                }}
              >
                重置
              </Button>
              <Button
                className={styles.reset}
                disabled={groups == 'super_admin' ? false : true}
                onClick={() => {
                  addForm.resetFields();
                  setModalTitle('新增用户');
                  setIsModalOpen(true);
                }}
              >
                新增
              </Button>
            </div>
          </div>
        </div>
      </Form>
      <div className={styles.listContent}>
        <Modal
          title={
            <nav className={styles.ModalHeader}>
              <span></span>
              <b>{modalTitle}</b>
            </nav>
          }
          open={isModalOpen}
          closeIcon={true}
          centered={true}
          width={modalTitle == '警告' ? '400px' : '480px'}
          footer={null}
        >
          {/* 删除框 */}
          {modalTitle == '警告' && (
            <div>
              <div
                style={{
                  display: 'inline-block',
                  textAlign: 'center',
                  width: '100%',
                  margin: '30px 0',
                }}
              >
                <img
                  style={{ width: '36px', height: '36px', marginBottom: '27px' }}
                  src={require('../../../public/registereuser/deteleimg.png')}
                  alt=""
                />
                <p style={{ fontSize: '14px' }}>确定要删除用户！</p>
              </div>
              <nav>
                <Button onClick={() => setIsModalOpen(false)}> 取消 </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    BackgroundUserDel({ backend_user_ids: [UserId] }).then((res) => {
                      if (res.code == 2004) {
                        message.success('删除成功！');
                        setIsModalOpen(false);
                        BackgroundUserListFun(paginationData.current, searchDate);
                      }
                    });
                  }}
                >
                  确定
                </Button>
              </nav>
            </div>
          )}
          {/* 新增框 */}
          {modalTitle == '新增用户' && (
            <Form
              name="nest-messages"
              form={addForm}
              {...formItemLayout}
              labelAlign={'right'}
              autoComplete="off"
              requiredMark={true}
              onFinish={(values) => {
                delete values.confirm_password;
                BackgroundUserAdd({institution_code: institutionCode, ...values }).then((res) => {
                  if (res.code == 2000) {
                    message.success('新增用户成功！');
                    BackgroundUserListFun('1', '');
                    setIsModalOpen(false);
                  } else if (res.message.indexOf('已存在一位使用该名字的用户') !== -1) {
                    message.error('账号重复，请修改账号！');
                  }
                });
              }}
            >
              <Form.Item
                name={'username'}
                label={
                  <span>
                    账<span style={{ opacity: 0 }}>账号</span>号
                  </span>
                }
                rules={[{ required: true, message: '请输入账号' }]}
              >
                <Input maxLength={20} placeholder="请输入账号" />
              </Form.Item>
              <Form.Item
                name={'password'}
                label={
                  <span>
                    密<span style={{ opacity: 0 }}>密码</span>码
                  </span>
                }
                rules={[
                  { required: true, message: '' },
                  {
                    validator: (_, value) => {
                      const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{8,}$/;
                      if (!passwordRegex.test(value)) {
                        return Promise.reject('密码必须包含字母和数字，长度至少为8个字符。');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input maxLength={20} placeholder={'请输入密码'} style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item
                name={'confirm_password'}
                label="确认密码"
                rules={[
                  { required: true, message: '请输入密码' },
                  {
                    validator: (_, value) => {
                      if (value && value !== addForm.getFieldValue('password')) {
                        return Promise.reject('两次输入的密码不匹配。');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input maxLength={20} placeholder={'再次输入密码'} />
              </Form.Item>
              {!isInstitution && (
                <Form.Item
                  name="institution_code"
                  label="所属机构"
                  rules={[{ required: true, message: '请选择所属机构' }]}
                >
                  <Select
                    placeholder="请选择所属机构"
                    options={getHospitalOptionsByKey('institution_code')}
                  />
                </Form.Item>
              )}
              <Form.Item
                name="role"
                label={
                  <span>
                    角<span style={{ opacity: 0 }}>角色</span>色
                  </span>
                }
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select
                  placeholder="请选择角色"
                  options={[
                    { value: 'admin', label: '管理员'},
                    { value: 'normal', label: '普通用户'},
                  ]}
                />
              </Form.Item>
              <Form.Item
                name={'name'}
                label="账号持有人"
                rules={[
                  { required: false, message: '请输入账号持有人' },
                  {
                    pattern: /^[\u4e00-\u9fa5]+$/,
                    message: '请输入正确的名字（仅能输入中文）',
                  },
                ]}
              >
                <Input maxLength={20} placeholder={'请输入持有人'} style={{ width: '100%' }} />
              </Form.Item>
              <nav>
                <Form.Item>
                  <Button onClick={() => setIsModalOpen(false)}> 取消 </Button>
                </Form.Item>
                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    {' '}
                    确定{' '}
                  </Button>
                </Form.Item>
              </nav>
            </Form>
          )}
          {/* 重置框 */}
          {modalTitle == '重置密码' && (
            <Form
              name="nest-messages"
              form={editForm}
              labelAlign={'right'}
              autoComplete="off"
              onFinish={(values) => {
                const obj = { new_password: values.password, backend_user_id: Number(UserId) };
                BackgroundUserPwd({ ...obj }).then((res) => {
                  if (res.code == 2000) {
                    setIsModalOpen(false);
                    message.success(res.message);
                  }
                });
              }}
              style={{ width: '100%' }}
            >
              <Form.Item
                name={'password'}
                labelCol={{ span: 6 }}
                label="新密码"
                rules={[
                  { required: true, message: '' },
                  {
                    validator: (_, value) => {
                      const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d).{8,}$/;
                      if (!passwordRegex.test(value)) {
                        return Promise.reject('密码必须包含字母和数字，长度至少为8个字符。');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input maxLength={20} placeholder="请输入新密码" style={{ width: '100%' }} />
              </Form.Item>
              <Form.Item
                name={'newpassword'}
                labelCol={{ span: 6 }}
                label="确认新密码"
                rules={[
                  { required: true, message: '请输入新密码' },
                  {
                    validator: (_, value) => {
                      if (value && value !== editForm.getFieldValue('password')) {
                        return Promise.reject('两次输入的密码不匹配。');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input maxLength={20} placeholder={'请再次输入新密码'} style={{ width: '100%' }} />
              </Form.Item>
              <nav>
                <Form.Item>
                  <Button onClick={() => setIsModalOpen(false)}> 取消 </Button>
                </Form.Item>
                <Form.Item>
                  <Button type="primary" htmlType="submit">
                    {' '}
                    确定{' '}
                  </Button>
                </Form.Item>
              </nav>
            </Form>
          )}
        </Modal>
        {/* 列表 */}
        <Table
          columns={columns}
          id={styles.role}
          rowKey="id"
          pagination={{
            style: { position: 'absolute', bottom: '1rem', left: '36%' },
            hideOnSinglePage: true,
            showSizeChanger: false,
            total: paginationData.total,
            pageSize: paginationData.pageSize,
            onChange: (e) => {
              setcurrentpage(e);
              BackgroundUserListFun(e, searchDate);
            },
          }}
          dataSource={paginationData.data}
          onChange={(e) => {}}
          style={{ minHeight: 520 }}
          rowClassName={(_, i): string => {
            if (i % 2 === 0) {
              return styles.colorOne;
            }
            return styles.colorTwo;
          }}
        />
      </div>
    </div>
  );
};

export default BackgroundUser;
