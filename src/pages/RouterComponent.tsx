import NotFound from '@/pages/404';
import { history } from 'umi';
import { useEffect, useState } from 'react';
import { Spin } from 'antd';

function RouterComponent() {
  const groups = localStorage.getItem('groups');
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    if (groups === 'admin') {
      history.push('/Dataoverview/');
    } else if (groups === 'company_user') {
      history.push('/IndexReport/team');
    } else {
      history.push('/Dataoverview');
    }
    setLoading(false);
    return () => {};
  }, [groups]);

  if (loading) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', width: '100%', height: '100%' }}>
        <Spin style={{ flex: 1 }}></Spin>
      </div>
    );
  }
  return <NotFound></NotFound>;
}

export default RouterComponent;
