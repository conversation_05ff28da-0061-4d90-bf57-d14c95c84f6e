export type QueryPaymentListParams = {
  create_datetime?: string;
  pay_method?: string;
  status?: string;
  number?: string;
} & API.PaginatedParams;

export enum PaymentStatusEnum {
  成功 = '成功',
  失败 = '失败',
}

export type PaymentListItem = {
  id: number;
  create_datetime: string;
  update_datetime: string;
  order_info: {
    id: number;
    create_datetime: string;
    update_datetime: string;
    payment_number: string;
    serial_number: string;
  };
  appointment_number: string;
  identify_number: string;
  money: number;
  status: PaymentStatusEnum;
  pay_method: string;
  order: number;
};

export type PaymentDetail = PaymentListItem & {
  user_info: {
    id: number;
    name: string;
    gender: number;
    phone: string;
    identify_number: string;
    age: number;
  };
};
