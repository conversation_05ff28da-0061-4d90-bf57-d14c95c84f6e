import React, { useRef, useState } from 'react';
import type { ActionType, ListToolBarProps, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import type { SearchConfig } from '@ant-design/pro-table/lib/components/Form/FormRender';
import { Button, DatePicker, Descriptions, Drawer, Space, Spin, Typography } from 'antd';
import moment from 'moment';
import { getPaymentDetail, getPaymentList } from '@/pages/PaymentManage/PaymentHistory/services';
import type {
  PaymentDetail,
  PaymentListItem,
  QueryPaymentListParams,
} from '@/pages/PaymentManage/PaymentHistory/type';
import { PaymentStatusEnum } from '@/pages/PaymentManage/PaymentHistory/type.d';

const { Title, Text } = Typography;

const PaymentHistory: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const tableRef = useRef<ActionType>();
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [paymentInfo, setPaymentInfo] = useState<PaymentDetail>();

  const initTableData = async (
    params: QueryPaymentListParams & { current: number; pageSize: number },
  ) => {
    const { current, pageSize, create_datetime, ...restParams } = params;
    const res = await getPaymentList({
      ...restParams,
      create_datetime: create_datetime ? moment(create_datetime).format('YYYY-MM-DD') : undefined,
      page_size: pageSize,
      page_num: current,
    });
    return { data: res.data, success: true, total: res.total };
  };

  const showDetailFunc = (info: PaymentListItem) => {
    setShowDetail(true);
    setLoading(true);
    getPaymentDetail(info.id)
      .then((res) => {
        setPaymentInfo(res.data);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const searchConfig: SearchConfig = {
    labelWidth: 'auto',
    span: 4,
    optionRender: (config) => [
      <Button key="submit" type="primary" onClick={() => config?.form?.submit()}>
        筛选
      </Button>,
      <Button key="clear" onClick={() => tableRef.current?.reset!()}>
        重置
      </Button>,
    ],
  };

  const toolBarConfig: ListToolBarProps = {
    settings: [],
  };

  const columns: ProColumns<PaymentListItem>[] = [
    {
      title: '支付业务编号',
      dataIndex: 'payment_number',
      align: 'center',
      render: (_, record) => record.order_info.payment_number,
    },
    {
      title: '交易流水号',
      dataIndex: 'name',
      align: 'center',
      hideInSearch: true,
      render: (_, record) => record.order_info.serial_number,
    },
    {
      title: '交易时间',
      dataIndex: 'create_datetime',
      align: 'center',
      renderFormItem: () => <DatePicker format="YYYY-MM-DD" />,
    },
    {
      title: '支付方式',
      dataIndex: 'pay_method',
      align: 'center',
    },
    {
      title: '预约编号',
      dataIndex: 'appointment_number',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '人员编号',
      dataIndex: 'identify_number',
      align: 'center',
      hideInSearch: true,
      render: (_, record) => {
        return record.identify_number.slice(0, 4) + '****' + record.identify_number.slice(-4);
      },
    },
    {
      title: '金额',
      dataIndex: 'money',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      valueEnum: PaymentStatusEnum,
    },
    {
      title: '操作',
      hideInSearch: true,
      dataIndex: 'operation',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button type="link" size="small" onClick={() => showDetailFunc(record)}>
            查看
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="base-content">
      <ProTable
        rowKey="id"
        actionRef={tableRef}
        search={searchConfig}
        columns={columns}
        toolbar={toolBarConfig}
        request={initTableData}
        tableStyle={{ paddingTop: '1.5rem' }}
        pagination={{ defaultPageSize: 10 }}
      />

      <Drawer
        title={<span style={{ fontSize: 16 }}>支付详情</span>}
        placement="right"
        onClose={() => setShowDetail(false)}
        open={showDetail}
      >
        <Spin spinning={loading}>
          <Space direction="vertical" size={0} style={{ marginBottom: '20px' }}>
            <Title level={3} style={{ marginBottom: '5px' }}>
              {paymentInfo?.user_info.name}
              <span style={{ marginLeft: '15px', fontSize: '15px' }}>
                {paymentInfo?.user_info.age}岁
              </span>
              <span style={{ marginLeft: '15px', fontSize: '15px' }}>
                {paymentInfo?.user_info.gender}
              </span>
            </Title>
            <Text type="secondary">身份证号：{paymentInfo?.user_info.identify_number}</Text>
            <Text type="secondary">手机号：{paymentInfo?.user_info.phone}</Text>
          </Space>

          <Descriptions title="支付信息" column={1}>
            <Descriptions.Item label="支付总额">￥{paymentInfo?.money}</Descriptions.Item>
            <Descriptions.Item label="支付业务编号">
              {paymentInfo?.order_info.payment_number}
            </Descriptions.Item>
            <Descriptions.Item label="业务编号">
              {paymentInfo?.appointment_number}
            </Descriptions.Item>
            <Descriptions.Item label="交易时间">
              {paymentInfo ? moment(paymentInfo.create_datetime).format('YYYY-MM-DD HH:mm:ss') : ''}
            </Descriptions.Item>
            <Descriptions.Item label="支付方式">{paymentInfo?.pay_method}</Descriptions.Item>
          </Descriptions>
        </Spin>
      </Drawer>
    </div>
  );
};

export default PaymentHistory;
