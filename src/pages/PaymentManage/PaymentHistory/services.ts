import request from "@/utils/request";
import type {PaymentDetail, PaymentListItem, QueryPaymentListParams} from "@/pages/PaymentManage/PaymentHistory/type";

export const getPaymentList = (params: QueryPaymentListParams) => {
  return request<API.Response<PaymentListItem[]>>('/api/reservation/payment/get_list/', {
    method: 'GET',
    params: { ...params }
  })
}

export const getPaymentDetail = (id: number) => {
  return request<API.Response<PaymentDetail>>(`/api/reservation/payment/${id}`, {
    method: 'GET',
  })
}

export function initiateRefund(checkup_appointment_id: number | string) {
  return request<API.Response>('/api/wechat_pay/refund/', {
    method: 'POST',
    data: { checkup_appointment_id },
  });
}
