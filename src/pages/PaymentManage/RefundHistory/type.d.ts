import type {PaymentListItem} from "@/pages/PaymentManage/PaymentHistory/type";

export type QueryRefundHistoryParams = {
  create_datetime?: string;
  pay_method?: string;
  status?: string;
  number?: string;
} & API.PaginatedParams;

export enum RefundStatusEnum {
  成功 = '成功',
  失败 = '失败',
  处理中 = '处理中',
  转入代发 = '转入代发',
  退款申请失败 = '退款申请失败'
}

export type RefundHistoryListItem = {
  id: number;
  modifier_name?: string;
  creator_name?: string;
  dept_belong_id?: string;
  create_datetime: string;
  update_datetime: string;
  order_info: {
    create_datetime: string;
    id: number;
    payment_number: string;
    serial_number: string;
    update_datetime: string;
  };
  description?: string;
  modifier?: string;
  appointment_number: string;
  identify_number: string;
  refund_money: number;
  status: string;
  pay_method: string;
  return_account: string;
  creator?: string;
  payment: number;
  order: number;
};

export type RefundHistoryDetail = RefundHistoryListItem & {
  user_info: {
    id: number;
    name: string;
    gender: number;
    phone: string;
    identify_number: string;
    age: number;
  };
  payment_info: PaymentListItem
};
