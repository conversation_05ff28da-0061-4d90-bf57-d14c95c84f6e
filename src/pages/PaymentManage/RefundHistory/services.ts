import request from "@/utils/request";
import type {QueryRefundHistoryParams, RefundHistoryListItem} from "@/pages/PaymentManage/RefundHistory/type";

export const getRefundHistoryList = (params: QueryRefundHistoryParams) => {
  return request<API.Response<RefundHistoryListItem[]>>('/api/reservation/refund/get_list/', {
    method: 'GET',
    params: { ...params }
  })
}

export const getRefundHistoryDetail = (id: number) => {
  return request<API.Response<any>>(`/api/reservation/refund/${id}`, {
    method: 'GET',
  })
}
