import type { MarriageLimitValue, ProjectListItem } from '@/pages/PackageManage/ProjectManage/type';
import type { GenderLimitValue, StatusValue } from '@/pages/PackageManage/IndexManage/type';

export type QueryPackageListParams = {
  keyword?: string;
  institution_name?: string;
  institution_code?: string;
} & API.PaginatedParams;

export type PackageListItem = {
  id: string;
  package_code: string;
  package_name: string;
  gender_limit: GenderLimitValue;
  age_min: number;
  age_max: number;
  individual_price: string;
  group_price: string;
  status: StatusValue;
  marriage_limit: MarriageLimitValue;
  projects: ProjectListItem[];
  institution_code: string;
  institution_name: string;
  created_time: string;
};

export type AddPackageParams = {
  package_code: string;
  package_name: string;
  gender_limit: GenderLimitValue;
  age_min: number;
  age_max: number;
  individual_price: string;
  group_price: string;
  status: StatusValue;
  marriage_limit: MarriageLimitValue;
  project_ids: number[];
  institution_code: string;
};
