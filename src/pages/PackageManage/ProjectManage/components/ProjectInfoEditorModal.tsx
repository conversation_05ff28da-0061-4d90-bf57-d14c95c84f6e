import { Col, Form, Input, InputNumber, message, Modal, Radio, Row, Select, Spin, Tag } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import type { Rule } from 'antd/lib/form';
import { debounce } from 'lodash';
import {
  AddProjectParams,
  MarriageLimitEnum,
  PregnantLimitEnum,
  ProjectListItem,
  ProjectTypeEnum,
} from '@/pages/PackageManage/ProjectManage/type.d';
import { getDepartmentList, getIndexOptions } from '@/pages/PackageManage/ProjectManage/services';
import {
  GenderLimitEnum,
  StatusEnum,
  type IndexListItem,
  StatusValue,
} from '@/pages/PackageManage/IndexManage/type.d';
import type { DefaultOptionType } from 'antd/lib/select';
import { useModel } from 'umi';
import useInstitution from '@/hooks/useInstitution';

type Props = {
  open: boolean;
  current?: ProjectListItem;
  handleOk: (data: AddProjectParams) => Promise<void>;
  handleCancel: () => void;
};

const rules: Record<string, Rule[]> = {
  project_code: [{ required: true, message: '请输入项目编号' }],
  project_name: [{ required: true, message: '请输入项目名称' }],
  department: [{ required: true, message: '请选择科室' }],
  project_type: [{ required: true, message: '请选择项目类别' }],
  marriage_limit: [{ required: true, message: '请选择婚姻限制' }],
  gender_limit: [{ required: true, message: '请选择性别限制' }],
  pregnant_limit: [{ required: true, message: '请选择怀孕限制' }],
  institution_code: [{ required: true, message: '请选择所属机构' }],
  price: [{ required: true, message: '请输入价格' }],
  status: [{ required: true, message: '请选择状态' }],
};

const ProjectInfoEditorModal: React.FC<Props> = (props) => {
  const { open, current, handleOk, handleCancel } = props;

  const { isInstitution, institutionCode } = useInstitution();
  const { getHospitalOptionsByKey } = useModel('useHospitalModel');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [title, setTitle] = useState('新增项目');
  const [departmentOptions, setDepartmentOptions] = useState<DefaultOptionType[]>([]);
  const [fetching, setFetching] = useState(false);
  const [indexValue, setIndexValue] = useState<string | null>(null);
  const [indexDataSource, setIndexDataSource] = useState<IndexListItem[]>([]);
  const options = useMemo(() => {
    return indexDataSource.map((item) => ({
      label: item.indicator_name,
      value: item.indicator_code,
    }));
  }, [indexDataSource]);
  const [selectIndexData, setSelectIndexData] = useState<IndexListItem[]>([]);

  useEffect(() => {
    getDepartmentList()
      .then((res) => {
        setDepartmentOptions(res.data.map((name) => ({ value: name, label: name })));
      })
      .catch((e) => {
        message.error('加载科室信息失败！');
      });
  }, []);

  useEffect(() => {
    if (open) {
      if (current) {
        setTitle('编辑项目');
        form.setFieldsValue(current);
        const initSelectedIndexData = current.indicators.map((item) => ({
          ...item,
          status: 'active' as StatusValue,
        }));
        setSelectIndexData(initSelectedIndexData);
      } else {
        setTitle('新增项目');
        form.resetFields();
        setSelectIndexData([]);
      }
    }
  }, [open]);

  const searchIndex = debounce((value: string) => {
    if (value) {
      setFetching(true);
      getIndexOptions(value)
        .then((res) => {
          setIndexDataSource(res.data);
          if (res.data?.length < 1) {
            message.warn('未查询到相关指标数据！');
          }
        })
        .finally(() => {
          setFetching(false);
        });
    }
  }, 800);

  const indexChange = (value: string) => {
    if (selectIndexData.find((item) => item.indicator_code === value)) {
      message.warn('此指标已存在与项目中');
    } else {
      const addIndexItem = indexDataSource.find((item) => item.indicator_code === value);
      setSelectIndexData([...selectIndexData, addIndexItem!]);
    }
    setIndexValue(null);
  };

  const cancelIndexSelect = (code: string) => {
    setSelectIndexData(selectIndexData.filter((item) => item.indicator_code !== code));
  };

  const onOk = async () => {
    await form.validateFields();
    setLoading(true);
    const formValues = form.getFieldsValue()
    if (isInstitution){
      formValues.institution_code = institutionCode;
    }
    await handleOk({
      ...form.getFieldsValue(),
      indicator_ids: selectIndexData.map((item) => item.id),
    }).catch((e) => {
      console.error(e);
    });
    setLoading(false);
  };

  return (
    <Modal
      title={title}
      confirmLoading={loading}
      open={open}
      onOk={onOk}
      onCancel={handleCancel}
      width="60%"
    >
      <Form
        form={form}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        initialValues={{ marriage_limit: '不限', gender_limit: 'any', pregnant_limit: 'any' }}
      >
        <Row>
          <Col span={12}>
            <Form.Item name="project_code" label="项目编号" required rules={rules.project_code}>
              <Input placeholder="请输入项目编号" maxLength={50} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="project_name" label="项目名称" rules={rules.project_name}>
              <Input placeholder="请输入项目名称" maxLength={200} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="department" label="所属科室" rules={rules.department}>
              <Select options={departmentOptions} placeholder="请选择所属科室" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="project_type" label="项目类型" rules={rules.project_type}>
              <Radio.Group>
                {Object.entries(ProjectTypeEnum).map(([value, label]) => (
                  <Radio key={value} value={value}>
                    {label}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="marriage_limit" label="婚姻限制" rules={rules.marriage_limit}>
              <Select
                placeholder="请选择婚姻限制"
                options={Object.entries(MarriageLimitEnum).map(([value, label]) => ({
                  value,
                  label,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="gender_limit" label="性别限制" rules={rules.gender_limit}>
              <Select
                placeholder="请选择性别限制"
                options={Object.entries(GenderLimitEnum).map(([value, label]) => ({
                  value,
                  label,
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="pregnant_limit" label="怀孕限制" rules={rules.pregnant_limit}>
              <Select
                placeholder="请选择怀孕限制"
                options={Object.entries(PregnantLimitEnum).map(([value, label]) => ({
                  value,
                  label,
                }))}
              />
            </Form.Item>
          </Col>
          {!isInstitution && (
            <Col span={12}>
              <Form.Item name="institution_code" label="所属机构" rules={rules.institution_code}>
                <Select
                  placeholder="请选择所属机构"
                  options={getHospitalOptionsByKey('institution_code')}
                />
              </Form.Item>
            </Col>
          )}
          <Col span={12}>
            <Form.Item name="price" label="价格" rules={rules.price}>
              <InputNumber
                style={{ width: '100%' }}
                placeholder="请输入价格"
                precision={2}
                min={0}
                addonAfter="￥"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="status" label="状态" rules={rules.status}>
              <Radio.Group>
                {Object.entries(StatusEnum).map(([value, label]) => (
                  <Radio key={value} value={value}>
                    {label}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="指标选择">
              <Select
                placeholder="指标编码或指标名称"
                showSearch
                value={indexValue}
                options={options}
                defaultActiveFirstOption={false}
                showArrow={false}
                filterOption={false}
                onSearch={searchIndex}
                onChange={indexChange}
                notFoundContent={fetching ? <Spin size="small" /> : null}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Row>
        <Col span={17} offset={4}>
          {selectIndexData.map((item) => (
            <Tag
              style={{ marginBottom: 5 }}
              key={item.indicator_code}
              closable
              onClose={() => cancelIndexSelect(item.indicator_code)}
            >
              {item.indicator_code} {item.indicator_name} {GenderLimitEnum[item.gender]}
            </Tag>
          ))}
        </Col>
      </Row>
    </Modal>
  );
};

export default ProjectInfoEditorModal;
