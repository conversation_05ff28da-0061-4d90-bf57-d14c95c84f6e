# 标签审核模块

## 功能概述

标签审核模块用于管理和审核系统中的标签信息，确保标签的准确性和规范性。

## 功能特性

### 总检标签审核页面

- **列表展示**：使用 ProTable 组件展示标签审核列表
- **搜索筛选**：支持按标签、原文ID、创建时间、审核时间、审核状态进行筛选
- **批量操作**：支持批量通过或拒绝待审核的标签
- **详情查看**：可查看标签的详细信息
- **审核操作**：支持单个标签的审核，可添加审核意见

## 页面结构

```
src/pages/TagAudit/
├── GeneralAudit/           # 总检标签审核
│   ├── index.tsx          # 主页面组件
│   ├── index.less         # 样式文件
│   ├── type.ts            # 类型定义
│   ├── services.ts        # API服务
│   ├── mock.ts            # 模拟数据
│   └── README.md          # 说明文档
└── README.md              # 模块说明
```

## 数据结构

### TagAuditListItem
```typescript
interface TagAuditListItem {
  id: number;
  original_id: string;        // 原文ID
  original_text: string;      // 原文表述
  organ: string;              // 器官
  keywords: string;           // 关键词
  tag_code: string;           // 标签编码
  tag_name: string;           // 标签名称
  creator: string;            // 创建人员
  created_time: string;       // 创建时间
  auditor?: string;           // 审核人员
  audit_time?: string;        // 审核时间
  status: 'pending' | 'approved' | 'rejected'; // 状态
}
```

## API 接口

### 获取标签审核列表
- **接口**: `GET /api/tag-audit/list`
- **参数**: QueryTagAuditListParams

### 获取标签审核详情
- **接口**: `GET /api/tag-audit/detail/{id}`

### 审核标签
- **接口**: `POST /api/tag-audit/audit`
- **参数**: AuditTagParams

### 批量审核标签
- **接口**: `POST /api/tag-audit/batch-audit`

## 使用说明

1. **访问页面**：通过菜单 "标签审核" -> "总检标签审核" 进入页面
2. **筛选数据**：使用搜索表单筛选需要的数据
3. **查看详情**：点击操作列的"查看"按钮查看标签详情
4. **单个审核**：点击操作列的"审核"按钮对单个标签进行审核
5. **批量审核**：选择多个待审核标签，使用顶部的批量操作按钮

## 状态说明

- **待审核** (pending)：新创建的标签，等待审核
- **已通过** (approved)：审核通过的标签
- **已拒绝** (rejected)：审核被拒绝的标签

## 注意事项

1. 只有状态为"待审核"的标签才能进行审核操作
2. 批量操作只能选择相同状态的标签
3. 审核意见为可选项，建议在拒绝时填写拒绝原因
