import request from '@/utils/request';
import type {
  TagAuditListItem,
  QueryTagAuditListParams,
  AuditTagParams,
  TagAuditDetail,
} from './type';
import { mockTagAuditList } from './mock';

// 获取标签审核列表
export const getTagAuditList = (params: QueryTagAuditListParams) => {
  // 在开发环境中使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise<API.Response<TagAuditListItem[]>>((resolve) => {
      setTimeout(() => {
        const { page_num = 1, page_size = 10 } = params;
        const start = (page_num - 1) * page_size;
        const end = start + page_size;
        const data = mockTagAuditList.slice(start, end);
        resolve({
          code: 2000,
          data,
          total: mockTagAuditList.length,
        });
      }, 500);
    });
  }

  return request<API.Response<TagAuditListItem[]>>('/api/tag-audit/list', {
    method: 'GET',
    params: { ...params },
  });
};

// 获取标签审核详情
export const getTagAuditDetail = (id: number) => {
  // 在开发环境中使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise<API.Response<TagAuditDetail>>((resolve) => {
      setTimeout(() => {
        const item = mockTagAuditList.find(item => item.id === id);
        if (item) {
          resolve({
            code: 2000,
            data: {
              ...item,
              audit_comment: item.status === 'rejected' ? '标签不够准确，需要重新标注' : undefined,
              original_content: '完整的原文内容...',
              tag_description: '标签的详细描述...',
            },
          });
        } else {
          resolve({
            code: 4004,
            message: '记录不存在',
            data: {} as TagAuditDetail,
          });
        }
      }, 300);
    });
  }

  return request<API.Response<TagAuditDetail>>(`/api/tag-audit/detail/${id}`, {
    method: 'GET',
  });
};

// 审核标签
export const auditTag = (params: AuditTagParams) => {
  // 在开发环境中使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise<API.Response>((resolve) => {
      setTimeout(() => {
        resolve({
          code: 2000,
          message: '审核成功',
          data: null,
        });
      }, 800);
    });
  }

  return request<API.Response>('/api/tag-audit/audit', {
    method: 'POST',
    data: { ...params },
  });
};

// 批量审核标签
export const batchAuditTag = (params: { ids: number[]; status: 'approved' | 'rejected'; audit_comment?: string }) => {
  // 在开发环境中使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise<API.Response>((resolve) => {
      setTimeout(() => {
        resolve({
          code: 2000,
          message: '批量审核成功',
          data: null,
        });
      }, 1000);
    });
  }

  return request<API.Response>('/api/tag-audit/batch-audit', {
    method: 'POST',
    data: { ...params },
  });
};
