export interface TagAuditListItem {
  id: number;
  original_id: string;
  original_text: string;
  organ: string;
  keywords: string;
  tag_code: string;
  tag_name: string;
  creator: string;
  created_time: string;
  auditor?: string;
  audit_time?: string;
  status: 'pending' | 'approved' | 'rejected';
}

export interface QueryTagAuditListParams extends API.PaginatedParams {
  tag_name?: string;
  original_id?: string;
  created_time_start?: string;
  created_time_end?: string;
  audit_time_start?: string;
  audit_time_end?: string;
  status?: string;
}

export interface AuditTagParams {
  id: number;
  status: 'approved' | 'rejected';
  audit_comment?: string;
}

export interface TagAuditDetail extends TagAuditListItem {
  audit_comment?: string;
  original_content?: string;
  tag_description?: string;
}

export const AuditStatusEnum = {
  pending: '待审核',
  approved: '已通过',
  rejected: '已拒绝',
};

export type AuditStatusValue = keyof typeof AuditStatusEnum;
