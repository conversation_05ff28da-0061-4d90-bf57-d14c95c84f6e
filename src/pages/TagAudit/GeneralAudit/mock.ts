import type { TagAuditListItem } from './type';

// 模拟数据
export const mockTagAuditList: TagAuditListItem[] = [
  {
    id: 1,
    original_id: 'ORG001',
    original_text: '患者出现胸闷、气短症状，建议进一步检查心电图',
    organ: '心脏',
    keywords: '胸闷,气短,心电图',
    tag_code: 'TAG001',
    tag_name: '心血管疾病',
    creator: '张医生',
    created_time: '2024-01-15 09:30:00',
    auditor: '李主任',
    audit_time: '2024-01-15 14:20:00',
    status: 'approved',
  },
  {
    id: 2,
    original_id: 'ORG002',
    original_text: '血压偏高，收缩压150mmHg，舒张压95mmHg',
    organ: '心血管',
    keywords: '血压,高血压,收缩压,舒张压',
    tag_code: 'TAG002',
    tag_name: '高血压',
    creator: '王医生',
    created_time: '2024-01-15 10:15:00',
    status: 'pending',
  },
  {
    id: 3,
    original_id: 'ORG003',
    original_text: '肝功能异常，ALT升高至80U/L',
    organ: '肝脏',
    keywords: 'ALT,肝功能,异常',
    tag_code: 'TAG003',
    tag_name: '肝功能异常',
    creator: '赵医生',
    created_time: '2024-01-15 11:00:00',
    auditor: '陈主任',
    audit_time: '2024-01-15 16:30:00',
    status: 'rejected',
  },
  {
    id: 4,
    original_id: 'ORG004',
    original_text: '血糖偏高，空腹血糖7.2mmol/L',
    organ: '内分泌',
    keywords: '血糖,空腹血糖,糖尿病',
    tag_code: 'TAG004',
    tag_name: '糖尿病',
    creator: '孙医生',
    created_time: '2024-01-15 13:45:00',
    status: 'pending',
  },
  {
    id: 5,
    original_id: 'ORG005',
    original_text: '肺部CT显示结节影，建议定期复查',
    organ: '肺部',
    keywords: 'CT,结节,肺部',
    tag_code: 'TAG005',
    tag_name: '肺结节',
    creator: '周医生',
    created_time: '2024-01-15 15:20:00',
    auditor: '吴主任',
    audit_time: '2024-01-15 17:10:00',
    status: 'approved',
  },
];
