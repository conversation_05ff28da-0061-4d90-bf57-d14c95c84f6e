import React, { useRef, useState, useMemo } from 'react';
import { Button, Space, Popconfirm, message, DatePicker, Modal, Form, Input, Tag, Radio } from 'antd';
import type { ActionType, ListToolBarProps, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { SearchConfig } from '@ant-design/pro-table/lib/components/Form/FormRender';
import type { TagAuditListItem, QueryTagAuditListParams, TagAuditDetail } from './type';
import { AuditStatusEnum } from './type';
import { getTagAuditList, getTagAuditDetail, auditTag, batchAuditTag } from './services';
import SimpleTimeRangeSelector from './components/SimpleTimeRangeSelector';
import dayjs from 'dayjs';
import moment from 'moment';
import './index.less';

const { RangePicker } = DatePicker;
const { TextArea } = Input;

const GeneralAudit: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<TagAuditListItem[]>([]);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [auditModalVisible, setAuditModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<TagAuditListItem | undefined>();
  const [auditDetail, setAuditDetail] = useState<TagAuditDetail | undefined>();
  const [auditForm] = Form.useForm();
  const [auditLoading, setAuditLoading] = useState(false);

  // 判断是否可以批量审核
  const canBatchAudit = useMemo(() => {
    if (selectedRows.length < 1) {
      return false;
    }
    return selectedRows.every((item) => item.status === 'pending');
  }, [selectedRows]);

  // 初始化表格数据
  const initTableData = async (
    params: QueryTagAuditListParams & {
      pageSize: number;
      current: number;
      created_time: any;
      audit_time: any;
    },
  ) => {
    const { current, pageSize, created_time, audit_time, ...restParams } = params;
    let created_time_start, created_time_end, audit_time_start, audit_time_end;

    // 处理创建时间区间
    if (created_time?.dates?.length) {
      const startTime = created_time.dates[0];
      const endTime = created_time.dates[1];
      const intervalType = created_time.intervalType || 'closed';

      switch (intervalType) {
        case 'closed':
          created_time_start = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          created_time_end = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'open':
          created_time_start = moment(startTime).add(1, 'second').format('YYYY-MM-DD HH:mm:ss');
          created_time_end = moment(endTime).subtract(1, 'second').format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'left-open':
          created_time_start = moment(startTime).add(1, 'second').format('YYYY-MM-DD HH:mm:ss');
          created_time_end = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'right-open':
          created_time_start = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          created_time_end = moment(endTime).subtract(1, 'second').format('YYYY-MM-DD HH:mm:ss');
          break;
      }
    }

    // 处理审核时间区间
    if (audit_time?.dates?.length) {
      const startTime = audit_time.dates[0];
      const endTime = audit_time.dates[1];
      const intervalType = audit_time.intervalType || 'closed';

      switch (intervalType) {
        case 'closed':
          audit_time_start = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          audit_time_end = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'open':
          audit_time_start = moment(startTime).add(1, 'second').format('YYYY-MM-DD HH:mm:ss');
          audit_time_end = moment(endTime).subtract(1, 'second').format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'left-open':
          audit_time_start = moment(startTime).add(1, 'second').format('YYYY-MM-DD HH:mm:ss');
          audit_time_end = moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'right-open':
          audit_time_start = moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          audit_time_end = moment(endTime).subtract(1, 'second').format('YYYY-MM-DD HH:mm:ss');
          break;
      }
    }

    const res = await getTagAuditList({
      ...restParams,
      page_size: pageSize,
      page_num: current,
      created_time_start,
      created_time_end,
      audit_time_start,
      audit_time_end,
    });
    return { data: res.data, success: true, total: res.total };
  };

  // 查看详情
  const handleViewDetail = async (record: TagAuditListItem) => {
    try {
      const res = await getTagAuditDetail(record.id);
      if (res.code === 2000) {
        setAuditDetail(res.data);
        setDetailModalVisible(true);
      }
    } catch (error) {
      message.error('获取详情失败');
    }
  };

  // 打开审核弹窗
  const handleOpenAudit = (record: TagAuditListItem) => {
    setCurrentRecord(record);
    setAuditModalVisible(true);
    auditForm.resetFields();
  };

  // 执行审核
  const handleAudit = async () => {
    try {
      const values = await auditForm.validateFields();
      setAuditLoading(true);
      
      const res = await auditTag({
        id: currentRecord!.id,
        status: values.status,
        audit_comment: values.audit_comment,
      });
      
      if (res.code === 2000) {
        message.success('审核成功');
        setAuditModalVisible(false);
        setCurrentRecord(undefined);
        actionRef.current?.reload();
      }
    } catch (error) {
      message.error('审核失败');
    } finally {
      setAuditLoading(false);
    }
  };

  // 批量审核
  const handleBatchAudit = async (status: 'approved' | 'rejected') => {
    try {
      const res = await batchAuditTag({
        ids: selectedRowKeys as number[],
        status,
      });
      
      if (res.code === 2000) {
        message.success('批量审核成功');
        setSelectedRowKeys([]);
        setSelectedRows([]);
        actionRef.current?.reload();
      }
    } catch (error) {
      message.error('批量审核失败');
    }
  };

  const searchConfig: SearchConfig = {
    labelWidth: 'auto',
    span: 6,
    collapsed: false,
    collapseRender: false,
    optionRender: (config) => [
      <Button key="submit" type="primary" onClick={() => config?.form?.submit()}>
        筛选
      </Button>,
      <Button key="clear" onClick={() => actionRef.current?.reset!()}>
        重置
      </Button>,
    ],
  };

  const toolBarConfig: ListToolBarProps = {
    settings: [],
    search: (
      <Space>
        <Button
          type="primary"
          disabled={!canBatchAudit}
          onClick={() => handleBatchAudit('approved')}
        >
          批量通过
        </Button>
        <Button
          type="primary"
          danger
          disabled={!canBatchAudit}
          onClick={() => handleBatchAudit('rejected')}
        >
          批量拒绝
        </Button>
      </Space>
    ),
  };

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedKeys: React.Key[], rows: TagAuditListItem[]) => {
      setSelectedRowKeys(selectedKeys);
      setSelectedRows(rows);
    },
    getCheckboxProps: (record: TagAuditListItem) => ({
      disabled: record.status !== 'pending',
    }),
  };

  const columns: ProColumns<TagAuditListItem>[] = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'indexBorder',
      width: 60,
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '原文ID',
      dataIndex: 'original_id',
      width: 100,
      align: 'center',
    },
    {
      title: '原文表述',
      dataIndex: 'original_text',
      ellipsis: true,
      width: 200,
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '器官',
      dataIndex: 'organ',
      width: 80,
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      ellipsis: true,
      width: 120,
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '标签编码',
      dataIndex: 'tag_code',
      width: 100,
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '标签',
      dataIndex: 'tag_name',
      width: 120,
      align: 'center',
    },
    {
      title: '创建人员',
      dataIndex: 'creator',
      width: 100,
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created_time',
      width: 140,
      hideInSearch: true,
      align: 'center',
      render: (_, record) =>
        record.created_time ? dayjs(record.created_time).format('YYYY-MM-DD HH:mm:ss') : '',
    },
    {
      title: '创建时间',
      dataIndex: 'created_time',
      hideInTable: true,
      renderFormItem: () => (
        <SimpleTimeRangeSelector
          placeholder={['创建开始时间', '创建结束时间']}
        />
      ),
    },
    {
      title: '审核人员',
      dataIndex: 'auditor',
      width: 100,
      hideInSearch: true,
      align: 'center',
      render: (_, record) => record.auditor || '-',
    },
    {
      title: '审核时间',
      dataIndex: 'audit_time',
      width: 140,
      hideInSearch: true,
      align: 'center',
      render: (_, record) =>
        record.audit_time ? dayjs(record.audit_time).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '审核时间',
      dataIndex: 'audit_time',
      hideInTable: true,
      renderFormItem: () => (
        <SimpleTimeRangeSelector
          placeholder={['审核开始时间', '审核结束时间']}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      valueEnum: AuditStatusEnum,
      render: (_, record) => {
        const statusColors = {
          pending: 'orange',
          approved: 'green',
          rejected: 'red',
        };
        return (
          <Tag color={statusColors[record.status]}>
            {AuditStatusEnum[record.status]}
          </Tag>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      hideInSearch: true,
      width: 150,
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          {record.status === 'pending' && (
            <Button
              type="link"
              size="small"
              onClick={() => handleOpenAudit(record)}
            >
              审核
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="base-content">
      <ProTable
        actionRef={actionRef}
        rowKey="id"
        search={searchConfig}
        toolbar={toolBarConfig}
        request={initTableData}
        columns={columns}
        rowSelection={rowSelection}
        pagination={{ defaultPageSize: 10 }}
      />

      {/* 详情弹窗 */}
      <Modal
        title="标签审核详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {auditDetail && (
          <div className="tag-audit-detail">
            <div className="detail-item">
              <span className="label">原文ID：</span>{auditDetail.original_id}
            </div>
            <div className="detail-item">
              <span className="label">原文表述：</span>{auditDetail.original_text}
            </div>
            <div className="detail-item">
              <span className="label">器官：</span>{auditDetail.organ}
            </div>
            <div className="detail-item">
              <span className="label">关键词：</span>{auditDetail.keywords}
            </div>
            <div className="detail-item">
              <span className="label">标签编码：</span>{auditDetail.tag_code}
            </div>
            <div className="detail-item">
              <span className="label">标签：</span>{auditDetail.tag_name}
            </div>
            <div className="detail-item">
              <span className="label">创建人员：</span>{auditDetail.creator}
            </div>
            <div className="detail-item">
              <span className="label">创建时间：</span>
              {auditDetail.created_time ? dayjs(auditDetail.created_time).format('YYYY-MM-DD HH:mm:ss') : ''}
            </div>
            {auditDetail.auditor && (
              <div className="detail-item">
                <span className="label">审核人员：</span>{auditDetail.auditor}
              </div>
            )}
            {auditDetail.audit_time && (
              <div className="detail-item">
                <span className="label">审核时间：</span>
                {dayjs(auditDetail.audit_time).format('YYYY-MM-DD HH:mm:ss')}
              </div>
            )}
            <div className="detail-item">
              <span className="label">状态：</span>
              <Tag color={auditDetail.status === 'pending' ? 'orange' : auditDetail.status === 'approved' ? 'green' : 'red'}>
                {AuditStatusEnum[auditDetail.status]}
              </Tag>
            </div>
            {auditDetail.audit_comment && (
              <div className="detail-item">
                <span className="label">审核意见：</span>{auditDetail.audit_comment}
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 审核弹窗 */}
      <Modal
        title="标签审核"
        open={auditModalVisible}
        onCancel={() => setAuditModalVisible(false)}
        onOk={handleAudit}
        confirmLoading={auditLoading}
      >
        <Form form={auditForm} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }} className="audit-form">
          <Form.Item
            label="审核结果"
            name="status"
            rules={[{ required: true, message: '请选择审核结果' }]}
          >
            <Radio.Group>
              <Radio value="approved">通过</Radio>
              <Radio value="rejected">拒绝</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label="审核意见"
            name="audit_comment"
          >
            <TextArea rows={4} placeholder="请输入审核意见（可选）" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default GeneralAudit;
