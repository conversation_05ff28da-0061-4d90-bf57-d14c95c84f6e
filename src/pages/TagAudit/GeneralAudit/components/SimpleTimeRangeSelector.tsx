import React, { useState } from 'react';
import { DatePicker, Radio, Space } from 'antd';
import moment from 'moment';

const { RangePicker } = DatePicker;

interface SimpleTimeRangeSelectorProps {
  value?: any;
  onChange?: (value: any) => void;
  placeholder?: [string, string];
}

const SimpleTimeRangeSelector: React.FC<SimpleTimeRangeSelectorProps> = ({
  value,
  onChange,
  placeholder = ['开始时间', '结束时间'],
}) => {
  const [intervalType, setIntervalType] = useState<'closed' | 'open' | 'left-open' | 'right-open'>('closed');

  const handleRangeChange = (dates: any) => {
    if (onChange) {
      onChange({
        dates,
        intervalType,
      });
    }
  };

  const handleIntervalTypeChange = (e: any) => {
    const type = e.target.value;
    setIntervalType(type);
    
    if (onChange && value?.dates) {
      onChange({
        dates: value.dates,
        intervalType: type,
      });
    }
  };

  const getPlaceholderText = (): [string, string] => {
    switch (intervalType) {
      case 'closed':
        return [`[${placeholder[0]}]`, `[${placeholder[1]}]`];
      case 'open':
        return [`(${placeholder[0]})`, `(${placeholder[1]})`];
      case 'left-open':
        return [`(${placeholder[0]})`, `[${placeholder[1]}]`];
      case 'right-open':
        return [`[${placeholder[0]})`, `(${placeholder[1]})`];
      default:
        return placeholder;
    }
  };

  const getIntervalDescription = () => {
    switch (intervalType) {
      case 'closed':
        return '包含起始和结束时间';
      case 'open':
        return '不包含起始和结束时间';
      case 'left-open':
        return '不包含起始时间，包含结束时间';
      case 'right-open':
        return '包含起始时间，不包含结束时间';
      default:
        return '';
    }
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }} className="simple-time-range-selector">
      <Radio.Group
        value={intervalType}
        onChange={handleIntervalTypeChange}
        size="small"
        style={{ width: '100%' }}
      >
        <Radio.Button value="closed">[闭区间]</Radio.Button>
        <Radio.Button value="open">(开区间)</Radio.Button>
        <Radio.Button value="left-open">(左开]</Radio.Button>
        <Radio.Button value="right-open">[右开)</Radio.Button>
      </Radio.Group>
      
      <RangePicker
        value={value?.dates}
        onChange={handleRangeChange}
        format="YYYY-MM-DD HH:mm:ss"
        placeholder={getPlaceholderText()}
        allowEmpty={[true, true]}
        showTime={{
          defaultValue: [
            moment('00:00:00', 'HH:mm:ss'),
            moment('23:59:59', 'HH:mm:ss'),
          ],
        }}
        style={{ width: '100%' }}
      />
      
      <div style={{ fontSize: '12px', color: '#666', textAlign: 'center' }}>
        {getIntervalDescription()}
      </div>
    </Space>
  );
};

export default SimpleTimeRangeSelector;
