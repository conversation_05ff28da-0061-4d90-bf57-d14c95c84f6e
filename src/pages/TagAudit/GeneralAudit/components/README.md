# 时间区间选择组件

## 组件说明

提供了两个时间区间选择组件，支持开区间、闭区间等多种时间范围选择方式。

## 组件列表

### 1. TimeRangeSelector（完整版）

功能最全的时间区间选择器，提供详细的配置选项。

**特性：**
- 支持四种区间类型选择
- 可单独控制起始和结束时间的包含性
- 提供详细的配置界面

**使用示例：**
```tsx
import TimeRangeSelector from './components/TimeRangeSelector';

<TimeRangeSelector
  placeholder={['开始时间', '结束时间']}
  value={timeRange}
  onChange={(value) => setTimeRange(value)}
/>
```

### 2. SimpleTimeRangeSelector（简化版）

简化的时间区间选择器，提供快捷的区间类型选择。

**特性：**
- 四个快捷按钮选择区间类型
- 界面简洁，操作便捷
- 自动显示区间类型说明

**使用示例：**
```tsx
import SimpleTimeRangeSelector from './components/SimpleTimeRangeSelector';

<SimpleTimeRangeSelector
  placeholder={['开始时间', '结束时间']}
  value={timeRange}
  onChange={(value) => setTimeRange(value)}
/>
```

## 区间类型说明

### 闭区间 [开始, 结束]
- 包含起始时间和结束时间
- 例：[2024-01-01 00:00:00, 2024-01-31 23:59:59]
- 查询条件：`>= 开始时间 AND <= 结束时间`

### 开区间 (开始, 结束)
- 不包含起始时间和结束时间
- 例：(2024-01-01 00:00:00, 2024-01-31 23:59:59)
- 查询条件：`> 开始时间 AND < 结束时间`

### 左开右闭 (开始, 结束]
- 不包含起始时间，包含结束时间
- 例：(2024-01-01 00:00:00, 2024-01-31 23:59:59]
- 查询条件：`> 开始时间 AND <= 结束时间`

### 左闭右开 [开始, 结束)
- 包含起始时间，不包含结束时间
- 例：[2024-01-01 00:00:00, 2024-01-31 23:59:59)
- 查询条件：`>= 开始时间 AND < 结束时间`

## 数据格式

组件返回的数据格式：

```typescript
{
  dates: [Moment, Moment] | null,  // 选择的时间范围
  intervalType: 'closed' | 'open' | 'left-open' | 'right-open',  // 区间类型
  includeStart?: boolean,  // 是否包含开始时间（仅完整版）
  includeEnd?: boolean,    // 是否包含结束时间（仅完整版）
}
```

## 实际应用场景

### 1. 日志查询
```
查询 2024-01-01 到 2024-01-31 的日志
- 闭区间：包含 1月1日 00:00:00 和 1月31日 23:59:59 的日志
- 开区间：不包含边界时间点的日志
```

### 2. 数据统计
```
统计某个时间段的数据
- 左闭右开：包含开始时间，不包含结束时间，常用于按天统计
- 左开右闭：不包含开始时间，包含结束时间
```

### 3. 审核记录查询
```
查询审核时间范围
- 可精确控制是否包含边界时间
- 避免重复统计边界数据
```

## 技术实现

### 时间边界处理

在查询时，组件会自动调整时间边界：

```typescript
// 开区间处理示例
if (intervalType === 'open') {
  startTime = moment(startTime).add(1, 'second');
  endTime = moment(endTime).subtract(1, 'second');
}
```

### 样式定制

可通过 CSS 类名进行样式定制：

```less
.simple-time-range-selector {
  .ant-radio-group {
    // 自定义样式
  }
}
```
