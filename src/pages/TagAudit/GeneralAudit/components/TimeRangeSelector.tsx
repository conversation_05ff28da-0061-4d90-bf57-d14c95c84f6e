import React, { useState } from 'react';
import { DatePicker, Select, Space, Checkbox } from 'antd';
import moment from 'moment';

const { RangePicker } = DatePicker;
const { Option } = Select;

interface TimeRangeSelectorProps {
  value?: any;
  onChange?: (value: any) => void;
  placeholder?: [string, string];
}

const TimeRangeSelector: React.FC<TimeRangeSelectorProps> = ({
  value,
  onChange,
  placeholder = ['开始时间', '结束时间'],
}) => {
  const [rangeType, setRangeType] = useState<'closed' | 'open' | 'left-open' | 'right-open'>('closed');
  const [includeStart, setIncludeStart] = useState(true);
  const [includeEnd, setIncludeEnd] = useState(true);

  const handleRangeChange = (dates: any) => {
    if (onChange) {
      onChange({
        dates,
        rangeType,
        includeStart,
        includeEnd,
      });
    }
  };

  const handleRangeTypeChange = (type: string) => {
    setRangeType(type as any);
    switch (type) {
      case 'closed':
        setIncludeStart(true);
        setIncludeEnd(true);
        break;
      case 'open':
        setIncludeStart(false);
        setIncludeEnd(false);
        break;
      case 'left-open':
        setIncludeStart(false);
        setIncludeEnd(true);
        break;
      case 'right-open':
        setIncludeStart(true);
        setIncludeEnd(false);
        break;
    }
    
    if (onChange && value?.dates) {
      onChange({
        dates: value.dates,
        rangeType: type,
        includeStart: type === 'closed' || type === 'right-open',
        includeEnd: type === 'closed' || type === 'left-open',
      });
    }
  };

  const getPlaceholderText = () => {
    const startSymbol = includeStart ? '[' : '(';
    const endSymbol = includeEnd ? ']' : ')';
    return [
      `${startSymbol}${placeholder[0]}`,
      `${placeholder[1]}${endSymbol}`,
    ];
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }} className="time-range-selector">
      <Select
        value={rangeType}
        onChange={handleRangeTypeChange}
        style={{ width: '100%' }}
        size="small"
      >
        <Option value="closed">闭区间 [开始, 结束]</Option>
        <Option value="open">开区间 (开始, 结束)</Option>
        <Option value="left-open">左开右闭 (开始, 结束]</Option>
        <Option value="right-open">左闭右开 [开始, 结束)</Option>
      </Select>

      <RangePicker
        value={value?.dates}
        onChange={handleRangeChange}
        format="YYYY-MM-DD HH:mm:ss"
        placeholder={getPlaceholderText() as [string, string]}
        allowEmpty={[true, true]}
        showTime={{
          defaultValue: [
            moment('00:00:00', 'HH:mm:ss'),
            moment('23:59:59', 'HH:mm:ss'),
          ],
        }}
        style={{ width: '100%' }}
      />

      <Space size="small">
        <Checkbox
          checked={includeStart}
          onChange={(e) => {
            setIncludeStart(e.target.checked);
            if (onChange && value?.dates) {
              onChange({
                ...value,
                includeStart: e.target.checked,
              });
            }
          }}
          disabled={rangeType !== 'closed' && rangeType !== 'open'}
        >
          包含开始时间
        </Checkbox>
        <Checkbox
          checked={includeEnd}
          onChange={(e) => {
            setIncludeEnd(e.target.checked);
            if (onChange && value?.dates) {
              onChange({
                ...value,
                includeEnd: e.target.checked,
              });
            }
          }}
          disabled={rangeType !== 'closed' && rangeType !== 'open'}
        >
          包含结束时间
        </Checkbox>
      </Space>
    </Space>
  );
};

export default TimeRangeSelector;
