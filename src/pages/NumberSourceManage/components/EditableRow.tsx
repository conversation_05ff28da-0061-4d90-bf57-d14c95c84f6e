import React from "react";
import type { FormInstance} from "antd";
import {Form} from "antd";

interface EditableRowProps {
  index: number;
}

export const EditableContext = React.createContext<FormInstance<Record<string, number>> | null>(null);

export const EditableRow: React.FC<EditableRowProps> = ({ index, ...props }) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};
