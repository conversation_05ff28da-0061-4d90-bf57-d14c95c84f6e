import React, { useEffect, useMemo, useState } from 'react';
import {Calendar, Card, Col, DatePicker, Empty, Row, Select, Space, Spin, Typography} from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import './index.less';
import { getNumberSourceList } from '@/pages/NumberSourceManage/services';
import type { NumberSourceDetail } from '@/pages/NumberSourceManage/types';
import {useModel} from "@@/plugin-model/useModel";

const { Title } = Typography;

const NumberSourceLookOver: React.FC = () => {
  const { hospitalList, getHospitalOptionsByKey } = useModel('useHospitalModel');
  const [loading, setLoading] = useState<boolean>(false);
  const [hospitalValue, setHospitalValue] = useState<number>();
  const [queryDate, setQueryDate] = useState<Moment>(moment());
  const [dateSource, setDateSource] = useState<Record<string, NumberSourceDetail>>({});
  const detailInfo = useMemo(() => {
    if (dateSource[queryDate.format('YYYY-MM-DD')]) {
      return dateSource[queryDate.format('YYYY-MM-DD')];
    }
    return null;
  }, [dateSource, queryDate]);

  const initDateSource = (monthDate: Moment, hospitalId: number) => {
    setLoading(true);
    const start_date = moment(monthDate).startOf('month').subtract(14, 'day').format('YYYY-MM-DD');
    const end_date = moment(monthDate).endOf('month').add(14, 'day').format('YYYY-MM-DD');
    getNumberSourceList({ start_date, end_date, institution: hospitalId })
      .then((res) => {
        const dateSourceMap: Record<string, NumberSourceDetail> = {};
        res.data.forEach((item) => {
          const dateStr = item.quota_date;
          dateSourceMap[dateStr] = item;
        });
        setDateSource(dateSourceMap);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const hospitalChange = (value: number) => {
    setHospitalValue(value);
    initDateSource(queryDate, value);
  };

  const dateSelected = (date: Moment) => {
    setQueryDate(date);
    if (date.format('YYYY-MM') !== queryDate.format('YYYY-MM')) {
      initDateSource(date, hospitalValue!);
    }
  };

  const dateCellRender = (date: Moment) => {
    const dateStr = date.format('YYYY-MM-DD');
    return dateSource[dateStr] && dateSource[dateStr].total_quota ? (
      <>
        <div>总人数：{dateSource[dateStr].used_total_quota}/{dateSource[dateStr].total_quota}</div>
        <div>男：{dateSource[dateStr].used_total_male}/{dateSource[dateStr].total_male}</div>
        <div>女：{dateSource[dateStr].used_total_female}/{dateSource[dateStr].total_female}</div>
      </>
    ) : null;
  };

  useEffect(() => {
    if (hospitalList.length > 0) {
      setHospitalValue(hospitalList[0].id);
      initDateSource(queryDate, hospitalList[0].id);
    }
  }, [hospitalList]);

  useEffect(() => {
    setLoading(true);
  }, []);

  return (
    <Spin spinning={loading}>
      <Card className="base-content">
        <Row gutter={10}>
          <Col span={18}>
            <Calendar
              value={queryDate}
              onChange={dateSelected}
              headerRender={({ value, onChange }) => (
                <Space className="margin-bottom">
                  <DatePicker
                    value={value}
                    onChange={(v) => onChange(v!)}
                    picker="month"
                    allowClear={false}
                  />
                  <Select
                    style={{ width: 200, paddingTop: '0.25rem', paddingBottom: '0.25rem'}}
                    options={getHospitalOptionsByKey('id')}
                    value={hospitalValue}
                    onChange={(v) => hospitalChange(v)}
                    placeholder="请设置预约机构"
                  />
                </Space>

              )}
              dateCellRender={dateCellRender}
            />
          </Col>
          <Col span={6}>
            <div className="config-detail">
              {detailInfo?.total_quota ? (
                <>
                  <Title level={5}>团队</Title>
                  <Card size="small" className="margin-bottom-10">
                    <p>团队：{detailInfo.used_group_quota}/{detailInfo.group_quota}</p>
                    <p>男：{detailInfo.used_group_male}/{detailInfo.group_male}</p>
                    <p className="margin-unset">女：{detailInfo.used_group_female}/{detailInfo.group_female}</p>
                  </Card>
                  <Title level={5}>个人</Title>
                  <Card size="small" className="margin-bottom-10">
                    <p>个人：{detailInfo.used_individual_quota}/{detailInfo.individual_quota}</p>
                    <p>男：{detailInfo.used_individual_male}/{detailInfo.individual_male}</p>
                    <p className="margin-unset">女：{detailInfo.used_individual_female}/{detailInfo.individual_female}</p>
                  </Card>
                  <Title level={5}>住院</Title>
                  <Card size="small" className="margin-bottom-10">
                    <p>住院：{detailInfo.used_admitted_quota}/{detailInfo.admitted_quota}</p>
                    <p>男：{detailInfo.used_admitted_male}/{detailInfo.admitted_male}</p>
                    <p className="margin-unset">女：{detailInfo.used_admitted_female}/{detailInfo.admitted_female}</p>
                  </Card>
                  {detailInfo.phy_quota_time_slot.length > 0 && (
                    <>
                      <Title level={5}>时间段</Title>
                      {detailInfo.phy_quota_time_slot.map((item) => (
                        <Card key={item.start_time} size="small" className="margin-bottom-10">
                          <p>
                            {moment(item.start_time, 'HH:mm').format('HH:mm')} - {moment(item.end_time, 'HH:mm').format('HH:mm')}
                          </p>
                          <p>总人数：{item.used_total_quota}/{item.total_quota}</p>
                          <p>男：{item.used_male_quota}/{item.male_quota}</p>
                          <p className="margin-unset">女：{item.used_female_quota}/{item.female_quota}</p>
                        </Card>
                      ))}
                    </>
                  )}
                  {detailInfo.phy_quota_project.length > 0 && (
                    <>
                      <Title level={5}>项目</Title>
                      {detailInfo.phy_quota_project.map((item) => (
                        <Card key={item.project} size="small" className="margin-bottom-10">
                          <p>{item.name}</p>
                          <p>总人数：{item.used_total_quota}/{item.total_quota}</p>
                          <p>男：{item.used_male_quota}/{item.male_quota}</p>
                          <p className="margin-unset">女：{item.used_female_quota}/{item.female_quota}</p>
                        </Card>
                      ))}
                    </>
                  )}
                </>
              ) : <Empty style={{marginTop: '50%'}} image={Empty.PRESENTED_IMAGE_SIMPLE} />}
            </div>
          </Col>
        </Row>
      </Card>
    </Spin>
  );
};

export default NumberSourceLookOver;
