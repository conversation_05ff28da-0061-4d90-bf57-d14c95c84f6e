export interface NumberSourceSaveParams {
  /**
   * 入院总数-女
   */
  admitted_female: number;
  /**
   * 入院总数-男
   */
  admitted_male: number;
  /**
   * 入院总数
   */
  admitted_quota: number;
  /**
   * 团队总数-女
   */
  group_female: number;
  /**
   * 团队总数-男
   */
  group_male: number;
  /**
   * 团队总数
   */
  group_quota: number;
  /**
   * 个人总数-女
   */
  individual_female: number;
  /**
   * 个人总数-男
   */
  individual_male: number;
  /**
   * 个人总数
   */
  individual_quota: number;
  /**
   * 机构id
   */
  institution: number;
  /**
   * 预约项目
   */
  phy_quota_project: PhyQuotaProject[];
  /**
   * 预约时段
   */
  phy_quota_time_slot: PhyQuotaTimeSlot[];
  /**
   * 日期，格式为'2025-03-11'
   */
  quota_date: string;
  /**
   * 状态/是否休息，active/inactive
   */
  status: string;
  /**
   * 总人数-女
   */
  total_female: number;
  /**
   * 总人数-男
   */
  total_male: number;
  /**
   * 总人数
   */
  total_quota: number;
  /**
   * 周几
   */
  weekday: number;
}

export interface PhyQuotaProject {
  /**
   * 女性总数
   */
  female_quota: number;
  /**
   * 机构编码
   */
  institution_code?: string;
  /**
   * 男性总数
   */
  male_quota: number;
  /**
   * 项目编码
   */
  project: string;
  /**
   * 类型，res_project
   */
  quota_type: string;
  /**
   * 总人数
   */
  total_quota: number;
}

export interface PhyQuotaTimeSlot {
  /**
   * 结束时间，格式 ‘15:17:15’
   */
  end_time: string;
  /**
   * 女性总数
   */
  female_quota: number;
  /**
   * 男性总数
   */
  male_quota: number;
  /**
   * 类型，res_interval
   */
  quota_type: string;
  /**
   * 开始时间，格式 ‘13:17:15’
   */
  start_time: string;
  /**
   * 总人数
   */
  total_quota: number;
}

export type NumberSourceDetail = {
  id: number;
  institution: {
    id: number;
    institution_code: string;
    institution_name: string;
  };
  quota_date: string;
  weekday: number;
  total_quota: number;
  total_male: number;
  total_female: number;
  used_total_quota: number;
  used_total_male: number;
  used_total_female: number;
  group_quota: number;
  group_male: number;
  group_female: number;
  used_group_quota: number;
  used_group_male: number;
  used_group_female: number;
  individual_quota: number;
  individual_male: number;
  individual_female: number;
  used_individual_quota: number;
  used_individual_male: number;
  used_individual_female: number;
  admitted_quota: number;
  admitted_male: number;
  admitted_female: number;
  used_admitted_quota: number;
  used_admitted_male: number;
  used_admitted_female: number;
  status: 'active' | 'inactive';
  phy_quota_time_slot: {
    start_time: string;
    end_time: string;
    quota_type: 'res_interval';
    total_quota: number;
    male_quota: number;
    female_quota: number;
    used_total_quota: number;
    used_male_quota: number;
    used_female_quota: number;
  }[];
  phy_quota_project: {
    project: string;
    name: string;
    institution: {
      id: number;
      institution_code: string;
      institution_name: string;
    };
    quota_type: 'res_project';
    total_quota: number;
    male_quota: number;
    female_quota: number;
    used_total_quota: number;
    used_male_quota: number;
    used_female_quota: number;
  }[];
};
