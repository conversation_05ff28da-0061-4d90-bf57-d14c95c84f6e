import React, { useEffect, useMemo, useState } from 'react';
import {
  <PERSON>ton,
  Card,
  Checkbox,
  DatePicker,
  Form,
  message,
  Modal,
  Select,
  Table,
  Tag,
  TimePicker,
  Typography,
} from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import type { ColumnType } from 'antd/lib/table';
import { EditableRow } from '@/pages/NumberSourceManage/components/EditableRow';
import EditableCell from '@/pages/NumberSourceManage/components/EditableCell';
import { cloneDeep } from 'lodash';
import type { RangeValue } from 'rc-picker/lib/interface';
import PackageProjectSelector from '@/components/PackageProjectSelect';
import type { ProjectListItem } from '@/pages/PackageManage/ProjectManage/type';
import type { CheckboxValueType } from 'antd/lib/checkbox/Group';
import type { NumberSourceDetail, NumberSourceSaveParams } from '@/pages/NumberSourceManage/types';
import { getNumberSourceList, saveNumberSource } from '@/pages/NumberSourceManage/services';
import { useModel } from '@@/plugin-model/useModel';

type TimeRangeItem = {
  startTime: Moment;
  endTime: Moment;
};

type ProjectItem = { name: string; code: string };

type TableData = {
  name: string;
  key: string;
  group: string;
  group_type: 'total' | 'male' | 'female';
  [key: string]: any;
};

type CusColumnType = ColumnType<TableData> & {
  editable?: boolean;
};

enum WeekDayEnum {
  'a',
  '周一',
  '周二',
  '周三',
  '周四',
  '周五',
  '周六',
  '周日',
}

const { Text } = Typography;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

const weekOptions = [
  { label: '周六', value: 6 },
  { label: '周日', value: 7 },
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
];

const defaultColumns: CusColumnType[] = [
  {
    title: '',
    dataIndex: 'name',
    width: 100,
    fixed: 'left',
    onCell: (_, index) => {
      if (index !== undefined) {
        return { rowSpan: index % 3 === 0 ? 3 : 0 };
      }
      return {};
    },
  },
];

const defaultTotalSource: TableData[] = [
  { name: '总人数', key: 'total', group: 'total', group_type: 'total' },
  { name: '总人数-男', key: 'total', group: 'total', group_type: 'male' },
  { name: '总人数-女', key: 'total', group: 'total', group_type: 'female' },
];

const defaultBaseGroupSource: TableData[] = [
  { name: '团队总数', key: 'group', group: 'base', group_type: 'total' },
  { name: '团队总数-男', key: 'group', group: 'base', group_type: 'male' },
  { name: '团队总数-女', key: 'group', group: 'base', group_type: 'female' },
  { name: '个人总数', key: 'individual', group: 'base', group_type: 'total' },
  { name: '个人总数-男', key: 'individual', group: 'base', group_type: 'male' },
  { name: '个人总数-女', key: 'individual', group: 'base', group_type: 'female' },
  { name: '入住检查', key: 'admitted', group: 'base', group_type: 'total' },
  { name: '入住检查-男', key: 'admitted', group: 'base', group_type: 'male' },
  { name: '入住检查-女', key: 'admitted', group: 'base', group_type: 'female' },
];

const generateTimeRangeSource = (startTime: Moment, endTime: Moment) => {
  const totalRow: TableData = {
    name: `${startTime!.format('HH:mm')}-${endTime!.format('HH:mm')}`,
    key: `${startTime!.format('HH:mm')}_${endTime!.format('HH:mm')}`,
    group: 'time_range',
    group_type: 'total',
  };
  const maleRow: TableData = {
    name: `${startTime!.format('HH:mm')}-${endTime!.format('HH:mm')}-男`,
    key: `${startTime!.format('HH:mm')}_${endTime!.format('HH:mm')}`,
    group: 'time_range',
    group_type: 'male',
  };
  const femaleRow: TableData = {
    name: `${startTime!.format('HH:mm')}-${endTime!.format('HH:mm')}-女`,
    key: `${startTime!.format('HH:mm')}_${endTime!.format('HH:mm')}`,
    group: 'time_range',
    group_type: 'female',
  };
  return { totalRow, maleRow, femaleRow };
};

const generateProjectSource = (code: string, name: string) => {
  const totalRow: TableData = { name: name, key: code, group: 'project', group_type: 'total' };
  const maleRow: TableData = {
    name: `${name}-男`,
    key: code,
    group: 'project',
    group_type: 'male',
  };
  const femaleRow: TableData = {
    name: `${name}-女`,
    key: code,
    group: 'project',
    group_type: 'female',
  };
  return { totalRow, maleRow, femaleRow };
};

const getAddDaysDateStr = (date: Moment, days: number) =>
  date.clone().add(days, 'day').format('YYYY-MM-DD');

const components = {
  body: {
    row: EditableRow,
    cell: EditableCell,
  },
};

const NumberSourceManage: React.FC = () => {
  const { hospitalList, getHospitalOptionsByKey } = useModel('useHospitalModel');
  const [loading, setLoading] = useState(false);
  const [disabledConfig, setDisabledConfig] = useState({
    timeRange: [] as TimeRangeItem[],
    project: [] as ProjectItem[],
    initEndDate: null as null | Moment,
  });
  const [hospitalValue, setHospitalValue] = useState<number>();
  const [showDateWarning, setShowDateWarning] = useState(false);
  const [dateRangeValue, setDateRangeValue] = useState<[Moment, Moment]>([
    moment(),
    moment().add(7, 'day'),
  ]);
  const [dayOf, setDayOf] = useState<number[]>([]);
  // 此属性用于存储被修改的dayOf，以触发输入单元格的渲染更新
  let changedDayOf: number[] = [];
  let timeRangeValue: RangeValue<Moment> = null;
  const [timeRangeList, setTimeRangeList] = useState<TimeRangeItem[]>([]);
  let projectValue: ProjectListItem[] | null = null;
  const [projectList, setProjectList] = useState<ProjectItem[]>([]);
  const [columns, setColumns] = useState<CusColumnType[]>(defaultColumns);
  const [scrollX, setScrollX] = useState({ x: 0 });
  const [totalSource, setTotalSource] = useState<TableData[]>(defaultTotalSource);
  const [baseGroupSource, setBaseGroupSource] = useState<TableData[]>(defaultBaseGroupSource);
  const [timeRangeSource, setTimeRangeSource] = useState<TableData[]>([]);
  const [projectSource, setProjectSource] = useState<TableData[]>([]);
  const institutionCode = useMemo(
    () => hospitalList.find((item) => item.id === hospitalValue)?.institution_code || null,
    [hospitalValue, hospitalList],
  );
  const [showConfigWarning, setShowConfigWarning] = useState(false);
  const dataSource = useMemo(() => {
    return [...totalSource, ...baseGroupSource, ...timeRangeSource, ...projectSource];
  }, [totalSource, baseGroupSource, timeRangeSource, projectSource]);
  const [saveLoading, setSaveLoading] = useState(false);

  const addTimeRange = () => {
    timeRangeValue = null;
    confirm({
      icon: null,
      title: '增加时间段',
      content: (
        <TimePicker.RangePicker
          format="HH:mm"
          placeholder={['开始时间', '结束时间']}
          onChange={(values) => {
            timeRangeValue = values;
          }}
        />
      ),
      onOk() {
        if (!timeRangeValue || timeRangeValue.length < 2) {
          message.warning('请选择时间段');
          return Promise.reject();
        }
        const [startTime, endTime] = [timeRangeValue![0], timeRangeValue![1]];
        const isOverlap = (range1: TimeRangeItem, range2: TimeRangeItem) => {
          return (
            range1.startTime.isBefore(range2.endTime) && range2.startTime.isBefore(range1.endTime)
          );
        };
        for (const item of timeRangeList) {
          if (isOverlap(item, { startTime: startTime as Moment, endTime: endTime as Moment })) {
            message.warning('时间段有重叠，请重新选择');
            return Promise.reject();
          }
        }
        const { totalRow, maleRow, femaleRow } = generateTimeRangeSource(startTime!, endTime!);
        const diffDays = dateRangeValue[1].diff(dateRangeValue[0], 'day');
        for (let i = 0; i <= diffDays; i++) {
          const currentDate = getAddDaysDateStr(dateRangeValue[0], i);
          [totalRow, maleRow, femaleRow].forEach((data) => {
            data[currentDate] = 0;
          });
        }
        setTimeRangeSource([...timeRangeSource, totalRow, maleRow, femaleRow]);
        setTimeRangeList([...timeRangeList, { startTime: startTime!, endTime: endTime! }]);
        return Promise.resolve();
      },
    });
  };

  const removeTimeRange = (startTime: Moment, endTime: Moment) => {
    setTimeRangeList(
      timeRangeList.filter(({ startTime: s }) => s.format('HH:mm') !== startTime.format('HH:mm')),
    );
    setTimeRangeSource(
      timeRangeSource.filter(
        (item) => item.key !== `${startTime.format('HH:mm')}_${endTime.format('HH:mm')}`,
      ),
    );
  };

  const addProject = () => {
    projectValue = null;
    confirm({
      icon: null,
      title: '增加项目',
      width: 500,
      content: (
        <PackageProjectSelector
          institutionCode={institutionCode}
          onChange={(_, selectedProjects) => {
            projectValue = selectedProjects;
          }}
        />
      ),
      onOk() {
        if (!projectValue) return;
        const addItem = projectValue
          .filter((item) => projectList.findIndex((p) => p.code === item.project_code) < 0)
          .map((item) => ({ name: item.project_name, code: item.project_code }));
        setProjectList([...projectList, ...addItem]);
        const addProjectSources: TableData[] = [];
        const diffDays = dateRangeValue[1].diff(dateRangeValue[0], 'day');
        addItem.forEach((item) => {
          const { totalRow, maleRow, femaleRow } = generateProjectSource(item.code, item.name);
          for (let i = 0; i <= diffDays; i++) {
            const currentDate = getAddDaysDateStr(dateRangeValue[0], i);
            [totalRow, maleRow, femaleRow].forEach((data) => {
              data[currentDate] = 0;
            });
          }
          addProjectSources.push(totalRow, maleRow, femaleRow);
        });
        setProjectSource([...projectSource, ...addProjectSources]);
      },
    });
  };

  const removeProject = (code: string) => {
    setProjectList(projectList.filter((item) => item.code !== code));
    setProjectSource(projectSource.filter((item) => item.key !== code));
  };

  /*
   * todo 逻辑有耦合待优化
   *
   * table内的保存操作，由于闭包问题。表格状态数据只能在回调内才能获取到最新的值。
   * */
  const handleCellSave = (record: TableData, dataIndex: string) => {
    const otherGender = record.group_type === 'female' ? 'male' : 'female';
    if (record.group === 'base') {
      let totalNumber = 0,
        totalNumberOfGenders = 0;
      // 这里只有在回调里才能获取到状态的最新值
      setBaseGroupSource((prevState) => {
        const newBaseGroup = cloneDeep(prevState);
        const otherGenderItem = newBaseGroup.find(
          (item) => item.key === record.key && item.group_type === otherGender,
        )!;
        const totalCount = (record[dataIndex] + otherGenderItem[dataIndex]) as number;
        newBaseGroup.forEach((item) => {
          if (item.group_type === 'total') {
            if (item.key === record.key) {
              item[dataIndex] = totalCount;
              totalNumber += totalCount;
            } else {
              totalNumber += item[dataIndex] as number;
            }
          } else if (item.group_type === record.group_type) {
            if (item.key === record.key) {
              item[dataIndex] = record[dataIndex];
              totalNumberOfGenders += record[dataIndex] as number;
            } else {
              totalNumberOfGenders += item[dataIndex] as number;
            }
          }
        });
        return newBaseGroup;
      });
      // 设置总人数
      setTotalSource((prevState) => {
        const newTotal = cloneDeep(prevState);
        newTotal.forEach((item) => {
          if (item.group_type === 'total') {
            item[dataIndex] = totalNumber;
          } else if (item.group_type === record.group_type) {
            item[dataIndex] = totalNumberOfGenders;
          }
        });
        return newTotal;
      });
    } else if (record.group === 'time_range') {
      let prevTotalSource: TableData[];
      setTotalSource((prevState) => {
        prevTotalSource = prevState;
        return prevState;
      });
      let totalNumber = 0,
        totalNumberOfGenders = 0;
      setTimeRangeSource((prevState) => {
        const newTimeRange = cloneDeep(prevState);
        const otherGenderItem = newTimeRange.find(
          (item) => item.key === record.key && item.group_type === otherGender,
        )!;
        const totalCount = (record[dataIndex] + otherGenderItem[dataIndex]) as number;
        newTimeRange.forEach((item) => {
          if (item.group_type === 'total') {
            if (item.key === record.key) {
              item[dataIndex] = totalCount;
              totalNumber += totalCount;
            } else {
              totalNumber += item[dataIndex] as number;
            }
          } else if (item.group_type === record.group_type) {
            if (item.key === record.key) {
              item[dataIndex] = record[dataIndex];
              totalNumberOfGenders += record[dataIndex] as number;
            } else {
              totalNumberOfGenders += item[dataIndex] as number;
            }
          }
        });
        let isValid = true;
        prevTotalSource.forEach((item) => {
          if (item.group_type === 'total') {
            isValid = item[dataIndex] >= totalNumber;
          } else if (item.group_type === record.group_type) {
            isValid = item[dataIndex] >= totalNumberOfGenders;
          }
        });
        if (!isValid) {
          message.error('时间段配置人数总和不能大于总人数');
          return prevState;
        }
        return newTimeRange;
      });
    } else if (record.group === 'project') {
      let prevTotalSource: TableData[];
      setTotalSource((prevState) => {
        prevTotalSource = prevState;
        return prevState;
      });
      setProjectSource((prevState) => {
        const newProject = cloneDeep(prevState);
        const otherGenderItem = newProject.find(
          (item) => item.key === record.key && item.group_type === otherGender,
        )!;
        const totalCount = (record[dataIndex] + otherGenderItem[dataIndex]) as number;
        newProject.forEach((item) => {
          if (item.key === record.key) {
            if (item.group_type === 'total') {
              item[dataIndex] = totalCount;
            } else if (item.group_type === record.group_type) {
              item[dataIndex] = record[dataIndex];
            }
          }
        });
        let isValid = true;
        prevTotalSource.forEach((item) => {
          if (item.group_type === 'total') {
            isValid = item[dataIndex] >= totalCount;
          } else if (item.group_type === record.group_type) {
            isValid = item[dataIndex] >= record[dataIndex];
          }
        });
        if (!isValid) {
          message.error('单项目限制人数不能大于总人数');
          return prevState;
        }
        return newProject;
      });
    }
  };

  const resetTableColumnsConfig = (
    startDate: Moment,
    endDate: Moment,
    options: {
      dayOfArr: number[];
      isAppendData?: boolean;
      isInit?: boolean;
    },
  ) => {
    const diffDays = endDate.diff(startDate, 'day');
    const newColumns = [...defaultColumns];
    const newTotalSource = options.isInit ? [...defaultTotalSource] : [...totalSource];
    const newBaseGroupSource = options.isInit ? [...defaultBaseGroupSource] : [...baseGroupSource];
    const newTimeRangeSource = options.isInit ? [] : [...timeRangeSource];
    const newProjectSource = options.isInit ? [] : [...projectSource];
    const [labelWidth, valueWidth] = [30, 75];
    setScrollX({ x: (diffDays + 1) * (labelWidth + valueWidth) });
    for (let i = 0; i <= diffDays; i++) {
      const currentDate = getAddDaysDateStr(startDate, i);
      const weekday = moment(currentDate).isoWeekday();
      const cellStyle = options.dayOfArr.includes(weekday) ? { backgroundColor: '#d9d9d9' } : {};
      const columnOfOneDay: CusColumnType[] = [
        {
          title: `${currentDate} ${WeekDayEnum[weekday]}`,
          colSpan: 2,
          dataIndex: `${currentDate}_label`,
          align: 'left',
          width: labelWidth,
          render: (_, record) => {
            if (record.group_type === 'male') {
              return '男';
            } else if (record.group_type === 'female') {
              return '女';
            } else {
              return '总';
            }
          },
          onCell: () => ({ style: cellStyle }),
        },
        {
          title: '',
          colSpan: 0,
          dataIndex: currentDate,
          width: valueWidth,
          // eslint-disable-next-line @typescript-eslint/no-loop-func
          shouldCellUpdate: (record, prevRecord) => {
            return (
              record[currentDate] !== prevRecord[currentDate] || changedDayOf.includes(weekday)
            );
          },
          render: (_, record) => (
            <div className="not-editable-cell-value-wrap">{record[currentDate]}</div>
          ),
          onCell: (record) => ({
            record,
            editable:
              !options.dayOfArr.includes(weekday) &&
              record.group_type !== 'total' &&
              record.group !== 'total',
            dataIndex: currentDate,
            children: record[currentDate],
            title: '',
            style: cellStyle,
            handleSave: handleCellSave,
          }),
        },
      ];
      newColumns.push(...columnOfOneDay);
      if (options.isAppendData) {
        [newTotalSource, newBaseGroupSource, newTimeRangeSource, newProjectSource].forEach(
          (source) => {
            source.forEach((data) => {
              data[currentDate] = data[currentDate] || 0;
            });
          },
        );
      }
    }
    setColumns(newColumns);
    if (options.isAppendData) {
      setTotalSource(newTotalSource);
      setBaseGroupSource(newBaseGroupSource);
      setTimeRangeSource(newTimeRangeSource);
      setProjectSource(newProjectSource);
    }
  };

  const disabledDate = (date: Moment) => {
    if (disabledConfig.initEndDate) {
      return date.isAfter(moment().add(30, 'day')) || date.isBefore(disabledConfig.initEndDate);
    }
    return date.isAfter(moment().add(30, 'day')) || date.isBefore(moment().subtract(1, 'day'));
  };

  const dateRangeChange = (dates: [Moment | null, Moment | null] | null) => {
    if (dates && dates[0] && dates[1]) {
      setDateRangeValue(dates as [Moment, Moment]);
      resetTableColumnsConfig(dates[0], dates[1], { dayOfArr: dayOf, isAppendData: true });
    }
  };

  const dayOfChange = (checkedValues: CheckboxValueType[]) => {
    const uniqueInArr1 = dayOf.filter((item) => !checkedValues.includes(item));
    const uniqueInArr2 = (checkedValues as number[]).filter((item) => !dayOf.includes(item));
    // 获取改变前后差异的数据
    changedDayOf = [...uniqueInArr1, ...uniqueInArr2];

    setDayOf(checkedValues as number[]);
    const [startDate, endDate] = dateRangeValue;
    if (startDate && endDate) {
      resetTableColumnsConfig(startDate, endDate, {
        dayOfArr: checkedValues as number[],
        isAppendData: true,
      });
    }
  };

  const initData = (hospitalId: number) => {
    setLoading(true);
    getNumberSourceList({ start_date: moment().format('YYYY-MM-DD'), institution: hospitalId })
      .then((res) => {
        const data = res.data;
        let initTotalSource = cloneDeep(defaultTotalSource);
        let initBaseGroupSource = cloneDeep(defaultBaseGroupSource);
        let initTimeRangeSource: TableData[] = [];
        let initProjectSource: TableData[] = [];
        const initDayOf = new Set<number>();
        const initTimeRange: TimeRangeItem[] = [];
        const initProject: ProjectItem[] = [];
        if (res.code !== 2000 || !res.data || res.data.length === 0) {
          // 如果首次进入没有时间段，则默认添加7天配置数据
          const [rangeStartDate, rangeEndDate] = [moment(), moment().add(7, 'day')];
          resetTableColumnsConfig(rangeStartDate, rangeEndDate, {
            dayOfArr: [],
            isAppendData: true,
            isInit: true,
          });
          setDateRangeValue([rangeStartDate, rangeEndDate]);
          setDisabledConfig({
            timeRange: initTimeRange,
            project: initProject,
            initEndDate: null,
          });
        } else {
          const dataSourceMap: Record<
            string,
            Record<'total' | 'male' | 'female', Record<string, any>>
          > = {};
          const disabledProjectMap: Record<string, boolean> = {};
          const disabledTimeRangeMap: Record<string, boolean> = {};
          data.forEach((item: NumberSourceDetail, index) => {
            if (index === 0) {
              dataSourceMap.total = {
                total: { [item.quota_date]: item.total_quota },
                male: { [item.quota_date]: item.total_male },
                female: { [item.quota_date]: item.total_female },
              };
              dataSourceMap.group = {
                total: { [item.quota_date]: item.group_quota },
                male: { [item.quota_date]: item.group_male },
                female: { [item.quota_date]: item.group_female },
              };
              dataSourceMap.individual = {
                total: { [item.quota_date]: item.individual_quota },
                male: { [item.quota_date]: item.individual_male },
                female: { [item.quota_date]: item.individual_female },
              };
              dataSourceMap.admitted = {
                total: { [item.quota_date]: item.admitted_quota },
                male: { [item.quota_date]: item.admitted_male },
                female: { [item.quota_date]: item.admitted_female },
              };
            } else {
              dataSourceMap.total.total[item.quota_date] = item.total_quota;
              dataSourceMap.total.male[item.quota_date] = item.total_male;
              dataSourceMap.total.female[item.quota_date] = item.total_female;
              dataSourceMap.group.total[item.quota_date] = item.group_quota;
              dataSourceMap.group.male[item.quota_date] = item.group_male;
              dataSourceMap.group.female[item.quota_date] = item.group_female;
              dataSourceMap.individual.total[item.quota_date] = item.individual_quota;
              dataSourceMap.individual.male[item.quota_date] = item.individual_male;
              dataSourceMap.individual.female[item.quota_date] = item.individual_female;
              dataSourceMap.admitted.total[item.quota_date] = item.admitted_quota;
              dataSourceMap.admitted.male[item.quota_date] = item.admitted_male;
              dataSourceMap.admitted.female[item.quota_date] = item.admitted_female;
            }
            if (item.phy_quota_project && item.phy_quota_project.length) {
              item.phy_quota_project.forEach((p) => {
                if (index === 0) {
                  const { totalRow, maleRow, femaleRow } = generateProjectSource(p.project, p.name);
                  initProjectSource.push(totalRow, maleRow, femaleRow);
                  dataSourceMap[p.project] = {
                    total: { [item.quota_date]: p.total_quota },
                    male: { [item.quota_date]: p.male_quota },
                    female: { [item.quota_date]: p.female_quota },
                  };
                  initProject.push({ code: p.project, name: p.name });
                } else {
                  dataSourceMap[p.project].total[item.quota_date] = p.total_quota;
                  dataSourceMap[p.project].male[item.quota_date] = p.male_quota;
                  dataSourceMap[p.project].female[item.quota_date] = p.female_quota;
                }
                // 项目如果配置了预约人数，则不能被删除
                if (p.total_quota > 0) {
                  disabledProjectMap[p.project] = true;
                }
              });
            }
            if (item.phy_quota_time_slot && item.phy_quota_time_slot.length) {
              item.phy_quota_time_slot.forEach((t) => {
                const [startTime, endTime] = [
                  moment(t.start_time, 'HH:mm'),
                  moment(t.end_time, 'HH:mm'),
                ];
                const key = `${startTime.format('HH:mm')}_${endTime.format('HH:mm')}`;
                if (index === 0) {
                  const { totalRow, maleRow, femaleRow } = generateTimeRangeSource(
                    startTime,
                    endTime,
                  );
                  initTimeRangeSource.push(totalRow, maleRow, femaleRow);
                  dataSourceMap[key] = {
                    total: { [item.quota_date]: t.total_quota },
                    male: { [item.quota_date]: t.male_quota },
                    female: { [item.quota_date]: t.female_quota },
                  };
                  initTimeRange.push({
                    startTime: moment(t.start_time, 'HH:mm'),
                    endTime: moment(t.end_time, 'HH:mm'),
                  });
                } else {
                  dataSourceMap[key].total[item.quota_date] = t.total_quota;
                  dataSourceMap[key].male[item.quota_date] = t.male_quota;
                  dataSourceMap[key].female[item.quota_date] = t.female_quota;
                }
                // 时间段如果配置了预约人数，则不能被删除
                if (t.total_quota > 0) {
                  disabledTimeRangeMap[startTime.format('HH:mm')] = true;
                }
              });
            }
            if (item.status === 'inactive') {
              initDayOf.add(item.weekday as number);
            }
          });
          const mapLoopFunc = (item: TableData) => ({
            ...item,
            ...dataSourceMap[item.key][item.group_type],
          });
          initTotalSource = initTotalSource.map(mapLoopFunc);
          initBaseGroupSource = initBaseGroupSource.map(mapLoopFunc);
          initTimeRangeSource = initTimeRangeSource.map(mapLoopFunc);
          initProjectSource = initProjectSource.map(mapLoopFunc);
          const [rangeStartDate, rangeEndDate] = [
            moment(data[0].quota_date),
            moment(data[data.length - 1].quota_date),
          ];
          setDateRangeValue([rangeStartDate, rangeEndDate]);
          resetTableColumnsConfig(rangeStartDate, rangeEndDate, {
            dayOfArr: Array.from(initDayOf),
            isAppendData: false,
          });
          setDisabledConfig({
            timeRange: initTimeRange.filter(
              (item) => disabledTimeRangeMap[item.startTime.format('HH:mm')],
            ),
            project: initProject.filter((item) => disabledProjectMap[item.code]),
            initEndDate: rangeEndDate,
          });
          setTotalSource(initTotalSource);
          setBaseGroupSource(initBaseGroupSource);
          setTimeRangeSource(initTimeRangeSource);
          setProjectSource(initProjectSource);
        }
        setDayOf(Array.from(initDayOf));
        setTimeRangeList(initTimeRange);
        setProjectList(initProject);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const hospitalChange = (value: number) => {
    setHospitalValue(value);
    initData(value);
  };

  const onSave = async () => {
    setSaveLoading(true);
    const diffDays = dateRangeValue[1].diff(dateRangeValue[0], 'day');
    const dataSourceMap: Record<string, Record<'total' | 'male' | 'female', TableData | null>> = {};
    dataSource.forEach((item) => {
      if (!dataSourceMap[item.key]) {
        const obj: Record<'total' | 'male' | 'female', TableData | null> = {
          total: null,
          female: null,
          male: null,
        };
        obj[item.group_type] = item;
        dataSourceMap[item.key] = obj;
      } else {
        dataSourceMap[item.key][item.group_type] = item;
      }
    });
    const params: NumberSourceSaveParams[] = [];
    for (let i = 0; i <= diffDays; i++) {
      const currentDateStr = getAddDaysDateStr(dateRangeValue[0], i);
      const weekday = moment(currentDateStr).isoWeekday();
      const param: NumberSourceSaveParams = {
        institution: hospitalValue!,
        quota_date: currentDateStr,
        weekday,
        status: dayOf.includes(weekday) ? 'inactive' : 'active',
        total_quota: dataSourceMap.total.total![currentDateStr],
        total_male: dataSourceMap.total.male![currentDateStr],
        total_female: dataSourceMap.total.female![currentDateStr],
        group_quota: dataSourceMap.group.total![currentDateStr],
        group_male: dataSourceMap.group.male![currentDateStr],
        group_female: dataSourceMap.group.female![currentDateStr],
        individual_quota: dataSourceMap.individual.total![currentDateStr],
        individual_male: dataSourceMap.individual.male![currentDateStr],
        individual_female: dataSourceMap.individual.female![currentDateStr],
        admitted_quota: dataSourceMap.admitted.total![currentDateStr],
        admitted_male: dataSourceMap.admitted.male![currentDateStr],
        admitted_female: dataSourceMap.admitted.female![currentDateStr],
        phy_quota_time_slot: timeRangeList.map(({ startTime, endTime }) => {
          const [startTimeStr, endTimeStr] = [startTime.format('HH:mm'), endTime.format('HH:mm')];
          const key = `${startTimeStr}_${endTimeStr}`;
          return {
            start_time: startTimeStr,
            end_time: endTimeStr,
            quota_type: 'res_interval',
            total_quota: dataSourceMap[key].total![currentDateStr],
            male_quota: dataSourceMap[key].male![currentDateStr],
            female_quota: dataSourceMap[key].female![currentDateStr],
          };
        }),
        phy_quota_project: projectList.map(({ code }) => ({
          project: code,
          institution_code: hospitalList.find((item) => item.id === hospitalValue!)!
            .institution_code,
          quota_type: 'res_project',
          total_quota: dataSourceMap[code].total![currentDateStr],
          male_quota: dataSourceMap[code].male![currentDateStr],
          female_quota: dataSourceMap[code].female![currentDateStr],
        })),
      };
      params.push(param);
    }
    const res = await saveNumberSource(params).finally(() => setSaveLoading(false));
    if (res.code === 2000) {
      message.success('保存成功');
      initData(hospitalValue!);
    } else {
      message.error(res.message || '保存失败');
    }
  };

  useEffect(() => {
    if (hospitalList.length > 0) {
      setHospitalValue(hospitalList[0].id);
      initData(hospitalList[0].id);
    }
  }, [hospitalList]);

  useEffect(() => {
    const [startDate, endDate] = dateRangeValue;
    const diffDays = endDate.diff(startDate, 'day');
    let isWarning = false;
    for (let i = 0; i <= diffDays; i++) {
      const currentDate = getAddDaysDateStr(startDate, i);
      if (totalSource[0][currentDate] === 0) {
        isWarning = true;
        break;
      }
    }
    setShowConfigWarning(isWarning);
  }, [totalSource]);

  useEffect(() => {
    const diffInDays = dateRangeValue[1].diff(dateRangeValue[0], 'days');
    setShowDateWarning(diffInDays < 30);
  }, [dateRangeValue]);

  useEffect(() => {
    setLoading(true);
  }, []);

  return (
    <Card className="base-content" loading={loading}>
      <Form.Item
        label="预约机构设置"
        labelCol={{ span: 2 }}
        labelAlign="left"
        style={{ marginBottom: '1rem' }}
      >
        <Select
          style={{ width: 200 }}
          options={getHospitalOptionsByKey('id')}
          value={hospitalValue}
          onChange={(value) => hospitalChange(value)}
          placeholder="请设置预约机构"
        />
      </Form.Item>
      <Form.Item
        label="预约日期设置"
        labelCol={{ span: 2 }}
        labelAlign="left"
        style={{ marginBottom: '1rem' }}
        validateStatus={showDateWarning ? 'warning' : ''}
        help={showDateWarning ? '号源配置日期范围未满一个月，请注意配置时间。' : ''}
      >
        <RangePicker
          value={dateRangeValue}
          disabled={[true, false]}
          disabledDate={disabledDate}
          onChange={dateRangeChange}
        />
      </Form.Item>
      <Form.Item
        label="自定义休息日"
        labelCol={{ span: 2 }}
        labelAlign="left"
        style={{ marginBottom: '1rem' }}
      >
        <Checkbox.Group
          value={dayOf}
          options={weekOptions}
          defaultValue={dayOf}
          onChange={dayOfChange}
        />
      </Form.Item>
      <Form.Item
        label="预约时段设置"
        labelCol={{ span: 2 }}
        labelAlign="left"
        style={{ marginBottom: '1rem' }}
      >
        {timeRangeList.map(({ startTime, endTime }) => (
          <Tag
            style={{ marginBottom: 3, marginTop: 3 }}
            key={startTime.format('HH:mm')}
            color="blue"
            closable={
              disabledConfig.timeRange.findIndex((item) => item.startTime.isSame(startTime)) < 0
            }
            onClose={() => removeTimeRange(startTime, endTime)}
          >
            {startTime.format('HH:mm')}-{endTime.format('HH:mm')}
          </Tag>
        ))}
        <Button type="primary" size="small" onClick={addTimeRange}>
          增加时间段
        </Button>
      </Form.Item>
      <Form.Item
        label="预约项目设置"
        labelCol={{ span: 2 }}
        labelAlign="left"
        style={{ marginBottom: '1rem' }}
      >
        {projectList.map((project) => (
          <Tag
            style={{ marginBottom: 3, marginTop: 3 }}
            key={project.code}
            color="blue"
            closable={disabledConfig.project.findIndex((item) => item.code === project.code) < 0}
            onClose={() => removeProject(project.code)}
          >
            {project.name}
          </Tag>
        ))}
        <Button type="primary" size="small" onClick={addProject}>
          增加项目
        </Button>
      </Form.Item>
      <Form.Item
        label={
          <>
            预约人数设置：
            {showConfigWarning && (
              <Text type="danger">存在配置号源为空的预约日期，请及时配置。</Text>
            )}
          </>
        }
        labelAlign="left"
        wrapperCol={{ span: 24 }}
        colon={false}
      >
        <Table
          rowKey="name"
          columns={columns}
          components={components}
          scroll={scrollX}
          dataSource={dataSource}
          bordered
          size="small"
          pagination={false}
        />
      </Form.Item>

      <div style={{ textAlign: 'right' }}>
        <Button
          style={{ width: 80 }}
          size="large"
          type="primary"
          loading={saveLoading}
          onClick={onSave}
        >
          保 存
        </Button>
      </div>
    </Card>
  );
};

export default NumberSourceManage;
