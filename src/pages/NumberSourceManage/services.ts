import request from '@/utils/request';
import type { NumberSourceDetail, NumberSourceSaveParams } from '@/pages/NumberSourceManage/types';

export const getNumberSourceList = (params: { start_date?: string; end_date?: string; institution: string | number }) => {
  return request<API.Response<NumberSourceDetail[]>>('/api/quota/daily_data/list/', {
    method: 'GET',
    params: { ...params },
  });
};

export const saveNumberSource = (params: NumberSourceSaveParams[]) => {
  return request<API.Response>('/api/quota/daily_data/', {
    method: 'POST',
    data: params,
    headers: { 'Content-Type': 'application/json' },
  });
};
