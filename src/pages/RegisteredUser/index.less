@import '~antd/es/style/themes/default.less';

.RegisteredUser {
  margin: 16px;
  background-color: #fff;

  .listContent {
    padding: 24px 24px 6px 24px;

    #role {
      height: 680px;
    }

    :global {
      .ant-pagination {
        position: absolute;
        left: 50% !important;
        transform: translateX(-50%);
      }

      .ant-pagination-item-active {
        background-color: #0066ff;
      }

      td.ant-table-column-sort {
        background: none;
      }

      .ant-cascader-input.ant-input {
        border: 1px solid #e4e7ed !important;
      }

      .ant-table-container table>thead>tr:first-child th:first-child {
        padding-left: 24px !important;
      }

      .ant-table-tbody>tr td:first-child {
        padding-left: 24px !important;
      }

      .ant-table-thead>tr>th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
        width: 0;
      }

      .ant-pagination-item-active a {
        color: white;
        background-color: rgba(0, 102, 255, 1);
      }

      .ant-pagination-item-active a:hover {
        color: white;
        background-color: rgba(0, 102, 255, 1);
      }

      .ant-table-thead>tr>th,
      .ant-table-tbody>tr>td,
      .ant-table tfoot>tr>th,
      .ant-table tfoot>tr>td {
        padding: 12px 10px;
      }

      .ant-table-thead>tr>th {
        background-color: rgba(239, 241, 247, 1);
        color: rgba(96, 98, 102, 1) !important;
      }

      .ant-table-column-title {
        color: rgba(96, 98, 102, 1) !important;
      }

      .ant-table-thead th.ant-table-column-has-sorters:hover {
        background-color: #d0d6ee;
      }

      .ant-table-thead th.ant-table-column-sort {
        background: #d0d6ee;
      }

      .ant-table-column-sorters {
        display: block;
      }

      .ant-table-column-sorter-inner {
        position: relative;
        top: -2px;
        left: 16px;
      }
    }
  }
}

.ageInput {
  width: 40%;
  height: 32px;
  padding: 0;
  text-align: center;
  border: 1px solid #e4e7ed;
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.ageInput:hover {
  border: 1px solid rgba(0, 102, 255, 1);
}

.ageInput::-webkit-input-placeholder {
  color: #c0c4cc;
}

.company {
  width: 360px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.operation {
  // width: 8rem;
  color: #06f;
  cursor: pointer;

  a {
    margin: 0 5px;
  }
}

.colorOne>td {
  padding: 8px !important;
}

.colorTwo {
  padding: 8px;
}

.colorTwo>td {
  padding: 8px !important;
}

.ListTitle {
  display: flex;
  justify-content: space-between;
  height: auto;
  margin-bottom: -10px !important;
  padding: 20px 24px 0 24px;
  border-bottom: 1px solid rgba(228, 231, 237, 1);

  .titleLeft {
    flex: 1444px;

    .label {
      width: 60px;
      margin-right: 12px;
      color: #606266;
      font-weight: 400;
      font-size: 12px;
    }
  }

  .titleRight {
    flex: 180px;
    font-size: 14px;

    .button {
      float: right;
      display: flex;
    }

    .reset {
      flex: 1;
      height: 32px;
      margin-right: 16px;
      border: 1px solid #e4e7ed;
      border-radius: 2px;
      outline: none;
      cursor: pointer;
    }
  }
}

:global {
  .ant-spin-nested-loading>div>.ant-spin {
    top: 100px;
  }

  .ant-form-item-label>label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
    opacity: 0;
  }

  .ant-modal-content {
    // width: 640px;
    .ant-modal-header {
      nav {
        display: flex;
        align-items: center;
        font-size: 16px;

        span {
          width: 4px;
          height: 16px;
          display: block;
          margin-right: 5px;
          background-color: #2B64F6;
        }
      }
    }

    .ant-modal-body {
      nav {
        display: flex;
        width: 180px;
        margin: 0 auto;

        :global {
          .ant-btn {
            width: 74px;
            height: 33px;
            padding: 0;
            margin: 0 8px;
            border-radius: 3px;
          }
        }
      }
    }
  }
}
:global{
  .ant-btn{
    height: 36.0px !important;
  }
}
