import React, { useEffect, useState } from 'react'
import styles from './index.less'
import { Button, Col, Form, Input, Modal, Row, Select, Table, DatePicker, Popconfirm, message } from 'antd';
import moment from 'moment';
import { RegisteredUserDel, RegisteredUserList, RestUserCertificate } from '@/services/registeredUser';
const formItemLayout = {
  labelCol: { xs: { span: 24 }, sm: { span: 10 }, },
  wrapperCol: { xs: { span: 15 }, sm: { span: 20 }, },
};

const RegisteredUser: React.FC = () => {
  const dateFormat = 'YYYY-MM-DD';
  // 筛选表单
  const [form] = Form.useForm();
  // 编辑表单
  const [editForm] = Form.useForm();
  // 分页器 数据
  const [paginationData, setPaginationData] = useState({ pageSize: 14, total: 0, current: 1, data: [] })
  const [currentpage, setcurrentpage] = useState(1)
  // 弹框显示控制
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 弹框名称
  const [modalTitle, setModalTitle] = useState('');
  // 筛选内容
  const [searchData, setSearchData] = useState({
    name: '',
    phone: '',
    identity_type: '',
    identity_number: '',
    report_num_min: '',
  })
  // 选中的id
  const [ControlsId, setControlsId] = useState('')
  // 列表所需参数
  const columns = [
    { title: '注册流水号', dataIndex: 'id', width: '10%', align: 'center' },
    { title: '姓名', dataIndex: 'name', width: '10%', align: 'center' },
    {
      title: '证件类型', dataIndex: 'identity_type', width: '11%', align: 'center',
      render: (_: any, e: any) => { return <span>{_ == 'ID_card' ? '身份证' : _ == 'passport' ? '护照' : ''}</span> }
    },
    { title: '证件号（用户ID）', dataIndex: 'identify_number', width: '12%', align: 'center' },
    { title: '手机号', dataIndex: 'phone', width: '12%', align: 'center' },
    {
      title: '注册时间', dataIndex: 'register_time', width: '10%', align: 'center',
      render: (_: any, e: any) => { return <span>{_ ? moment(_).format('YYYY-MM-DD') : '无'}</span> }
    },
    {
      title: '上次体检时间', dataIndex: 'last_check_time', width: '10%', align: 'center',
      render: (_: any, e: any) => { return <span>{_ ? moment(_).format('YYYY-MM-DD') : '无'}</span> }
    },
    {
      title: '报告数量', dataIndex: 'checkup_numbers', width: '10%', align: 'center',
      render: (_: any, e: any) => { return <span>{_ ? _ : '无报告'}</span> }
    },
    {
      title: '操作', dataIndex: 'operation', width: '15%', align: 'center',
      render: (_: any, e: any) => {
        return (
          <div key={e.commercial_code} className={styles.operation}>
            <a onClick={() => {
              editForm.resetFields()
              editForm.setFieldsValue({
                serial_number: e.id,
                name: e.name,
                certificate_type: e.identity_type,
                last_time: e.latest_check_time ? moment(e.latest_check_time.split('T')[0], 'YYYY-MM-DD') : '',
                phone: e.phone,
                id_number: e.identify_number,
                registered_time: e.register_time ? moment(e.register_time.split('T')[0], 'YYYY-MM-DD') : ''
              })
              setModalTitle('编辑用户')
              setControlsId(e.id)
              setIsModalOpen(true)
            }}>编辑</a>
            <a onClick={() => {
              setModalTitle('警告')
              setIsModalOpen(true)
              setControlsId(e.id)
            }}>删除</a>
          </div>
        );
      },
    },
  ];
  // 注册用户列表
  const RegisteredUserListFun = (filterObj: any, current: any) => {
    const obj = { ...filterObj, page: String(current), num: paginationData.pageSize }
    RegisteredUserList({ ...obj }).then(res => {
      setPaginationData({ ...paginationData, total: res.count, data: res.results })
    })
  }

  useEffect(() => {
    RegisteredUserListFun(searchData, 1)
  }, [])
  return (
    <div className={styles.RegisteredUser}>
      <Form form={form} onFinish={(e) => {
        let obj: any = { ...e }
        if (e.report_num_min != undefined && e.report_num_max == undefined) {
          obj = { ...obj, report_num_max: 100 } // 默认值100
        }
        setSearchData({ ...obj })
        RegisteredUserListFun(obj, 1)
      }} >
        <div className={styles.ListTitle}>
          <div className={styles.titleLeft}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div style={{ flex: 1 }}>
                <Form.Item name={'name'} label={'姓名:'}><Input placeholder={'请输入姓名'} style={{ width: '80%' }} /></Form.Item>
              </div>
              <div style={{ flex: 1 }}>
                <Form.Item name={'phone'} label={'手机号:'}><Input placeholder={'请输入手机号'} style={{ width: '80%' }} /></Form.Item>
              </div>
              <div style={{ flex: 1 }}>
                <Form.Item name={'identity_type'} label={'证件类型:'}>
                  <Select placeholder={'请选择证件类型'} style={{ width: '80%' }} options={[
                    { label: '身份证', value: 'ID_card' },
                    { label: '护照', value: 'passport' }
                  ]} />
                </Form.Item>
              </div>
              <div style={{ flex: 1 }}>
                <Form.Item name={'identify_number'} label={'证件号:'}>
                  <Input placeholder={'请输入证件号'} style={{ width: '80%' }} />
                </Form.Item></div>
              <div style={{ flex: 1 }}>
                <Form.Item label={'报告数量:'}>
                  <Form.Item name="report_num_min" rules={[{ required: false }]} noStyle>
                    <input className={styles.ageInput} placeholder="输入数量" />
                  </Form.Item>
                  <Form.Item noStyle><span style={{ margin: '0 4px' }}>~</span></Form.Item>
                  <Form.Item name="report_num_max" rules={[{ required: false }]} noStyle>
                    <input className={styles.ageInput} placeholder="输入数量" />
                  </Form.Item>
                </Form.Item>{' '}
              </div>
            </div>
          </div>
          <div className={styles.titleRight}>
            <div className={styles.button}>
              <Button className={styles.reset} type="primary" htmlType="submit"> 筛 选 </Button>
              <Button className={styles.choose} onClick={() => {
                form.resetFields();
                RegisteredUserListFun({}, currentpage)
                setSearchData({
                  name: '',
                  phone: '',
                  identity_type: '',
                  identity_number: '',
                  report_num_min: '',
                })
              }}> 重 置 </Button>
            </div>
          </div>
        </div>
      </Form>
      <div className={styles.listContent}>
        <Modal title={<nav className={styles.ModalHeader}><span></span><b>{modalTitle}</b></nav>} open={isModalOpen}
          closeIcon={true}
          centered={true}
          width={modalTitle == '警告' ? '400px' : '650px'}
          footer={null}>
          {/* 删除框 */}
          {modalTitle == '警告' ? <div>
            <div style={{ display: 'inline-block', textAlign: 'center', width: '100%', margin: '30px 0' }}>
              <img style={{ width: '36px', height: '36px', marginBottom: '27px' }} src={require('../../../public/registereuser/deteleimg.png')} alt="" />
              <p style={{ fontSize: '14px' }}>确定要删除用户！</p>
            </div>
            <nav>
              <Button onClick={() => setIsModalOpen(false)}> 取消 </Button>
              <Button type="primary" onClick={() => {
                setIsModalOpen(false)
                RegisteredUserDel({ register_user_ids: [ControlsId] }).then(res => {
                  if (res.code == 2004) {
                    RegisteredUserListFun(searchData, paginationData.current)
                    message.success('删除成功!')
                  }
                })
              }}> 确定 </Button>
            </nav>
          </div> : <Form name="nest-messages" form={editForm} {...formItemLayout} labelAlign={'right'} autoComplete="off"
            onFinish={() => { setIsModalOpen(false) }}
            style={{ width: '100%' }}>
            <Row>
              <Col>
                <Form.Item name={'name'} label="姓名" rules={[{ required: true }]}><Input placeholder='请输入姓名' disabled={true} /></Form.Item>
                <Form.Item name={'certificate_type'} label="证件类型">
                  <Select disabled={true} placeholder={'请选择证件类型'} style={{ width: '100%' }} options={[
                    { label: '身份证', value: 'ID_card' },
                    { label: '护照', value: 'passport' }
                  ]} />
                </Form.Item>
                <Popconfirm placement="topLeft" title={'您确定要重置此用户证件吗？'} onConfirm={() => {
                  RestUserCertificate({ register_user_id: ControlsId }).then(res => {
                    if (res.code == 2000) {
                      RegisteredUserListFun(searchData, paginationData.current)
                      editForm.setFieldValue('certificate_type', undefined)
                      editForm.setFieldValue('id_number', '')
                      message.success('重置证件成功!')
                    }
                  })
                }} okText="确定" cancelText="取消">
                  <p style={{ color: '#2B64F6', fontSize: '12px', cursor: 'pointer', position: 'absolute', margin: '-20px 0 0 41.5%' }}>重置用户证件</p>
                </Popconfirm>
                <Form.Item name={'serial_number'} label="注册流水号" rules={[{ required: true }]}><Input disabled={true} placeholder={'请输入注册流水号'} /></Form.Item>
                <Form.Item name={'last_time'} label="上次体检时间" rules={[{ required: true }]}>
                  <DatePicker format={dateFormat} disabled={true} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col>
                <Form.Item name={'phone'} label="手机号码" rules={[{ required: true }]}><Input disabled={true} placeholder='请输入手机号码' /></Form.Item>
                <Form.Item name={'id_number'} label="证件号"><Input disabled={true} placeholder={'请输入证件号'} /></Form.Item>
                <Form.Item name={'registered_time'} label="注册时间" rules={[{ required: true }]}>
                  <DatePicker format={dateFormat} disabled={true} style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>
            <nav>
              <Form.Item><Button onClick={() => setIsModalOpen(false)}> 取消 </Button></Form.Item>
              <Form.Item><Button type="primary" onClick={() => setIsModalOpen(false)}> 确定 </Button></Form.Item>  {/* htmlType="submit" */}
            </nav>
          </Form>}
        </Modal>
        {/* 列表 */}
        <Table
          columns={columns}
          id={styles.role}
          rowKey={(e) => { return e.serial_number; }}
          pagination={{
            style: { position: 'absolute', bottom: '1rem', left: '36%' },
            hideOnSinglePage: true,
            showSizeChanger: false,
            total: paginationData.total,
            pageSize: paginationData.pageSize,
            onChange: (e) => {
              setcurrentpage(e)
              RegisteredUserListFun(searchData, e)
            }
          }}
          dataSource={paginationData.data}
          onChange={(e) => { }}
          style={{ minHeight: 520 }}
          rowClassName={(_, i): string => {
            if (i % 2 === 0) {
              return styles.colorOne;
            }
            return styles.colorTwo;
          }}
        />
      </div>
    </div>
  )
}
export default RegisteredUser
