import React, { useRef } from 'react';
import {
  type ActionType,
  type ListToolBarProps,
  type ProColumns,
  ProTable,
} from '@ant-design/pro-table';
import type { SearchConfig } from '@ant-design/pro-table/lib/components/Form/FormRender';
import { Button, Select } from 'antd';
import type { TeamListItem, TeamListParams } from '@/pages/PatientManage/type';
import { getTeamList } from '@/pages/PatientManage/services';
import { useModel } from '@@/plugin-model/useModel';
import useInstitution from '@/hooks/useInstitution';

const TeamManage: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const { getHospitalOptionsByKey } = useModel('useHospitalModel');
  const {isInstitution, institutionCode} = useInstitution()

  const initTableData = async (
    params: TeamListParams & {
      pageSize: number;
      current: number;
    },
  ) => {
    const { current, pageSize, ...restParams } = params;
    if (isInstitution){
      restParams.institution = institutionCode
    }
    const res = await getTeamList({
      ...restParams,
      page_size: pageSize,
      page_num: current,
    });
    return { data: res.data, success: true, total: res.total };
  };

  const searchConfig: SearchConfig = {
    labelWidth: 'auto',
    span: 4,
    optionRender: (config) => [
      <Button key="submit" type="primary" onClick={() => config?.form?.submit()}>
        筛选
      </Button>,
      <Button key="clear" onClick={() => tableRef.current?.reset!()}>
        重置
      </Button>,
    ],
  };

  const toolBarConfig: ListToolBarProps = {
    settings: [],
    search: false,
  };

  const columns: ProColumns<TeamListItem>[] = [
    {
      title: '团队ID',
      hideInSearch: true,
      dataIndex: 'id',
      align: 'center',
    },
    {
      title: '团队名称',
      dataIndex: 'team_name',
      align: 'center',
      colSize: 1.5,
    },
    {
      title: '是否在用',
      hideInSearch: true,
      dataIndex: 'status',
      align: 'center',
      valueEnum: {
        active: '是',
        inactive: '否',
      },
    },
    {
      title: '团队所属机构',
      hideInSearch: true,
      dataIndex: 'institution_name',
      align: 'center',
    },
    {
      title: '团队所属机构',
      dataIndex: 'institution',
      align: 'center',
      hideInSearch: isInstitution,
      hideInTable: true,
      colSize: 1.5,
      renderFormItem: () => (
        <Select placeholder="请选择" allowClear options={getHospitalOptionsByKey('institution_code')} />
      ),
    },
  ];

  return (
    <div className="base-content">
      <ProTable
        rowKey="id"
        actionRef={tableRef}
        search={searchConfig}
        columns={columns}
        toolbar={toolBarConfig}
        request={initTableData}
        pagination={{ defaultPageSize: 10 }}
      />
    </div>
  );
};

export default TeamManage;
