import React, { useRef, useState } from 'react';
import {
  type ActionType,
  type ListToolBarProps,
  type ProColumns,
  ProTable,
} from '@ant-design/pro-table';
import type { SearchConfig } from '@ant-design/pro-table/lib/components/Form/FormRender';
import { Button, message, Space } from 'antd';
import BatchEditDrawer from '@/pages/PatientManage/BatchManage/components/BatchEditDrawer';
import type { BatchListItem, BatchListParams, EditBatchParams } from '@/pages/PatientManage/type';
import {editBatchInfo, getBatchList, synchronizeBatchData} from '@/pages/PatientManage/services';
import moment from 'moment';
import type { BatchInfoDetailDrawerInstance } from '@/pages/PatientManage/BatchManage/components/BatchDetailDrawer';
import BatchDetailDrawer from '@/pages/PatientManage/BatchManage/components/BatchDetailDrawer';
import useInstitution from '@/hooks/useInstitution';

const BatchManage: React.FC = () => {
  const { isInstitution, institutionCode } = useInstitution();
  const [syncLoading, setSyncLoading] = useState<boolean>(false);
  const tableRef = useRef<ActionType>();
  const [currentBatchId, setCurrentBatchId] = useState<number>();
  const [showDrawer, setShowDrawer] = useState(false);
  const detailModelRef = useRef<BatchInfoDetailDrawerInstance>(null);

  const syncBatch = async () => {
    setSyncLoading(true)
    const res = await synchronizeBatchData().catch(() => {
      message.error('同步失败');
    });
    if (res && res.code === 2000) {
      message.success('同步成功');
      tableRef.current?.reload();
    }
    setSyncLoading(false)
  }

  const initTableData = async (
    params: BatchListParams & {
      pageSize: number;
      current: number;
    },
  ) => {
    const { current, pageSize, ...restParams } = params;
    const res = await getBatchList({
      ...restParams,
      institution: isInstitution ? institutionCode : undefined,
      page_size: pageSize,
      page_num: current,
    });
    return { data: res.data, success: true, total: res.total };
  };

  const openEditInfoModal = (record: BatchListItem) => {
    setCurrentBatchId(record.id);
    setShowDrawer(true);
  };

  const saveBatchInfo = async (data: EditBatchParams) => {
    const res = await editBatchInfo({ ...data });
    if (res.code === 2000) {
      message.success('保存成功');
      tableRef.current?.reload();
      setCurrentBatchId(undefined);
      setShowDrawer(false);
    }
  };

  const searchConfig: SearchConfig = {
    labelWidth: 'auto',
    span: 4,
    optionRender: (config) => [
      <Button key="submit" type="primary" onClick={() => config?.form?.submit()}>
        筛选
      </Button>,
      <Button key="clear" onClick={() => tableRef.current?.reset!()}>
        重置
      </Button>,
    ],
  };

  const toolBarConfig: ListToolBarProps = {
    settings: [],
    search: (
      <Button key="submit" type="primary" loading={syncLoading} onClick={syncBatch}>
        同步批次
      </Button>
    ),
  };

  const columns: ProColumns<BatchListItem>[] = [
    {
      title: '批次ID',
      hideInSearch: true,
      dataIndex: 'id',
      align: 'center',
    },
    {
      title: '团队名称',
      dataIndex: 'team_name',
      align: 'center',
      colSize: 1.5,
      hideInTable: true,
    },
    {
      title: '批次名称',
      dataIndex: 'batch_name',
      align: 'center',
      colSize: 1.5,
    },
    {
      title: '关联团队',
      hideInSearch: true,
      dataIndex: 'team_name',
      align: 'center',
    },
    {
      title: '体检开始时间',
      hideInSearch: true,
      dataIndex: 'start_date',
      align: 'center',
    },
    {
      title: '创建时间',
      hideInSearch: true,
      dataIndex: 'created_at',
      align: 'center',
      ellipsis: true,
      render: (_, record) => (
        <>{record.created_at ? moment(record.created_at).format('YYYY-MM-DD HH:mm') : '-'}</>
      ),
    },
    {
      title: '批次人数',
      hideInSearch: true,
      dataIndex: 'total_quota',
      align: 'center',
    },
    {
      title: '操作',
      hideInSearch: true,
      dataIndex: 'operation',
      align: 'center',
      width: 130,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => detailModelRef.current?.show(record.id.toString())}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            disabled={record.checkup_appointment}
            onClick={() => openEditInfoModal(record)}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="base-content">
      <ProTable
        rowKey="id"
        actionRef={tableRef}
        search={searchConfig}
        columns={columns}
        toolbar={toolBarConfig}
        request={initTableData}
        pagination={{ defaultPageSize: 10 }}
      />

      <BatchEditDrawer
        show={showDrawer}
        batchId={currentBatchId!}
        onSave={saveBatchInfo}
        onHide={() => {
          setShowDrawer(false);
          setCurrentBatchId(undefined);
        }}
      />

      <BatchDetailDrawer ref={detailModelRef} />
    </div>
  );
};

export default BatchManage;
