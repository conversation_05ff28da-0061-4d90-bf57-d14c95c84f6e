import { DocumentTypeEnum } from '@/pages/PatientManage/type.d';

export const genderOptions = ['男', '女'];

export const maritalStatusOptions = ['未婚', '已婚', '离异', '丧偶'];

export const maritalStatusToPackageMap = {
  不限: 'any',
  已婚: 'married',
  未婚: 'unmarried',
  离异: 'divorced',
  丧偶: 'widowed',
};

export const genderToPackageMap = {
  不限: 'any',
  男: 'male',
  女: 'female',
};

type CustomCondition = { label: string; key: string; options?: { label: string; value: string }[] };
export const customConditionsConfig: Record<string, CustomCondition> = {
  name: { label: '姓名', key: 'name' },
  identity_type: {
    label: '证件类型',
    key: 'identity_type',
    options: Object.entries(DocumentTypeEnum).map(([value, label]) => ({ label, value })),
  },
  identify_number: { label: '证件号码', key: 'identify_number' },
  department: { label: '部门', key: 'department' },
  privacy_level: { label: '隐私级别', key: 'privacy_level' },
  position: { label: '岗位', key: 'position' },
  member: { label: '会员', key: 'member' },
  custom_condition: { label: '自定义条件1', key: 'custom_condition' },
  custom_condition_one: { label: '自定义条件2', key: 'custom_condition_one' },
  custom_condition_two: { label: '自定义条件3', key: 'custom_condition_two' },
};
