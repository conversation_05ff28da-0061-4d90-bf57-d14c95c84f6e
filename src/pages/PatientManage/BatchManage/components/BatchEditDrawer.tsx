import type { EditableFormInstance, ProColumns } from '@ant-design/pro-table';
import { EditableProTable } from '@ant-design/pro-table';
import 'moment/locale/zh-cn';
import locale from 'antd/es/date-picker/locale/zh_CN';
import {
  Button,
  DatePicker,
  Drawer,
  Empty,
  Form,
  Input,
  message,
  Modal,
  Select,
  Space,
  Spin,
  Table,
  Tag,
  Upload,
} from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import '../index.less';
import type { UploadProps } from 'antd/lib/upload/interface';
import {
  deleteGroup,
  getBatchDetail,
  getExcelPatientInfo,
  getPackageOptions,
} from '@/pages/PatientManage/services';
import {
  AnalyzeExcelRes,
  BatchPatientInfo,
  Group,
  GroupDetail,
  InstitutionInfo,
  PackageDataItem,
} from '@/pages/PatientManage/type';
import { DocumentTypeEnum } from '@/pages/PatientManage/type.d';
import type { Moment } from 'moment';
import moment from 'moment';
import { calculateAge, extractInfoFromID, validateIDCard } from '@/utils/utils';
import { cloneDeep, uniqueId } from 'lodash';
import GroupEditDrawer from '@/pages/PatientManage/BatchManage/components/GroupEditDrawer';
import {
  genderOptions,
  maritalStatusOptions,
} from '@/pages/PatientManage/BatchManage/components/utils';
import type { ColumnsType } from 'antd/lib/table';
import GroupDetailDrawer from '@/pages/PatientManage/BatchManage/components/GroupDetailDrawer';

type Props = {
  show: boolean;
  batchId: number;
  onSave: (data: any) => Promise<void>;
  onHide: () => void;
};

const { confirm } = Modal;

const baseFieldProps = { size: 'small' };

const supplementaryInfoByIdCard = (item: AnalyzeExcelRes) => {
  if (item && item.identity_type === 'ID_card' && validateIDCard(item.identify_number)) {
    const { gender, age, birthday } = extractInfoFromID(item.identify_number);
    return { ...item, gender, age, date_birth: birthday };
  } else if (item && item.identity_type !== 'ID_card' && item.date_birth) {
    return { ...item, age: calculateAge(item.date_birth) };
  }
  return item;
};

let currentGroup: {
  group: Group;
  patients: (BatchPatientInfo & { rowkey: string | number })[];
} | null;

const BatchEditDrawer: React.FC<Props> = (props) => {
  const { show, onHide, batchId, onSave } = props;
  const [form] = Form.useForm();
  const institutionInfo = useRef<InstitutionInfo>({ institution: '', institution_name: '' });
  const [packageData, setPackageData] = useState<PackageDataItem[]>([]);
  const packageOptions = useMemo(
    () => packageData.map((item) => ({ label: item.package_name, value: item.id })),
    [packageData],
  );
  const [showGroupEdit, setShowGroupEdit] = useState(false);
  const [showGroupDetail, setShowGroupDetail] = useState(false);
  const [loading, setLoading] = useState(false);
  const editTableRef = useRef<EditableFormInstance>();
  const [tableLoading, setTableLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  let addAppointmentDate: Moment | null = null;
  const [appointmentDates, setAppointmentDates] = useState<Moment[]>([]);
  const [groupsDataSource, setGroupsDataSource] = useState<Group[]>([]);
  const notGrouped = useMemo(() => {
    return groupsDataSource.find((group) => group.is_default);
  }, [groupsDataSource]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<(BatchPatientInfo & { rowkey: string | number })[]>(
    [],
  );

  const initPackageOptions = async (institutionCode: string) => {
    const res = await getPackageOptions(institutionCode, 1).catch(() => {
      setPackageData([]);
    });
    if (res) {
      setPackageData(res.data);
    }
  };

  const showDatePicker = () => {
    confirm({
      icon: null,
      title: '添加预约时间',
      maskClosable: true,
      content: (
        <DatePicker
          style={{ width: 170 }}
          locale={locale}
          placeholder="请选择要添加的预约时间"
          disabledDate={(date) => {
            return !!appointmentDates.find(
              (item) => item.format('YYYY-MM-DD') === date.format('YYYY-MM-DD'),
            );
          }}
          onChange={(value) => {
            addAppointmentDate = value;
          }}
        />
      ),
      onOk: () => {
        if (!addAppointmentDate) {
          message.warning('请选择要添加的预约时间');
          return Promise.reject();
        }
        const sortDates = [...appointmentDates, addAppointmentDate].sort((a, b) =>
          a.isBefore(b) ? -1 : 1,
        );
        setAppointmentDates(sortDates);
        addAppointmentDate = null;
        return Promise.resolve();
      },
    });
  };

  const toAddGroup = () => {
    currentGroup = null;
    setShowGroupEdit(true);
  };

  const toEditOrDetailGroup = (group: Group, type: 'edit' | 'detail') => {
    currentGroup = { group, patients: dataSource.filter((item) => item.group_id === group.id) };
    if (type === 'edit') {
      setShowGroupEdit(true);
    } else {
      setShowGroupDetail(true);
    }
  };

  const deleteGroupInfo = (id: number) => {
    deleteGroup(id).then((res) => {
      if (res.code === 2000) {
        message.success('分组删除成功');
        setGroupsDataSource(groupsDataSource.filter((item) => item.id != id));
        setDataSource(
          dataSource.map((patient) => {
            const newPatient = { ...patient };
            if (patient.group_id === id) {
              newPatient.group_id = notGrouped!.id;
            }
            return newPatient;
          }),
        );
      }
    });
  };

  const saveGroupInfo = (groupInfo: GroupDetail, removePatientKeys: (string | number)[]) => {
    const newGroupData = cloneDeep(groupsDataSource);
    const index = newGroupData.findIndex((item) => item.id === groupInfo.id);
    const { package: packageInfo, ...restGroupInfo } = groupInfo;
    const { projects, ...restPackageInfo } = packageInfo;
    const newGroup = {
      ...restGroupInfo,
      package: { ...restPackageInfo, projects: projects.map((item) => item.id) },
    };
    if (index > -1) {
      newGroupData[index] = newGroup;
    } else {
      newGroupData.push(newGroup);
    }
    setGroupsDataSource(newGroupData);
    setDataSource(
      dataSource.map((patient) => {
        const newPatient = { ...patient };
        if (removePatientKeys.includes(patient.rowkey)) {
          newPatient.group_id = notGrouped?.id || null;
        }
        return newPatient;
      }),
    );
    setShowGroupEdit(false);
  };

  const generateEmptyPatient = () => {
    return {
      rowkey: (Math.random() * 1000000).toFixed(0),
      address: '',
      age: undefined,
      custom_condition: '',
      custom_condition_one: '',
      custom_condition_two: '',
      date_birth: undefined,
      department: '',
      gender: '',
      identify_number: '',
      identity_type: 'ID_card',
      marital_status: undefined,
      member: '',
      name: '',
      phone: '',
      position: '',
      privacy_level: '',
      reservation_time: undefined,
      group_id: notGrouped?.id || null,
    };
  };

  // @ts-ignore
  const fileChange: UploadProps['customRequest'] = ({ file }) => {
    setTableLoading(true);
    const needEditKeys: string[] = [];
    getExcelPatientInfo(file as File)
      .then((res) => {
        setDataSource([
          ...dataSource.filter(
            (item) => res.data.findIndex((i) => i.identify_number === item.identify_number) === -1,
          ),
          ...res.data.map((item) => {
            const key = uniqueId();
            if (!item.name || !item.identity_type || !item.identify_number) {
              needEditKeys.push(key);
            }
            return {
              rowkey: key,
              group_id: notGrouped?.id || null,
              ...supplementaryInfoByIdCard(item),
            };
          }),
        ]);
        if (needEditKeys.length > 0) {
          setEditableRowKeys([...editableKeys, ...needEditKeys]);
        }
      })
      .finally(() => {
        setTableLoading(false);
      });
  };

  const automaticGrouping = () => {
    if (dataSource.length < 1) {
      message.warn('请先添加人员');
      return;
    }
    const groupedPatientsData = dataSource.map((patient) => {
      for (const group of groupsDataSource) {
        let canGroup = true;
        const { gender_limit, marital_status, min_age, max_age, conditions } = group;
        if (gender_limit !== '不限' && patient.gender !== gender_limit) {
          canGroup = false;
          continue;
        }
        if (marital_status !== '不限' && patient.marital_status !== marital_status) {
          canGroup = false;
          continue;
        }
        if (
          (patient.age && (patient.age < min_age || patient.age > max_age)) ||
          !patient.age ||
          min_age === null
        ) {
          canGroup = false;
          continue;
        }
        const customConditionFormValues = {};
        conditions.forEach(({ field_name, field_value }) => {
          customConditionFormValues[field_name] = field_value;
        });
        for (const key in customConditionFormValues) {
          const value = customConditionFormValues[key];
          if (patient[key] !== value) {
            canGroup = false;
            break;
          }
        }
        if (canGroup) {
          return { ...patient, group_id: group.id };
        }
      }
      return { ...patient, group_id: notGrouped?.id || null };
    });
    setDataSource(groupedPatientsData);
    message.success('分组成功');
  };

  const saveBatchInfo = async () => {
    const values = await form.validateFields();
    await editTableRef.current?.validateFields();
    if (editableKeys.length > 0) {
      message.warn('请先保存人员配置');
      return;
    }
    const idCardValidateMap: Record<string, boolean> = {};
    for (const item of dataSource) {
      if (!idCardValidateMap[item.identify_number]) {
        idCardValidateMap[item.identify_number] = true;
      } else {
        message.error('人员配置存在重复的身份证号，无法保存');
        return;
      }
    }
    setSaveLoading(true);
    const { batch_code, batch_name, packages } = values;
    // console.log('打印分组数据',groupsDataSource);
    await onSave({
      id: batchId,
      batch_code,
      batch_name,
      phy_dates: appointmentDates.map((item) => item.format('YYYY-MM-DD')),
      start_date: appointmentDates[0].format('YYYY-MM-DD'),
      end_date: appointmentDates[appointmentDates.length - 1].format('YYYY-MM-DD'),
      phy_batch_patients: dataSource.map((item) => {
        const { rowkey, ...rest } = item;
        return rest;
      }),
      groups: groupsDataSource.map((item) => ({
        id: item.id,
        group_name: item.group_name,
        gender_limit: item.gender_limit,
        age_limit: item.age_limit,
        min_age: item.min_age,
        max_age: item.max_age,
        marital_status: item.marital_status,
        price_limit: item.price_limit,
        custom_conditions: item.conditions,
        package: {
          package_code: item.package?.package_code,
          package_name: item.package?.package_name,
          gender_limit: item.package?.gender_limit,
          age_min: item.package?.age_min,
          age_max: item.package?.age_max,
          individual_price: item.package?.individual_price,
          group_price: item.package?.group_price,
          status: item.package?.status,
          marriage_limit: item.package?.marriage_limit,
          project_ids: item.package?.projects,
          institution_name: item.package?.institution_name,
          institution_code: item.package?.institution_code,
        },
      })),
      packages: packageData
        .filter((item) => packages.includes(item.id))
        .map((item) => ({
          id: item.id,
          package_code: item.package_code,
          package_name: item.package_name,
        })),
    }).finally(() => {
      setSaveLoading(false);
    });
  };

  useEffect(() => {
    if (show) {
      if (batchId) {
        setLoading(true);
        getBatchDetail(batchId)
          .then(async (res) => {
            institutionInfo.current = res.data.institution_info;
            form.setFieldsValue({
              team_name: res.data.team_name,
              batch_code: res.data.batch_code,
              batch_name: res.data.batch_name,
              packages: res.data.packages
            });
            setAppointmentDates(res.data.phy_dates.map((dateStr) => moment(dateStr)));
            const patientDataSource: (BatchPatientInfo & { rowkey: string | number })[] = [];
            res.data.groups.forEach((group) => {
              group.patients.forEach((patient) => {
                patientDataSource.push({
                  rowkey:
                    patient.patient.name +
                    patient.patient.identity_type +
                    patient.patient.identify_number,
                  group_id: group.id,
                  ...supplementaryInfoByIdCard(patient.patient),
                });
              });
            });
            setGroupsDataSource(res.data.groups);
            setDataSource(patientDataSource);
            await initPackageOptions(res.data.institution_info.institution);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    } else {
      setEditableRowKeys([]);
      setDataSource([]);
      setAppointmentDates([]);
      form.resetFields();
    }
  }, [show, batchId]);

  const groupColumns: ColumnsType<Group> = [
    {
      title: '名称',
      dataIndex: 'group_name',
    },
    {
      title: '性别限制',
      dataIndex: 'gender_limit',
    },
    {
      title: '婚姻限制',
      dataIndex: 'marital_status',
    },
    {
      title: '最小年龄',
      dataIndex: 'min_age',
    },
    {
      title: '最大年龄',
      dataIndex: 'max_age',
    },
    {
      title: '金额上限',
      dataIndex: 'price_limit',
    },
    {
      title: '分组人数',
      dataIndex: 'number',
      render: (_, record) => {
        return dataSource.filter((patient) => patient.group_id === record.id).length;
      },
    },
    {
      title: '操作',
      width: '120px',
      render: (_, record) => (
        <Space>
          <Button type="link" size="small" onClick={() => toEditOrDetailGroup(record, 'detail')}>
            查看
          </Button>
          {record.is_default || (
            <Button type="link" size="small" onClick={() => toEditOrDetailGroup(record, 'edit')}>
              编辑
            </Button>
          )}
          {record.is_default || (
            <Button type="text" size="small" danger onClick={() => deleteGroupInfo(record.id)}>
              删除
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const columns: ProColumns<BatchPatientInfo & { rowkey: string | number }>[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      fieldProps: baseFieldProps,
      formItemProps: () => ({
        rules: [{ required: true, message: '此项为必填项' }],
      }),
      width: '80px',
    },
    {
      title: '证件类型',
      dataIndex: 'identity_type',
      fieldProps: { size: 'small', style: { minWidth: 'unset', width: '90px' } },
      formItemProps: () => ({
        rules: [{ required: true, message: '此项为必填项' }],
      }),
      valueType: 'select',
      valueEnum: (() => {
        const typeEnum = {};
        Object.entries(DocumentTypeEnum).forEach(([value, label]) => {
          typeEnum[value] = label;
        });
        return typeEnum;
      })(),
      width: '100px',
    },
    {
      title: '证件号',
      fieldProps: baseFieldProps,
      dataIndex: 'identify_number',
      width: '160px',
      formItemProps: (formInstance, { rowKey }) => ({
        rules: [
          { required: true, message: '此项为必填项' },
          {
            validator: (_rule, value: string) => {
              // 类型提示声明错误 rowKey实际值是string[]
              const [key] = rowKey! as unknown as string[];
              const rowData = formInstance.getFieldsValue();
              const type = rowData[key].identity_type;
              if (type === 'ID_card') {
                if (!validateIDCard(value)) {
                  return Promise.reject(new Error('请输入正确的身份证号'));
                }
                const index = dataSource.findIndex((item) => item.identify_number === value);
                if (index > -1 && key !== dataSource[index].rowkey) {
                  return Promise.reject(new Error('存在重复的身份证号'));
                }
              }
              return Promise.resolve();
            },
          },
        ],
      }),
    },
    {
      title: '性别',
      dataIndex: 'gender',
      /*shouldCellUpdate: (record, prevRecord) => {
        return record.gender !== prevRecord.gender;
      },*/
      fieldProps: { size: 'small', style: { minWidth: 'unset', width: '40px' } },
      valueType: 'select',
      valueEnum: (() => {
        const valueEnum = {};
        genderOptions.forEach((item) => {
          valueEnum[item] = { text: item };
        });
        return valueEnum;
      })(),
      width: '50px',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      /*shouldCellUpdate: (record, prevRecord) => {
        return record.age !== prevRecord.age;
      },*/
      // tooltip: '年龄由身份证号或出生日期计算得出',
      fieldProps: { size: 'small', style: { minWidth: 'unset', width: '40px' }, disabled: true },
      valueType: 'digit',
      width: '50px',
    },
    {
      title: '婚姻状况',
      key: 'marital_status',
      dataIndex: 'marital_status',
      fieldProps: { size: 'small', style: { minWidth: 'unset', width: '60px' } },
      valueType: 'select',
      valueEnum: (() => {
        const valueEnum = {};
        maritalStatusOptions.forEach((item) => {
          valueEnum[item] = { text: item };
        });
        return valueEnum;
      })(),
      width: '70px',
    },
    {
      title: '出生日期',
      fieldProps: baseFieldProps,
      dataIndex: 'date_birth',
      valueType: 'date',
      width: '110px',
    },
    {
      title: '预约时间',
      fieldProps: baseFieldProps,
      dataIndex: 'reservation_time',
      valueType: 'date',
      width: '110px',
    },
    {
      title: '部门',
      fieldProps: baseFieldProps,
      dataIndex: 'department',
      width: '100px',
    },
    {
      title: '联系电话',
      fieldProps: baseFieldProps,
      dataIndex: 'phone',
      width: '120px',
    },
    {
      title: '联系地址',
      fieldProps: baseFieldProps,
      dataIndex: 'address',
    },
    {
      title: '隐私级别',
      fieldProps: baseFieldProps,
      dataIndex: 'privacy_level',
      width: '70px',
    },
    {
      title: '岗位',
      fieldProps: baseFieldProps,
      dataIndex: 'position',
      width: '80px',
    },
    {
      title: '会员',
      fieldProps: baseFieldProps,
      dataIndex: 'member',
      width: '70px',
    },
    {
      title: '自定义条件1',
      fieldProps: baseFieldProps,
      dataIndex: 'custom_condition',
      width: '100px',
    },
    {
      title: '自定义条件2',
      fieldProps: baseFieldProps,
      dataIndex: 'custom_condition_one',
      width: '100px',
    },
    {
      title: '自定义条件3',
      fieldProps: baseFieldProps,
      dataIndex: 'custom_condition_two',
      width: '100px',
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      width: '120px',
      render: (_text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.rowkey);
          }}
        >
          编辑
        </a>,
        <a
          key="delete"
          onClick={() => {
            setDataSource(dataSource.filter((item) => item.rowkey !== record.rowkey));
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  return (
    <Drawer
      width="800px"
      open={show}
      title={<span style={{ fontSize: 16 }}>编辑批次</span>}
      onClose={onHide}
    >
      <Spin spinning={loading}>
        <Form
          form={form}
          labelAlign="left"
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
          autoComplete="off"
        >
          <Form.Item label="批次编号" name="batch_code" noStyle>
            <Input disabled style={{ visibility: 'hidden', height: 0 }} placeholder="批次编号" />
          </Form.Item>
          <Form.Item
            label="批次名称"
            name="batch_name"
            rules={[{ required: true, message: '批次名称' }]}
          >
            <Input disabled style={{ width: '200px' }} placeholder="批次名称" />
          </Form.Item>
          <Form.Item
            label="关联团队"
            name="team_name"
            rules={[{ required: true, message: '关联团队' }]}
          >
            <Input disabled style={{ width: '200px' }} placeholder="关联团队" />
          </Form.Item>
          <Form.Item
            label="可体检时间"
            name="phy_dates"
            required
            rules={[
              {
                validator: () => {
                  if (appointmentDates.length > 0) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('请先添加体检时间'));
                },
              },
            ]}
          >
            {appointmentDates.map((date) => (
              <Tag
                key={date.format('YYYY-MM-DD')}
                color="processing"
                closable
                onClose={() =>
                  setAppointmentDates(
                    appointmentDates.filter(
                      (item) => item.format('YYYY-MM-DD') !== date.format('YYYY-MM-DD'),
                    ),
                  )
                }
              >
                {date.format('YYYY-MM-DD')}
              </Tag>
            ))}
            <Button size="small" type="primary" onClick={showDatePicker}>
              添加体检时间
            </Button>
          </Form.Item>
          <Form.Item label="关联套餐" name="packages">
            <Select
              style={{ minWidth: '200px', width: 'unset' }}
              showSearch
              mode="multiple"
              options={packageOptions}
              optionFilterProp="label"
              placeholder="请选择关联套餐"
              notFoundContent={
                <Empty description="暂无套餐数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
              }
              filterOption={(input, option) =>
                ((option?.label as string) ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        </Form>

        <Form.Item label="分组设置" style={{ marginBottom: 'unset' }} wrapperCol={{ span: 20 }}>
          <Space align="center" style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button size="small" type="primary" onClick={toAddGroup}>
              新增分组
            </Button>
          </Space>
        </Form.Item>
        <Table
          rowKey="id"
          columns={groupColumns}
          dataSource={groupsDataSource}
          size="small"
          pagination={false}
          style={{ marginBottom: '1.5rem' }}
        />
        <GroupEditDrawer
          institutionInfo={institutionInfo.current}
          show={showGroupEdit}
          batchId={batchId}
          current={currentGroup}
          onHide={() => setShowGroupEdit(false)}
          onSave={saveGroupInfo}
        />
        <GroupDetailDrawer
          show={showGroupDetail}
          onHide={() => setShowGroupDetail(false)}
          current={currentGroup}
        />

        <Form.Item label="人员信息" style={{ marginBottom: 'unset' }} wrapperCol={{ span: 20 }}>
          <Space align="center" style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Upload showUploadList={false} customRequest={fileChange}>
              <Button type="primary" size="small">
                导入人员信息
              </Button>
            </Upload>
          </Space>
        </Form.Item>
        <EditableProTable
          editableFormRef={editTableRef}
          className="edit-table"
          tableStyle={{ marginTop: 10 }}
          size="small"
          rowKey="rowkey"
          headerTitle={null}
          recordCreatorProps={{
            position: 'bottom',
            record: generateEmptyPatient,
          }}
          loading={tableLoading}
          scroll={{ x: 1800 }}
          columns={columns}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: 'multiple',
            editableKeys,
            onValuesChange: (record) => {
              if (
                record &&
                record.identity_type === 'ID_card' &&
                validateIDCard(record.identify_number)
              ) {
                const { gender, age, birthday } = extractInfoFromID(record.identify_number);
                editTableRef.current?.setRowData?.(record.rowkey, {
                  gender,
                  age,
                  date_birth: birthday,
                });
              } else if (record && record.identity_type !== 'ID_card' && record.date_birth) {
                editTableRef.current?.setRowData?.(record.rowkey, {
                  age: calculateAge(record.date_birth),
                });
              }
            },
            onChange: setEditableRowKeys,
          }}
        />

        <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: 40 }}>
          <Button style={{ marginRight: 20 }} type="primary" onClick={automaticGrouping}>
            人员自动分组
          </Button>
          <Button style={{ marginRight: 20 }} onClick={onHide}>
            取消
          </Button>
          <Button loading={saveLoading} type="primary" onClick={saveBatchInfo}>
            保存
          </Button>
        </div>
      </Spin>
    </Drawer>
  );
};

export default BatchEditDrawer;
