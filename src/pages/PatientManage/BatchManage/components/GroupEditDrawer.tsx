import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { MenuProps } from 'antd';
import {
  Button,
  Drawer,
  Dropdown,
  Form,
  Input,
  Select,
  Space,
  Table,
  InputNumber,
  Typography,
  message,
} from 'antd';
import type { RefType } from '@/components/PackageProjectSelect';
import PackageProjectSelector from '@/components/PackageProjectSelect';
import type { ColumnsType } from 'antd/lib/table';
import type { ProjectListItem } from '@/pages/PackageManage/ProjectManage/type';
import type {
  BatchPatientInfo,
  Group,
  GroupDetail,
  InstitutionInfo,
} from '@/pages/PatientManage/type';
import { DocumentTypeEnum } from '@/pages/PatientManage/type.d';
import {
  customConditionsConfig,
  genderOptions,
  genderToPackageMap,
  maritalStatusOptions,
  maritalStatusToPackageMap,
} from '@/pages/PatientManage/BatchManage/components/utils';
import { addGroupInfo, editGroupInfo, getGroupInfo } from '@/pages/PatientManage/services';
import { nanoid } from 'nanoid';
import dayjs from 'dayjs';

const { Text } = Typography;

type Props = {
  show: boolean;
  batchId: number;
  institutionInfo: InstitutionInfo;
  current: { group: Group; patients: (BatchPatientInfo & { rowkey: string | number })[] } | null;
  onHide: () => void;
  onSave: (groupInfo: GroupDetail, removePatientKeys: (string | number)[]) => void;
};

const genderLimitOptions = [
  { label: '不限', value: '不限' },
  ...genderOptions.map((item) => ({
    label: item,
    value: item,
  })),
];
const maritalStatusLimitOptions = [
  { label: '不限', value: '不限' },
  ...maritalStatusOptions.map((item) => ({
    label: item,
    value: item,
  })),
];

const GroupEditDrawer: React.FC<Props> = (props) => {
  const { show, onHide, batchId, institutionInfo, onSave, current } = props;
  const [form] = Form.useForm();
  const [priceLimit, setPriceLimit] = useState<number | null>(null);
  const [customForm] = Form.useForm();
  const [addedCustomConditions, setAddedCustomConditions] = useState<
    (keyof typeof customConditionsConfig)[]
  >([]);
  const nonAdditiveConditionItem = useMemo(() => {
    const menuItem: MenuProps['items'] = [];
    Object.entries(customConditionsConfig).forEach(([key, config]) => {
      if (!addedCustomConditions.includes(key as keyof typeof customConditionsConfig)) {
        menuItem.push({ key, label: config.label });
      }
    });
    return menuItem;
  }, [addedCustomConditions]);
  const projectSelectorRef = useRef<RefType>(null);
  const [defaultSelectedProjectIds, setDefaultSelectedProjectIds] = useState<number[]>([]);
  const [projects, setProjects] = useState<ProjectListItem[]>([]);
  const totalPrice = useMemo(() => {
    const actualPrice = projects.reduce((acc, cur) => {
      return acc + Number(cur.price);
    }, 0);
    // todo 这里如果是湖州中心医院，展示价格等于实际价格乘以1.1，目前10%的上浮比率前端写死
    const displayPrice =
      institutionInfo.institution === 'HZSZXYYJKTJZX' ? actualPrice * 1.1 : actualPrice;
    const isOutOfBudget = !!(priceLimit && Number(displayPrice.toFixed(2)) > priceLimit);
    return {
      actualPrice: actualPrice.toFixed(2),
      displayPrice: displayPrice.toFixed(2),
      isOutOfBudget,
    };
  }, [projects, priceLimit]);
  const [patientList, setPatientList] = useState<
    (BatchPatientInfo & { rowkey: string | number })[]
  >([]);
  const [removePatientKeys, setRemovePatientKeys] = useState<(string | number)[]>([]);

  const handleAddCustomCondition: MenuProps['onClick'] = ({ key }) => {
    setAddedCustomConditions([
      ...addedCustomConditions,
      key as keyof typeof customConditionsConfig,
    ]);
  };

  const handleRemoveCustomCondition = (key: string) => {
    setAddedCustomConditions(addedCustomConditions.filter((item) => item !== key));
    customForm.resetFields([key]);
  };

  const projectsSelectedCallback = (
    _selectedProjectIds: number[],
    selectedProjects: ProjectListItem[],
  ) => {
    setProjects([...selectedProjects]);
  };

  const removePatient = (key: string | number) => {
    setPatientList(patientList.filter((item) => item.rowkey !== key));
    setRemovePatientKeys([...removePatientKeys, key]);
  };

  const save = async () => {
    const conditionFormValues = await form.validateFields();
    const customConditionFormValues = await customForm.validateFields();
    if (projects.length < 1) {
      message.error('请选择体检项目。');
      return;
    }
    const { gender_limit, marital_status, min_age, max_age, price_limit, group_name } =
      conditionFormValues;
    let isPassValid = true;
    for (const patient of patientList) {
      if (gender_limit !== '不限' && patient.gender !== gender_limit) {
        isPassValid = false;
        break;
      }
      if (marital_status !== '不限' && patient.marital_status !== marital_status) {
        isPassValid = false;
        break;
      }
      if ((patient.age && (patient.age < min_age || patient.age > max_age)) || !patient.age) {
        isPassValid = false;
        break;
      }
      for (const key in customConditionFormValues) {
        const value = customConditionFormValues[key];
        if (patient[key] !== value) {
          isPassValid = false;
          break;
        }
      }
    }
    if (!isPassValid) {
      message.error('当前存在不满足分组条件的人员，请移除后再进行保存。');
      return;
    }
    let packageCode = nanoid(20);
    const packageName = group_name;
    if (current) {
      packageCode = current.group.package?.package_code || packageCode;
    }
    const params = {
      batch: batchId,
      group_name,
      gender_limit,
      age_limit: '限制',
      min_age,
      max_age,
      marital_status,
      price_limit,
      // 拼凑接口需要的套餐数据
      package: {
        package_code: packageCode,
        package_name: packageName,
        gender_limit: genderToPackageMap[gender_limit],
        age_min: min_age,
        age_max: max_age,
        individual_price: totalPrice.actualPrice,
        group_price: totalPrice.actualPrice,
        status: 'active',
        marriage_limit: maritalStatusToPackageMap[marital_status],
        project_ids: projects.map((item) => item.id),
        institution_name: institutionInfo.institution_name,
        institution_code: institutionInfo.institution,
      },
      // 拼凑接口需要的自定义条件数据
      custom_conditions: Object.entries(customConditionFormValues).map(([key, value]) => ({
        field_name: key,
        field_value: value,
      })),
    };
    let res;
    if (current) {
      res = await editGroupInfo({ ...params, id: current.group.id });
      console.log(res);
    } else {
      res = await addGroupInfo(params);
    }
    onSave(res.data, removePatientKeys);
  };

  useEffect(() => {
    if (show) {
      if (current) {
        getGroupInfo(current.group.id).then((res) => {
          console.log(res.data);
          form.setFieldsValue({
            group_name: res.data.group_name,
            gender_limit: res.data.gender_limit,
            marital_status: res.data.marital_status,
            min_age: res.data.min_age,
            max_age: res.data.max_age,
            price_limit: res.data.price_limit,
          });
          setPriceLimit(res.data.price_limit ? Number(res.data.price_limit) : null);
          if (res.data.conditions && res.data.conditions.length > 0) {
            const customConditionsFieldsValue = {};
            const initAddCustomConditions: (keyof typeof customConditionsConfig)[] = [];
            res.data.conditions.forEach((item) => {
              customConditionsFieldsValue[item.field_name] = item.field_value;
              initAddCustomConditions.push(item.field_name);
            });
            setAddedCustomConditions(initAddCustomConditions);
            customForm.setFieldsValue(customConditionsFieldsValue);
          }
          setPatientList([...current.patients]);
          setProjects(res.data.package?.projects || []);
          setDefaultSelectedProjectIds(res.data.package?.projects.map((item) => item.id) || []);
        });
      }
    } else {
      setAddedCustomConditions([]);
      setDefaultSelectedProjectIds([]);
      setRemovePatientKeys([]);
      setPatientList([]);
      setProjects([]);
      form?.resetFields();
      customForm?.resetFields();
      projectSelectorRef.current?.reset();
    }
  }, [show, current]);

  const projectColumns: ColumnsType<ProjectListItem> = [
    { title: '科室', dataIndex: 'department', width: 60 },
    { title: '编码', dataIndex: 'project_code', width: 60 },
    { title: '名称', dataIndex: 'project_name' },
    { title: '价格', dataIndex: 'price', width: 60 },
  ];

  const memberColumns: ColumnsType<BatchPatientInfo & { rowkey: string | number }> = [
    {
      title: '姓名',
      dataIndex: 'name',
      width: '80px',
    },
    {
      title: '证件类型',
      dataIndex: 'identity_type',
      width: '100px',
      render: (_, record) => DocumentTypeEnum[record.identity_type],
    },
    {
      title: '证件号',
      dataIndex: 'identify_number',
      width: '160px',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      width: '50px',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: '50px',
    },
    {
      title: '婚姻状况',
      dataIndex: 'marital_status',
      width: '70px',
    },
    {
      title: '出生日期',
      dataIndex: 'date_birth',
      width: '110px',
    },
    {
      title: '预约时间',
      dataIndex: 'reservation_time',
      width: '110px',
      render: (_, record) =>
        record.reservation_time ? dayjs(record.reservation_time).format('YYYY-MM-DD') : '',
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: '100px',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      width: '120px',
    },
    {
      title: '联系地址',
      dataIndex: 'address',
    },
    {
      title: '隐私级别',
      dataIndex: 'privacy_level',
      width: '70px',
    },
    {
      title: '岗位',
      dataIndex: 'position',
      width: '80px',
    },
    {
      title: '会员',
      dataIndex: 'member',
      width: '70px',
    },
    {
      title: '操作',
      fixed: 'right',
      width: '120px',
      render: (_text, record) => <a onClick={() => removePatient(record.rowkey)}>删除</a>,
    },
  ];

  return (
    <Drawer
      width="800px"
      open={show}
      title={<span style={{ fontSize: 16 }}>{current ? '编辑' : '新建'}分组</span>}
      onClose={onHide}
    >
      <div style={{ display: 'flex', marginBottom: 20 }}>
        <div style={{ width: 430, marginRight: 20 }}>
          <Form
            form={form}
            labelAlign="left"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 19 }}
            autoComplete="off"
          >
            <Form.Item
              label="小组名称"
              name="group_name"
              rules={[{ required: true, message: '请输入小组名称' }]}
            >
              <Input style={{ width: '200px' }} placeholder="请输入小组名称" />
            </Form.Item>
            <Form.Item
              label="性别限制"
              name="gender_limit"
              rules={[{ required: true, message: '请输入性别限制' }]}
            >
              <Select
                style={{ width: '200px' }}
                placeholder="请输入性别限制"
                options={genderLimitOptions}
              />
            </Form.Item>
            <Form.Item
              label="婚姻限制"
              name="marital_status"
              rules={[{ required: true, message: '请输入婚姻限制' }]}
            >
              <Select
                style={{ width: '200px' }}
                placeholder="请输入婚姻限制"
                options={maritalStatusLimitOptions}
              />
            </Form.Item>
            <Form.Item
              label="最小年龄"
              name="min_age"
              rules={[{ required: true, message: '请输入最小年龄' }]}
            >
              <InputNumber
                style={{ width: '200px' }}
                min={0}
                max={120}
                precision={0}
                placeholder="请输入最小年龄"
              />
            </Form.Item>
            <Form.Item
              label="最大年龄"
              name="max_age"
              rules={[{ required: true, message: '请输入最大年龄' }]}
            >
              <InputNumber
                style={{ width: '200px' }}
                min={0}
                max={120}
                precision={0}
                placeholder="请输入最大年龄"
              />
            </Form.Item>
            <Form.Item
              label="金额上限"
              name="price_limit"
              rules={[{ required: true, message: '请输入金额上限' }]}
            >
              <InputNumber
                style={{ width: '200px' }}
                min={0}
                precision={2}
                value={priceLimit}
                onChange={(value) => setPriceLimit(value)}
                placeholder="请输入金额上限"
              />
            </Form.Item>
          </Form>
        </div>

        <div style={{ flex: 1 }}>
          <Dropdown
            menu={{ items: nonAdditiveConditionItem, onClick: handleAddCustomCondition }}
            placement="bottom"
          >
            <Button type="primary" style={{ marginBottom: '1.5rem' }}>
              增加自定义条件
            </Button>
          </Dropdown>
          <Form
            form={customForm}
            labelAlign="left"
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 19 }}
            autoComplete="off"
          >
            {addedCustomConditions.map((key) => (
              <Form.Item key={key} label={customConditionsConfig[key].label} required>
                <Space>
                  <Form.Item
                    name={key}
                    noStyle
                    rules={[
                      { required: true, message: `请输入${customConditionsConfig[key].label}` },
                    ]}
                  >
                    {customConditionsConfig[key].options ? (
                      <Select
                        style={{ width: '150px' }}
                        placeholder={`请选择${customConditionsConfig[key].label}`}
                        options={customConditionsConfig[key].options}
                      />
                    ) : (
                      <Input
                        style={{ width: '150px' }}
                        placeholder={`请输入${customConditionsConfig[key].label}`}
                      />
                    )}
                  </Form.Item>
                  <Button type="primary" danger onClick={() => handleRemoveCustomCondition(key)}>
                    移除
                  </Button>
                </Space>
              </Form.Item>
            ))}
          </Form>
        </div>
      </div>
      <div style={{ display: 'flex', marginBottom: 20 }}>
        <div style={{ width: 430, marginRight: 20 }}>
          <div>体检项目选择</div>
          <PackageProjectSelector
            ref={projectSelectorRef}
            defaultSelectedProjects={defaultSelectedProjectIds}
            institutionCode={institutionInfo.institution}
            onChange={projectsSelectedCallback}
          />
        </div>
        <div style={{ flex: 1 }}>
          <Text type={totalPrice.isOutOfBudget ? 'danger' : undefined}>
            已选中项目（已选中金额：{totalPrice.displayPrice}
            {totalPrice.isOutOfBudget ? '，超出金额限制' : ''}）
          </Text>
          <Table
            scroll={{ y: 320 }}
            rowKey="project_code"
            columns={projectColumns}
            dataSource={projects}
            pagination={false}
            size="small"
          />
        </div>
      </div>
      {current && (
        <>
          人员信息
          <Table
            rowKey="rowkey"
            scroll={{ x: 1500 }}
            columns={memberColumns}
            dataSource={patientList}
            size="small"
            pagination={false}
          />
        </>
      )}

      <Space style={{ width: '100%', marginTop: 30, display: 'flex', justifyContent: 'flex-end' }}>
        <Button onClick={onHide}>取消</Button>
        <Button type="primary" onClick={save}>
          保存
        </Button>
      </Space>
    </Drawer>
  );
};

export default GroupEditDrawer;
