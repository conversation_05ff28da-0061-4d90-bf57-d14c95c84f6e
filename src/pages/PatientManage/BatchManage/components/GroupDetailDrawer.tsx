import React, { useEffect, useMemo, useState } from 'react';
import { Descriptions, Drawer, Table, Typography } from 'antd';
import type { BatchPatientInfo, Group, GroupDetail } from '@/pages/PatientManage/type';
import { DocumentTypeEnum } from '@/pages/PatientManage/type.d';
import { getGroupInfo } from '@/pages/PatientManage/services';
import type { ColumnsType } from 'antd/lib/table';
import type { ProjectListItem } from '@/pages/PackageManage/ProjectManage/type';
import dayjs from 'dayjs';
import { customConditionsConfig } from '@/pages/PatientManage/BatchManage/components/utils';

type Props = {
  show: boolean;
  onHide: () => void;
  current: { group: Group; patients: (BatchPatientInfo & { rowkey: string | number })[] } | null;
};

const { Text } = Typography;

const GroupDetailDrawer: React.FC<Props> = ({ show, onHide, current }) => {
  const [groupInfo, setGroupInfo] = useState<GroupDetail>();
  const totalPrice = useMemo(() => {
    const price = groupInfo?.package?.projects.reduce((acc, cur) => {
      return acc + Number(cur.price);
    }, 0) || 0;
    // todo 这里如果是湖州中心医院，展示价格等于实际价格乘以1.1，目前10%的上浮比率前端写死
    if (groupInfo?.package?.institution_code === 'HZSZXYYJKTJZX'){
      return (price * 1.1).toFixed(2);
    }else {
      return price.toFixed(2);
    }
  }, [groupInfo]);

  useEffect(() => {
    if (show && current) {
      getGroupInfo(current.group.id).then((res) => {
        console.log(res);
        setGroupInfo(res.data);
      });
    } else {
      setGroupInfo(undefined);
    }
  }, [show, current]);

  const projectColumns: ColumnsType<ProjectListItem> = [
    { title: '科室', dataIndex: 'department', width: 60 },
    { title: '编码', dataIndex: 'project_code', width: 60 },
    { title: '名称', dataIndex: 'project_name' },
    { title: '价格', dataIndex: 'price', width: 60 },
  ];

  const memberColumns: ColumnsType<BatchPatientInfo & { rowkey: string | number }> = [
    {
      title: '姓名',
      dataIndex: 'name',
      width: '80px',
    },
    {
      title: '证件类型',
      dataIndex: 'identity_type',
      width: '100px',
      render: (_, record) => DocumentTypeEnum[record.identity_type],
    },
    {
      title: '证件号',
      dataIndex: 'identify_number',
      width: '160px',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      width: '50px',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: '50px',
    },
    {
      title: '婚姻状况',
      dataIndex: 'marital_status',
      width: '70px',
    },
    {
      title: '出生日期',
      dataIndex: 'date_birth',
      width: '110px',
    },
    {
      title: '预约时间',
      dataIndex: 'reservation_time',
      width: '110px',
      render: (_, record) =>
        record.reservation_time ? dayjs(record.reservation_time).format('YYYY-MM-DD') : '',
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: '100px',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      width: '120px',
    },
    {
      title: '联系地址',
      dataIndex: 'address',
    },
    {
      title: '隐私级别',
      dataIndex: 'privacy_level',
      width: '70px',
    },
    {
      title: '岗位',
      dataIndex: 'position',
      width: '80px',
    },
    {
      title: '会员',
      dataIndex: 'member',
      width: '70px',
    },
  ];

  return (
    <Drawer
      width="800px"
      open={show}
      title={<span style={{ fontSize: 16 }}>查看分组</span>}
      onClose={onHide}
    >
      <div style={{ display: 'flex', marginBottom: 20 }}>
        <div style={{ width: 430, marginRight: 20 }}>
          <Descriptions column={1}>
            <Descriptions.Item label="小组名称">{groupInfo?.group_name}</Descriptions.Item>
            <Descriptions.Item label="性别限制">{groupInfo?.gender_limit}</Descriptions.Item>
            <Descriptions.Item label="婚姻限制">{groupInfo?.marital_status}</Descriptions.Item>
            <Descriptions.Item label="最小年龄">{groupInfo?.min_age}</Descriptions.Item>
            <Descriptions.Item label="最大年龄">{groupInfo?.max_age}</Descriptions.Item>
            {groupInfo?.conditions.map(({ field_name, field_value }) => (
              <Descriptions.Item key={field_name} label={customConditionsConfig[field_name].label}>
                {customConditionsConfig[field_name].options
                  ? customConditionsConfig[field_name].options?.find(
                      (option) => option.value === field_value,
                    )?.label
                  : field_value}
              </Descriptions.Item>
            ))}
            <Descriptions.Item label="金额上限">{groupInfo?.price_limit}</Descriptions.Item>
          </Descriptions>
        </div>
        <div style={{ flex: 1 }}>
          <Text>已选中项目（已选中金额：{totalPrice}）</Text>
          <Table
            scroll={{ y: 300 }}
            rowKey="project_code"
            columns={projectColumns}
            dataSource={groupInfo?.package?.projects || []}
            pagination={false}
            size="small"
          />
        </div>
      </div>
      人员信息
      <Table
        rowKey="rowkey"
        scroll={{ x: 1500 }}
        columns={memberColumns}
        dataSource={current?.patients || []}
        size="small"
        pagination={false}
      />
    </Drawer>
  );
};

export default GroupDetailDrawer;
