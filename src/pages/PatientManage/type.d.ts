import type { ProjectListItem } from '@/pages/PackageManage/ProjectManage/type';

export type PatientListItem = {
  id: string;
  name: string;
  gender: string;
  identity_type: 'ID_card';
  identify_number: string;
  phone: string;
  height: string;
  weight: string;
  bmi: string;
  blood_type?: string;
  marital_status: string;
  created_time: string;
  team_num: string;
  batch: string;
};

export type QueryPatientListParams = {
  name?: string;
  phone?: string;
  identify_number?: string;
  created_time_start?: string;
  created_time_end?: string;
} & API.PaginatedParams;

export type AddPatientParams = {
  name: string;
  gender: string;
  identity_type: string;
  identify_number: string;
  phone: string;
  marital_status: string;
  weight?: string;
  height?: string;
  team_num?: string;
  batch?: string;
};

export type EditPatientParams = { patient_id: string } & AddPatientParams;

export type TeamListParams = {
  institution?: string;
  team_code?: string;
} & API.PaginatedParams;

export type TeamListItem = {
  id: number;
  team_code: string;
  team_name: string;
  institution: string;
  institution_name: string;
  status: 'active' | 'inactive';
};

export type BatchListParams = {
  institution?: string;
  team_name?: string;
  batch_name?: string;
} & API.PaginatedParams;

type BatchPackage = { id: number; package_code: string; package_name: string };

export type BatchListItem = {
  id: number;
  batch_code: string;
  batch_name: string;
  team: number;
  team_name: string;
  start_date: string;
  end_date: string;
  price_limit: string;
  total_quota: number;
  used_quota: number;
  created_at: string;
  packages: BatchPackage[];
  checkup_appointment: boolean;
};

export type GroupDetail = {
  age_limit: string;
  // 批次ID
  batch: number;
  gender_limit: string;
  group_name: string;
  id: number;
  marital_status: string;
  max_age: number;
  min_age: number;
  price_limit: string;
  is_default: boolean;
  package: {
    age_max: number;
    age_min: number;
    gender_limit: string;
    group_price: number;
    id: number;
    individual_price: number;
    institution_code: string;
    institution_name: string;
    marriage_limit: string;
    package_code: string;
    package_name: string;
    projects: ProjectListItem[];
    status: string;
  };
  conditions: {
    field_name: string;
    field_value: string;
  }[];
  patients: {
    appointment_date: string;
    check_in_time: string;
    id: number;
    patient: AnalyzeExcelRes;
    register_time: string;
    source: string;
  }[];
};

export type Group = {
  age_limit: string;
  // 批次ID
  batch: number;
  gender_limit: string;
  group_name: string;
  id: number;
  marital_status: string;
  max_age: number;
  min_age: number;
  price_limit: string;
  is_default: boolean;
  package: {
    age_max: number;
    age_min: number;
    gender_limit: string;
    group_price: number;
    id: number;
    individual_price: number;
    institution_code: string;
    institution_name: string;
    marriage_limit: string;
    package_code: string;
    package_name: string;
    projects: number[];
    status: string;
  };
  conditions: {
    field_name: string;
    field_value: string;
  }[];
  patients: {
    appointment_date: string;
    check_in_time: string;
    id: number;
    patient: AnalyzeExcelRes;
    register_time: string;
    source: string;
  }[];
};

export type InstitutionInfo = {
  institution: string;
  institution_name: string;
};

export type BatchDetail = {
  id: number;
  batch_code: string;
  batch_name: string;
  team: number;
  team_name: string;
  start_date: string;
  end_date: string;
  total_quota: number;
  used_quota: number;
  created_at: string;
  groups: Group[];
  institution_info: InstitutionInfo;
  phy_dates: string[];
  packages: number[];
};

export type PackageDataItem = {
  id: number;
  package_code: string;
  package_name: string;
  institution_name: string;
  institution_code: string;
};

export type AnalyzeExcelRes = {
  group_id?: number;
  address?: string;
  age?: number;
  custom_condition?: string;
  custom_condition_one?: string;
  custom_condition_two?: string;
  date_birth?: string;
  department?: string;
  gender?: string;
  identify_number: string;
  identity_type: string;
  marital_status?: string;
  member?: string;
  name: string;
  phone?: string;
  position?: string;
  privacy_level?: string;
  reservation_time?: string;
};

export type BatchPatientInfo = {
  group_id: number | null;
  address?: string;
  age?: number;
  custom_condition?: string;
  custom_condition_one?: string;
  custom_condition_two?: string;
  date_birth?: string;
  department?: string;
  gender?: string;
  identify_number: string;
  identity_type: string;
  marital_status?: string;
  member?: string;
  name: string;
  phone?: string;
  position?: string;
  privacy_level?: string;
  reservation_time?: string;
};

export type EditBatchParams = {
  id: string;
  batch_code: string;
  batch_name: string;
  phy_dates: string[];
  start_date: string;
  end_date: string;
  packages: {
    id: string;
    package_code: string;
    package_name: string;
  }[];
  phy_batch_patients: BatchPatientInfo[];
  groups: {
    id: number;
    group_name: string;
    gender_limit: string;
    age_limit: string;
    min_age: number;
    max_age: number;
    marital_status: string;
    price_limit: string;
    custom_conditions: { field_name: string; field_value: string }[];
    package: {
      package_code: string;
      package_name: string;
      gender_limit: string;
      age_min: number;
      age_max: number;
      individual_price: number;
      group_price: number;
      status: string;
      marriage_limit: string;
      project_ids: number[];
      institution_name: string;
      institution_code: string;
    };
  }[];
};

export enum DocumentTypeEnum {
  ID_card = '身份证',
  passport = '护照',
  hk_macau_pass = '港澳通行证',
  taiwan_pass = '台湾通行证',
  military_certificate = '军官证',
  other = '其他',
}
