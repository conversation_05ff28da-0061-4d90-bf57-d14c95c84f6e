import { Form, Input, Modal, Select, InputNumber } from 'antd';
import type { Rule } from 'antd/lib/form';
import React, { useEffect, useState } from 'react';
import { extractInfoFromID, validateIDCard, validatePhoneNumber } from '@/utils/utils';
import type { AddPatientParams, PatientListItem } from '@/pages/PatientManage/type';

type Props = {
  current?: PatientListItem;
  open: boolean;
  onOk: (res: AddPatientParams) => Promise<void>;
  onCancel: () => void;
};

const rulesMap: Record<string, Rule[]> = {
  name: [{ required: true, message: '请输入姓名' }],
  id_card: [
    {
      required: true,
      message: '请输入身份证号',
    },
    {
      validator: (_rule, value: string) => {
        if (validateIDCard(value) || value === '' || value === undefined) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('请输入正确的身份证号'));
      },
    },
  ],
  phone: [
    {
      required: true,
      message: '请输入手机号码',
    },
    {
      validator: (_rule, value: string) => {
        if (validatePhoneNumber(value) || value === '' || value === undefined) {
          return Promise.resolve();
        }
        return Promise.reject(new Error('请输入正确的手机号'));
      },
    },
  ],
  marital_status: [{ required: true, message: '请输入婚姻状况' }],
};

const EditInfoModal: React.FC<Props> = (props) => {
  const { current, open, onOk, onCancel } = props;

  const [form] = Form.useForm();
  const [title, setTitle] = useState('新增体检者');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      if (current) {
        setTitle('编辑体检者');
        form.setFieldsValue(current);
      } else {
        setTitle('新增体检者');
        form.resetFields();
      }
    }
  }, [open]);

  const onSave = async () => {
    await form.validateFields();
    setLoading(true);
    const fieldsValue = form.getFieldsValue();
    const gender = extractInfoFromID(fieldsValue.identify_number).gender;
    await onOk({ gender, identity_type: 'ID_card', ...fieldsValue }).catch((e) => {
      console.error(e);
    });
    setLoading(false);
  };

  return (
    <Modal
      title={title}
      confirmLoading={loading}
      open={open}
      onOk={onSave}
      onCancel={onCancel}
    >
      <Form
        form={form}
        labelCol={{ span: 7 }}
        wrapperCol={{ span: 12 }}
        initialValues={{ remember: true }}
        autoComplete="off"
      >
        <Form.Item label="姓名" name="name" rules={rulesMap.name}>
          <Input />
        </Form.Item>
        <Form.Item label="身份证号" name="identify_number" rules={rulesMap.id_card}>
          <Input />
        </Form.Item>
        <Form.Item label="手机号码" name="phone" rules={rulesMap.phone}>
          <Input />
        </Form.Item>
        <Form.Item label="身高" name="height">
          <InputNumber addonAfter="cm" style={{ width: '100%' }} min={50} max={300} precision={2} />
        </Form.Item>
        <Form.Item label="体重" name="weight">
          <InputNumber addonAfter="kg" style={{ width: '100%' }} min={10} max={200} precision={2} />
        </Form.Item>
        <Form.Item label="婚姻状况" name="marital_status" rules={rulesMap.marital_status}>
          <Select
            options={[
              { value: '未婚', label: '未婚' },
              { value: '已婚', label: '已婚' },
              { value: '离异', label: '离异' },
              { value: '丧偶', label: '丧偶' },
            ]}
          />
        </Form.Item>
        <Form.Item label="团队编号" name="team_num">
          <Input />
        </Form.Item>
        <Form.Item label="批次" name="batch">
          <Input />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditInfoModal;
