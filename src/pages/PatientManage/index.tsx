import React, { useRef, useState } from 'react';
import type { ActionType, ListToolBarProps, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import type { SearchConfig } from '@ant-design/pro-table/lib/components/Form/FormRender';
import { Button, DatePicker, Descriptions, Drawer, Typography } from 'antd';
import { addPatient, editPatient, getPatientList } from '@/pages/PatientManage/services';
import { extractInfoFromID } from '@/utils/utils';
import EditInfoModal from '@/pages/PatientManage/components/EditInfoModal';
import moment from 'moment';
import type {
  AddPatientParams,
  PatientListItem,
  QueryPatientListParams,
} from '@/pages/PatientManage/type';

const { RangePicker } = DatePicker;

const PatientManage: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const [showEditor, setShowEditor] = useState<boolean>(false);
  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [patientInfo, setPatientInfo] = useState<PatientListItem>();

  const initTableData = async (
    params: QueryPatientListParams & {
      created_time: [string, string];
      pageSize: number;
      current: number;
    },
  ) => {
    const { current, pageSize, created_time, ...restParams } = params;
    let created_time_start, created_time_end;
    if (created_time?.length) {
      created_time_start = moment(created_time[0]).format('YYYY-MM-DD');
      created_time_end = moment(created_time[1]).format('YYYY-MM-DD');
    }
    const res = await getPatientList({
      ...restParams,
      page_size: pageSize,
      page_num: current,
      created_time_start,
      created_time_end,
    });
    return { data: res.data, success: true, total: res.total };
  };

  const showPatientDetail = (info: PatientListItem) => {
    setPatientInfo(info);
    setShowDetail(true);
  };

  const showAddPatientModal = () => {
    setShowEditor(true);
    setPatientInfo(undefined);
  };

  const toEditPatientInfo = () => {
    setShowDetail(false);
    setShowEditor(true);
  };

  const hideEditInfoModal = () => {
    setShowEditor(false);
  };

  const onSave = async (params: AddPatientParams) => {
    console.log('保存数据', params);
    if (patientInfo) {
      await editPatient({ patient_id: patientInfo.id, ...params });
    } else {
      await addPatient(params);
    }
    hideEditInfoModal();
    tableRef.current?.reload();
  };

  const searchConfig: SearchConfig = {
    labelWidth: 'auto',
    span: 4,
    optionRender: (config) => [
      <Button key="submit" type="primary" onClick={() => config?.form?.submit()}>
        筛选
      </Button>,
      <Button key="clear" onClick={() => tableRef.current?.reset!()}>
        重置
      </Button>,
    ],
  };

  const toolBarConfig: ListToolBarProps = {
    settings: [],
    search:
      '',
      /*<Button key="submit" type="primary" onClick={showAddPatientModal}>
        新增体检者
      </Button>*/
  };

  const columns: ProColumns<PatientListItem>[] = [
    {
      title: '用户编号',
      hideInSearch: true,
      dataIndex: 'id',
      align: 'center',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '身份证号',
      dataIndex: 'identify_number',
      colSize: 1.5,
      align: 'center',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      align: 'center',
    },
    /*{
      title: '批次',
      dataIndex: 'batch',
      align: 'center',
    },
    {
      title: '团队编号',
      hideInSearch: true,
      dataIndex: 'team_num',
      align: 'center',
    },*/
    {
      title: '创建时间',
      dataIndex: 'created_time',
      align: 'center',
      colSize: 1.5,
      renderFormItem: () => (
        <RangePicker
          mode={['date', 'date']}
          format="YYYY-MM-DD"
          placeholder={['开始时间', '结束时间']}
        />
      ),
      render: (_, record) => (
        <>{record.created_time ? moment(record.created_time).format('YYYY-MM-DD HH:mm') : '-'}</>
      ),
    },
    {
      title: '操作',
      hideInSearch: true,
      dataIndex: 'operation',
      align: 'center',
      render: (_, record) => <a onClick={() => showPatientDetail(record)}>查看</a>,
    },
  ];

  return (
    <div className="base-content">
      <ProTable
        rowKey="id"
        tableStyle={{ marginTop: 20 }}
        actionRef={tableRef}
        search={searchConfig}
        columns={columns}
        toolbar={toolBarConfig}
        request={initTableData}
        pagination={{ defaultPageSize: 10 }}
      />

      <EditInfoModal
        current={patientInfo}
        open={showEditor}
        onOk={onSave}
        onCancel={hideEditInfoModal}
      />

      <Drawer
        title={<span style={{ fontSize: 16 }}>客户详情</span>}
        placement="right"
        onClose={() => setShowDetail(false)}
        open={showDetail}
      >
        <Typography.Title level={2} style={{ marginBottom: '30px' }}>
          {patientInfo?.name}
          <span style={{ marginLeft: '15px', fontSize: '15px' }}>
            {patientInfo?.identify_number && extractInfoFromID(patientInfo.identify_number).age}岁
          </span>
          <span style={{ marginLeft: '15px', fontSize: '15px' }}>
            {patientInfo?.identify_number && extractInfoFromID(patientInfo.identify_number).gender}
          </span>
        </Typography.Title>
        <Descriptions title="基本信息" column={1}>
          <Descriptions.Item label="姓名">{patientInfo?.name}</Descriptions.Item>
          <Descriptions.Item label="身份证号">{patientInfo?.identify_number}</Descriptions.Item>
          <Descriptions.Item label="手机号">{patientInfo?.phone}</Descriptions.Item>
          <Descriptions.Item label="性别">
            {patientInfo?.identify_number && extractInfoFromID(patientInfo.identify_number).gender}
          </Descriptions.Item>
          <Descriptions.Item label="年龄">
            {patientInfo?.identify_number && extractInfoFromID(patientInfo.identify_number).age}
          </Descriptions.Item>
          {/*<Descriptions.Item label="身高">{patientInfo?.height}</Descriptions.Item>
          <Descriptions.Item label="体重">{patientInfo?.weight}</Descriptions.Item>
          <Descriptions.Item label="BMI">{patientInfo?.bmi}</Descriptions.Item>
          <Descriptions.Item label="批次">{patientInfo?.batch}</Descriptions.Item>
          <Descriptions.Item label="团队编号">{patientInfo?.team_num}</Descriptions.Item>*/}
        </Descriptions>
        {/*<div style={{ marginTop: '80px', textAlign: 'center' }}>
          <Button
            style={{ margin: 'auto' }}
            type="primary"
            size="large"
            onClick={toEditPatientInfo}
          >
            编辑信息
          </Button>
        </div>*/}
      </Drawer>
    </div>
  );
};

export default PatientManage;
