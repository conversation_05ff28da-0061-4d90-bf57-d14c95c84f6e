import type { RadioChangeEvent } from 'antd';
import {
  message,
  Spin,
  Button,
  Card,
  Col,
  Form,
  Input,
  InputNumber,
  List,
  Modal,
  Radio,
  Row,
  Select,
  Typography,
  Checkbox,
  DatePicker,
  Empty,
} from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import type { Rule } from 'antd/lib/form';
import AppointmentProjectSelector from './AppointmentProjectSelector';
import { debounce } from 'lodash';
import {
  getDepartmentProjects,
  getPatientList,
  getRecommendedPackages,
} from '@/pages/AppointmentManage/AppointmentRecords/services';
import type { DefaultOptionType } from 'antd/lib/select';
import { extractInfoFromID } from '@/utils/utils';
import type {
  PackageInfo,
  DepartmentProjectInfo,
  AddAppointmentParams,
  DepartmentProjectItem,
} from '@/pages/AppointmentManage/AppointmentRecords/type';
import moment from 'moment';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';

type Props = {
  open: boolean;
  hospitalOptions: DefaultOptionType[];
  handleOk: (data: AddAppointmentParams) => Promise<void>;
  handleCancel: () => void;
};

const { Title, Text } = Typography;
const { confirm } = Modal;

const rules: Record<string, Rule[]> = {
  name: [{ required: true, message: '请选择用户' }],
  identify_number: [{ required: true, message: '请输入身份证号' }],
  phone: [{ required: true, message: '请输入手机号码' }],
  checkup_time: [{ required: true, message: '请选择体检时间' }],
  height: [{ required: true, message: '请输入身高' }],
  weight: [{ required: true, message: '请输入体重' }],
  hospital: [{ required: true, message: '请选择体检机构' }],
};

let selectedProjects: DepartmentProjectItem[] = [];

const AddAppointmentModal: React.FC<Props> = (props) => {
  const { open, hospitalOptions, handleOk, handleCancel } = props;

  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [fetching, setFetching] = useState(false);
  const [userSourceData, setUserSourceData] = useState<any[]>([]);
  const [packageLoading, setPackageLoading] = useState(false);
  const [packageValue, setPackageValue] = useState<number>();
  const [projectOption, serProjectOption] = useState<DepartmentProjectInfo>();
  const recommendedPackagesData = useRef<PackageInfo[]>();
  const [riskAddInfo, setRiskAddInfo] = useState({
    show: false,
    dataSource: [] as DepartmentProjectItem[],
    selectedItems: [] as DepartmentProjectItem[],
    riskNames: '' as string,
  });
  const options = useMemo(() => {
    return userSourceData.map((item) => ({
      label: item.name,
      value: item.id_card,
    }));
  }, [userSourceData]);

  useEffect(() => {
    if (open) {
      selectedProjects = [];
      form.resetFields();
      recommendedPackagesData.current = undefined;
      serProjectOption(undefined);
      setRiskAddInfo({ show: false, dataSource: [], selectedItems: [], riskNames: '' });
    }
  }, [open]);

  const searchUser = debounce((value: string) => {
    if (value) {
      setFetching(true);
      getPatientList(value)
        .then((res) => {
          setUserSourceData(res.data);
          if (res.data?.length < 1) {
            message.warn('未查询到相关人员！');
          }
        })
        .finally(() => {
          setFetching(false);
        });
    }
  }, 800);

  const loadPackage = async () => {
    const { package_type, identify_number, hospital, marital_status } = form.getFieldsValue([
      'package_type',
      'identify_number',
      'hospital',
      'marital_status',
    ]);
    if (package_type && identify_number && hospital) {
      setPackageLoading(true);
      const { age, gender } = extractInfoFromID(identify_number);
      const res = await getRecommendedPackages({
        marriage_limit: marital_status || undefined,
        suggest_type: package_type,
        identify_number,
        hospital_id: hospital,
        age,
        gender,
      }).catch((e) => console.error(e));
      if (res) {
        recommendedPackagesData.current = res.data.packages;
        setRiskAddInfo({
          show: riskAddInfo.show,
          dataSource: res.data.risk_items,
          selectedItems: [],
          riskNames: res.data.risk_names,
        });
        if (res.data.packages.length > 0) {
          setPackageValue(res.data.packages[0].id);
          serProjectOption(res.data.projects);
        }
      }
      setPackageLoading(false);
    }
  };

  const userChange = (value: string) => {
    form.setFieldsValue({ identify_number: value });
    loadPackage();
  };

  const recommendedMethodChange = (value: '快速推荐' | '精准推荐') => {
    setRiskAddInfo({ ...riskAddInfo, show: value === '精准推荐' });
    loadPackage();
  };

  const packageChange = (e: RadioChangeEvent) => {
    setPackageValue(e.target.value);
    let riskOptionIds;
    if (riskAddInfo.show) {
      riskOptionIds = riskAddInfo.dataSource.map((item) => item.id);
    }
    getDepartmentProjects(e.target.value, riskOptionIds).then((res) => {
      serProjectOption(res.data);
    });
  };

  const showPackageDetail = (packageInfo: PackageInfo) => {
    confirm({
      icon: null,
      title: '套餐项目详情',
      content: (
        <List
          size="small"
          dataSource={packageInfo.project_info || []}
          renderItem={(item) => (
            <List.Item.Meta
              title={item.project_name}
              description={item.indicator_names?.join('、')}
            />
          )}
        />
      ),
      maskClosable: true,
      cancelButtonProps: { style: { display: 'none' } },
      okText: '关闭',
    });
  };

  const projectSelectedChange = (
    e: CheckboxChangeEvent,
    id: number,
    project: DepartmentProjectItem,
  ) => {
    let selectedItems;
    if (e.target.checked) {
      selectedItems = [...riskAddInfo.selectedItems, project];
    } else {
      selectedItems = riskAddInfo.selectedItems.filter((item) => item.id !== id);
    }
    setRiskAddInfo({ ...riskAddInfo, selectedItems });
  };

  const onSelectedProjects = (_values: number[], projects: DepartmentProjectItem[]) => {
    selectedProjects = projects;
  };

  const onOk = async () => {
    await form.validateFields();
    const {
      identify_number,
      phone,
      checkup_time,
      height,
      weight,
      marital_status,
      hospital,
      package_type,
    } = form.getFieldsValue();
    const { gender, birthday } = extractInfoFromID(identify_number);
    // 服务端需要上传套餐源数据
    const packageItem = recommendedPackagesData.current!.find((item) => item.id === packageValue)!;

    const total_cost = [...selectedProjects, ...riskAddInfo.selectedItems].reduce(
      (acc, cur) => acc + Number(cur.price),
      Number(packageItem.price),
    );

    const params: AddAppointmentParams = {
      hospital,
      user_info: {
        name: userSourceData.find((item) => item.id_card === identify_number).name,
        gender,
        phone,
        identify_number,
        height,
        weight,
        birthday,
        blood_type: '',
        marital_status,
        relationship: '',
      },
      checkup_time: moment(checkup_time).format('YYYY-MM-DD'),
      checkup_suite: {
        id: packageItem.id,
        package_name: packageItem.package_name,
        customize: packageItem.customize,
        project_info: packageItem.project_info,
        individual_price: packageItem.individual_price,
        group_price: packageItem.group_price,
        price: packageItem.price,
        projects: packageItem.projects,
        suggest: packageItem.suggest,
      },
      // 将选择项目的源数据转换成服务端需要上传的格式
      checkup_items: selectedProjects.map((item) => {
        return {
          id: item.id,
          project_name: item.project_name,
          project_code: item.project_code,
          department: item.department,
          price: item.price,
        };
      }),
      actual_price: total_cost.toFixed(2),
      total_cost: total_cost.toFixed(2),
      risk_items: riskAddInfo.selectedItems.map((item) => {
        return {
          id: item.id,
          project_name: item.project_name,
          project_code: item.project_code,
          department: item.department,
          price: item.price,
        };
      }),
      package_type,
      appointment_method: '管理端',
      appointment_status: '确认中',
      registration: false,
    };
    setLoading(true);
    await handleOk(params).catch((e) => {
      console.error(e);
    });
    setLoading(false);
  };

  return (
    <Modal
      title="新增预约"
      confirmLoading={loading}
      open={open}
      onOk={onOk}
      onCancel={handleCancel}
      width="60%"
    >
      <Title level={5}>预约信息</Title>
      <Form
        form={form}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        initialValues={{ package_type: '快速推荐' }}
      >
        <Row>
          <Col span={12}>
            <Form.Item name="code" label="姓名" rules={rules.code}>
              <Select
                placeholder="请选择体检用户"
                showSearch
                options={options}
                defaultActiveFirstOption={false}
                showArrow={false}
                filterOption={false}
                onSearch={searchUser}
                onChange={userChange}
                notFoundContent={fetching ? <Spin size="small" /> : null}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="identify_number" label="身份证号" rules={rules.identify_number}>
              <Input placeholder="请输入身份证号" maxLength={18} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="phone" label="手机号码" rules={rules.phone}>
              <Input placeholder="请输入手机号码" maxLength={11} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="checkup_time" label="体检时间" rules={rules.checkup_time}>
              <DatePicker
                placeholder="请选择体检时间"
                format="YYYY-MM-DD"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="height" label="身高" rules={rules.height}>
              <InputNumber
                style={{ width: '100%' }}
                placeholder="请输入身高（cm）"
                precision={0}
                min={0}
                max={120}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="weight" label="体重" rules={rules.weight}>
              <InputNumber
                style={{ width: '100%' }}
                placeholder="请输入体重（kg）"
                precision={0}
                min={0}
                max={120}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="marital_status" label="婚姻状况">
              <Select
                placeholder="请选择婚姻状况"
                options={[
                  { label: '未婚', value: '未婚' },
                  { label: '已婚', value: '已婚' },
                  { label: '离异', value: '离异' },
                  { label: '丧偶', value: '丧偶' },
                ]}
                onChange={(_) => loadPackage()}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="hospital" label="体检机构" rules={rules.hospital}>
              <Select
                placeholder="请选择体检机构"
                options={hospitalOptions}
                onChange={(_) => loadPackage()}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="package_type" label="推荐方式">
              <Select
                placeholder="请选择推荐方式"
                onChange={recommendedMethodChange}
                options={[
                  { label: '按性别年龄快速推荐', value: '快速推荐' },
                  { label: '按风险覆盖项精准推荐', value: '精准推荐' },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <Spin spinning={packageLoading}>
        <Title level={5}>套餐信息</Title>
        <Radio.Group
          style={{ display: 'flex', flexWrap: 'nowrap', overflowX: 'auto' }}
          onChange={packageChange}
          value={packageValue}
        >
          {recommendedPackagesData.current?.map((packageInfo) => (
            <Card
              size="small"
              key={packageInfo.id}
              bordered
              style={{ width: 200, margin: 10, flexShrink: 0 }}
            >
              <Radio value={packageInfo.id}>
                <Text strong>{packageInfo.package_name}</Text>
              </Radio>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: 6,
                }}
              >
                <Text>￥{packageInfo.price}</Text>
                <Button type="link" size="small" onClick={() => showPackageDetail(packageInfo)}>
                  查看详情
                </Button>
              </div>
            </Card>
          )) || <Empty style={{ margin: 'auto' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />}
        </Radio.Group>

        {riskAddInfo.show && (
          <>
            <Title level={5} style={{ marginTop: 16 }}>
              风险加项
            </Title>
            <Row>
              <Col span={18} offset={3}>
                {riskAddInfo.riskNames && (
                  <Text style={{ display: 'block' }} strong>
                    存在【{riskAddInfo.riskNames}】健康风险
                  </Text>
                )}
                {riskAddInfo.dataSource.length > 0 ? (
                  <List
                    size="small"
                    dataSource={riskAddInfo.dataSource}
                    renderItem={(project) => (
                      <List.Item extra={`￥${project.price}`}>
                        <Checkbox
                          checked={
                            riskAddInfo.selectedItems.findIndex((item) => item.id === project.id) >
                            -1
                          }
                          style={{ paddingRight: '15px' }}
                          onChange={(e) => projectSelectedChange(e, project.id, project)}
                        />
                        <List.Item.Meta
                          title={project.project_name}
                          description={project.indicator_names.join('、')}
                        />
                      </List.Item>
                    )}
                  />
                ) : <Empty style={{ margin: 'auto' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />}
              </Col>
            </Row>
          </>
        )}

        <Title level={5} style={{ marginTop: 16 }}>
          加项信息
        </Title>
        <Row>
          <Col span={18} offset={3}>
            <AppointmentProjectSelector
              dataSource={projectOption}
              showSearch={false}
              onChange={onSelectedProjects}
            />
          </Col>
        </Row>
      </Spin>
    </Modal>
  );
};

export default AddAppointmentModal;
