import { Drawer, Descriptions, Typography, Space, Modal, Button, Card, List } from 'antd';
import { ForwardRefExoticComponent, RefAttributes } from 'react';
import { forwardRef, useImperativeHandle, useState } from 'react';
import {
  AppointmentListItem,
  PackageProjectInfoItem,
} from '@/pages/AppointmentManage/AppointmentRecords/type';
import { calculateAge } from '@/utils/utils';

export type PackageInfoDetailDrawerInstance = {
  show: (current: AppointmentListItem) => void;
};

const { Title, Text } = Typography;
const { confirm } = Modal;

const AppointmentDetailDrawer: ForwardRefExoticComponent<
  RefAttributes<PackageInfoDetailDrawerInstance>
> = forwardRef((_props, ref) => {
  const [show, setShow] = useState<boolean>(false);
  const [packageInfo, setPackageInfo] = useState<AppointmentListItem>();

  useImperativeHandle(ref, () => ({
    show: (current) => {
      setShow(true);
      setPackageInfo(current);
    },
  }));

  const showPackageDetail = (info: PackageProjectInfoItem[]) => {
    confirm({
      icon: null,
      title: '套餐项目详情',
      content: (
        <List
          size="small"
          dataSource={info || []}
          renderItem={(item) => (
            <List.Item.Meta
              title={item.project_name}
              description={item.indicator_names?.join('、')}
            />
          )}
        />
      ),
      maskClosable: true,
      cancelButtonProps: { style: { display: 'none' } },
      okText: '关闭',
    });
  };

  const hide = () => {
    setShow(false);
  };

  return (
    <Drawer width="40%" open={show} title={<span style={{ fontSize: 16 }}>预约详情</span>} onClose={hide}>
      <Space direction="vertical" size={0} style={{ marginBottom: '20px' }}>
        <Title level={3} style={{ marginBottom: '5px' }}>
          {packageInfo?.user_info.name}
          <span style={{ marginLeft: '15px', fontSize: '15px' }}>
            {packageInfo?.user_info.birthday
              ? calculateAge(packageInfo?.user_info.birthday) + '岁'
              : ''}
          </span>
          <span style={{ marginLeft: '15px', fontSize: '15px' }}>
            {packageInfo?.user_info.gender}
          </span>
        </Title>
        <Text type="secondary">身份证号；{packageInfo?.user_info.identify_number}</Text>
        <Text type="secondary">手机号：{packageInfo?.user_info.phone}</Text>
      </Space>

      <Descriptions title="预约信息" column={2}>
        <Descriptions.Item label="预约编号">{packageInfo?.id}</Descriptions.Item>
        <Descriptions.Item label="预计体检时间">{packageInfo?.checkup_time} {packageInfo?.checkup_time_period}</Descriptions.Item>
        <Descriptions.Item label="预约发起时间">{packageInfo?.create_datetime}</Descriptions.Item>
        <Descriptions.Item label="来源">{packageInfo?.appointment_method}</Descriptions.Item>
        <Descriptions.Item label="预约状态">{packageInfo?.appointment_status}</Descriptions.Item>
        <Descriptions.Item label="体检机构">{packageInfo?.hospital_name}</Descriptions.Item>
        <Descriptions.Item label="推荐类型">{packageInfo?.package_type}</Descriptions.Item>
        <Descriptions.Item label="所属团队">{packageInfo?.team_name}</Descriptions.Item>
        <Descriptions.Item label="项目总价">￥{packageInfo?.actual_price}</Descriptions.Item>
        <Descriptions.Item label="金额上限">{packageInfo?.price_limit}</Descriptions.Item>
        <Descriptions.Item label="预约类型">{packageInfo?.appointment_type}</Descriptions.Item>
        <Descriptions.Item label="所属批次">{packageInfo?.batch_name}</Descriptions.Item>
      </Descriptions>

      <Descriptions title="套餐信息" />
      <Card size="small" bordered style={{ width: 200, flexShrink: 0 }}>
        <Text strong>{packageInfo?.checkup_suite.package_name}</Text>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginTop: 6,
          }}
        >
          <Text>￥{packageInfo?.checkup_suite.price}</Text>
          <Button
            type="link"
            size="small"
            onClick={() => showPackageDetail(packageInfo!.checkup_suite.project_info)}
          >
            查看详情
          </Button>
        </div>
      </Card>

      {packageInfo?.package_type === '个性化智能推荐' && (
        <>
          <Descriptions title="风险加项信息" style={{ marginTop: 15 }} />
          <List
            size="small"
            dataSource={packageInfo?.risk_items || []}
            renderItem={(project) => (
              <List.Item extra={`￥${project.price}`}>
                <List.Item.Meta
                  title={project.project_name}
                  description={project.indicator_names?.join('、')}
                />
              </List.Item>
            )}
          />
        </>
      )}

      <Descriptions title="加项信息" style={{ marginTop: 15 }} />
      <List
        size="small"
        dataSource={packageInfo?.checkup_items || []}
        renderItem={(project) => (
          <List.Item extra={`￥${project.price}`}>
            <List.Item.Meta
              title={project.project_name}
              description={project.indicator_names?.join('、')}
            />
          </List.Item>
        )}
      />
    </Drawer>
  );
});

export default AppointmentDetailDrawer;
