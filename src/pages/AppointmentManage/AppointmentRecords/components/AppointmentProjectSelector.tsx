import React, {useEffect, useMemo, useState} from 'react';
import { Checkbox, Empty, Input, List, Tabs } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import type {
  DepartmentProjectInfo,
  DepartmentProjectItem,
} from '@/pages/AppointmentManage/AppointmentRecords/type';

type Props = {
  dataSource?: DepartmentProjectInfo;
  showSearch?: boolean;
  defaultSelectedProjectIds?: number[];
  onChange?: (selectedProjectIds: number[], selectedProjects: DepartmentProjectItem[]) => void;
};

type DepartmentProjectData = {
  department: string;
  selectedCount?: number;
  projects: DepartmentProjectItem[];
};

const { Search } = Input;
let selectedProjects: DepartmentProjectItem[] = [];

const AppointmentProjectSelector: React.FC<Props> = (props) => {
  const { dataSource, defaultSelectedProjectIds, onChange, showSearch = true } = props;
  const [searchValue, setSearchValue] = useState('');
  const [selectedProjectIds, setSelectedProjectIds] = useState<number[]>([]);
  const [departmentProjectData, setDepartmentProjectData] = useState<DepartmentProjectData[]>([]);
  const departmentProjectOptions = useMemo(() => {
    const newData: DepartmentProjectData[] = [];
    departmentProjectData.forEach((departmentInfo) => {
      let selectedCount = 0;
      const matchedProjects = departmentInfo.projects.filter((project) => {
        if (selectedProjectIds.includes(project.id)) {
          selectedCount++;
        }
        if (searchValue) {
          return (
            project.project_code.includes(searchValue) || project.project_name.includes(searchValue)
          );
        }
        return true;
      });
      if (matchedProjects.length > 0) {
        newData.push({
          department: departmentInfo.department,
          selectedCount,
          projects: matchedProjects,
        });
      }
    });
    return newData;
  }, [searchValue, departmentProjectData, selectedProjectIds]);

  useEffect(() => {
    if (dataSource) {
      selectedProjects = [];
      if (defaultSelectedProjectIds) {
        setSelectedProjectIds(defaultSelectedProjectIds);
      }else {
        setSelectedProjectIds([])
      }
      const initData = Object.entries(dataSource).map(([department, projects]) => {
        if (defaultSelectedProjectIds){
          projects.forEach((project) => {
            if (defaultSelectedProjectIds.includes(project.id)) {
              selectedProjects.push(project);
            }
          })
        }
        return { department, projects };
      });
      setDepartmentProjectData(initData);
    }
  }, [dataSource, defaultSelectedProjectIds]);

  const projectSelectedChange = (
    e: CheckboxChangeEvent,
    id: number,
    project: DepartmentProjectItem,
  ) => {
    let values: number[] = [];
    if (e.target.checked) {
      values = [...selectedProjectIds, id];
      selectedProjects.push(project);
    } else {
      values = selectedProjectIds.filter((item) => item !== id);
      selectedProjects = selectedProjects.filter((item) => item.id !== id);
    }
    setSelectedProjectIds(values);
    onChange?.(values, selectedProjects);
  };

  return (
    <>
      {showSearch && (
        <Search
          style={{ maxWidth: 200 }}
          placeholder="项目编码或项目名称"
          onSearch={setSearchValue}
        />
      )}
      {dataSource ? (
        <Tabs
          style={{ maxHeight: 320, minHeight: 200, marginTop: 10 }}
          size="small"
          type="card"
          tabPosition="left"
          defaultActiveKey="0"
          items={departmentProjectOptions.map((item) => {
            return {
              label: `${item.department}${
                item.selectedCount && item.selectedCount > 0 ? '(' + item.selectedCount + ')' : ''
              }`,
              key: item.department,
              children: (
                <div style={{ maxHeight: 320, overflowY: 'auto' }}>
                  <List
                    size="small"
                    dataSource={item.projects}
                    renderItem={(project) => (
                      <List.Item extra={`￥${project.price}`}>
                        <Checkbox
                          checked={selectedProjectIds.includes(project.id)}
                          onChange={(e) => projectSelectedChange(e, project.id, project)}
                          style={{ paddingRight: '15px' }}
                        />
                        <List.Item.Meta
                          title={`${project.project_code} ${project.project_name}`}
                          description={project.indicator_names.join('、')}
                        />
                      </List.Item>
                    )}
                  />
                </div>
              ),
            };
          })}
        />
      ) : (
        <Empty style={{ margin: 'auto' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </>
  );
};

export default AppointmentProjectSelector;
