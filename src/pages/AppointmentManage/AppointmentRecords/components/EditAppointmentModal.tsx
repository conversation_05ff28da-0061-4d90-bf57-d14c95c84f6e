import { Empty, message, RadioChangeEvent, Space, Spin } from 'antd';
import {
  Date<PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Col,
  Descriptions,
  InputNumber,
  List,
  Modal,
  Radio,
  Row,
  Checkbox,
  Typography,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import AppointmentProjectSelector from '@/pages/AppointmentManage/AppointmentRecords/components/AppointmentProjectSelector';
import type {
  AppointmentListItem,
  EditAppointmentParams,
  DepartmentProjectInfo,
  DepartmentProjectItem,
  PackageInfo,
} from '@/pages/AppointmentManage/AppointmentRecords/type';
import { AppointmentStatusEnum } from '@/pages/AppointmentManage/AppointmentRecords/type.d';
import { extractInfoFromID } from '@/utils/utils';
import {
  getDepartmentProjects,
  getRecommendedPackages,
} from '@/pages/AppointmentManage/AppointmentRecords/services';
import type { Moment } from 'moment';
import moment from 'moment';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';

type Props = {
  open: boolean;
  current?: AppointmentListItem;
  handleOk: (id: number, params: EditAppointmentParams) => Promise<void>;
  handleCancel: () => void;
};

const { Title, Text } = Typography;
const { confirm } = Modal;

let selectedProjects: DepartmentProjectItem[] = [];

const EditAppointmentModal: React.FC<Props> = (props) => {
  const { open, current, handleOk, handleCancel } = props;

  const [appointmentInfo, setAppointmentInfo] = useState<AppointmentListItem>();
  const [loading, setLoading] = useState(false);
  const [packageLoading, setPackageLoading] = useState(false);
  const [packageValue, setPackageValue] = useState<number>();
  const [riskInfo, setRiskInfo] = useState({
    dataSource: [] as DepartmentProjectItem[],
    selectedItems: [] as DepartmentProjectItem[],
    riskNames: '' as string,
  });
  const [projectOption, serProjectOption] = useState<DepartmentProjectInfo>();
  const recommendedPackagesData = useRef<PackageInfo[]>();
  const [checkupTime, setCheckupTime] = useState<Moment | null>(null);
  const [actualPrice, setActualPrice] = useState<number | null>(null);

  const loadPackage = async (
    package_type: string,
    identify_number: string,
    hospital: string,
    marriage_limit: string,
    default_risk_selected_items?: DepartmentProjectItem[],
  ) => {
    setPackageLoading(true);
    const { age, gender } = extractInfoFromID(identify_number);
    const res = await getRecommendedPackages({
      marriage_limit: marriage_limit || undefined,
      suggest_type: package_type,
      identify_number,
      hospital_id: hospital,
      age,
      gender,
    }).catch((e) => console.error(e));
    if (res) {
      recommendedPackagesData.current = res.data.packages;
      if (default_risk_selected_items) {
        setRiskInfo({
          selectedItems: default_risk_selected_items,
          dataSource: res.data.risk_items,
          riskNames: res.data.risk_names,
        });
      } else {
        setRiskInfo({
          ...riskInfo,
          dataSource: res.data.risk_items,
          riskNames: res.data.risk_names,
        });
      }
      if (res.data.packages.length > 0) {
        serProjectOption(res.data.projects);
      }
    }
    setPackageLoading(false);
  };

  const packageChange = (e: RadioChangeEvent) => {
    setPackageValue(e.target.value);
    getDepartmentProjects(e.target.value).then((res) => {
      serProjectOption(res.data);
    });
  };

  const showPackageDetail = (packageInfo: PackageInfo) => {
    confirm({
      icon: null,
      title: '套餐项目详情',
      content: (
        <List
          size="small"
          dataSource={packageInfo.project_info || []}
          renderItem={(item) => (
            <List.Item.Meta
              title={item.project_name}
              description={item.indicator_names?.join('、')}
            />
          )}
        />
      ),
      maskClosable: true,
      cancelButtonProps: { style: { display: 'none' } },
      okText: '关闭',
    });
  };

  const projectSelectedChange = (
    e: CheckboxChangeEvent,
    id: number,
    project: DepartmentProjectItem,
  ) => {
    let selectedItems;
    if (e.target.checked) {
      selectedItems = [...riskInfo.selectedItems, project];
    } else {
      selectedItems = riskInfo.selectedItems.filter((item) => item.id !== id);
    }
    setRiskInfo({ ...riskInfo, selectedItems });
  };

  const onSelectedProjects = (_values: number[], projects: DepartmentProjectItem[]) => {
    selectedProjects = projects;
  };

  const onOk = async (status?: AppointmentStatusEnum) => {
    if (!checkupTime) {
      message.warn('预计体检时间不能为空');
      return;
    }
    if (!actualPrice) {
      message.warn('项目总价不能为空');
      return;
    }
    const packageItem = recommendedPackagesData.current!.find((item) => item.id === packageValue)!;
    const params: EditAppointmentParams = {
      checkup_time: checkupTime!.format('YYYY-MM-DD'),
      checkup_suite: {
        id: packageItem.id,
        package_name: packageItem.package_name,
        customize: packageItem.customize,
        project_info: packageItem.project_info,
        individual_price: packageItem.individual_price,
        group_price: packageItem.group_price,
        price: packageItem.price,
        projects: packageItem.projects,
        suggest: packageItem.suggest,
      },
      // 将选择项目的源数据转换成服务端需要上传的格式
      checkup_items: selectedProjects.map((item) => {
        return {
          id: item.id,
          project_name: item.project_name,
          project_code: item.project_code,
          department: item.department,
          price: item.price,
        };
      }),
      risk_items: riskInfo.selectedItems.map((item) => {
        return {
          id: item.id,
          project_name: item.project_name,
          project_code: item.project_code,
          department: item.department,
          price: item.price,
        };
      }),
      actual_price: actualPrice!.toFixed(2),
      appointment_status: status || appointmentInfo!.appointment_status,
    };
    setLoading(true);
    await handleOk(current!.id, params).catch((e) => {
      console.error(e);
    });
    setLoading(false);
  };

  useEffect(() => {
    if (open) {
      selectedProjects = [];
      if (current) {
        selectedProjects = current.checkup_items;
        setAppointmentInfo(current);
        setPackageValue(current.checkup_suite.id);
        loadPackage(
          current.package_type,
          current.user_info.identify_number,
          current.hospital.toString(),
          current.user_info.marital_status,
          current.risk_items || [],
        );
        setCheckupTime(moment(current.checkup_time));
        setActualPrice(current.actual_price);
      }
    }
  }, [open]);

  return (
    <Modal
      title="调整项目"
      confirmLoading={loading}
      open={open}
      onCancel={handleCancel}
      width="60%"
      footer={
        <Space>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={() => onOk()}>
            暂存
          </Button>
          <Button type="primary" onClick={() => onOk(AppointmentStatusEnum.待支付)}>
            确定
          </Button>
        </Space>
      }
    >
      <Title level={5}>预约信息</Title>
      <Descriptions column={3}>
        <Descriptions.Item label="姓名">{appointmentInfo?.user_info.name}</Descriptions.Item>
        <Descriptions.Item label="年龄">
          {appointmentInfo ? extractInfoFromID(appointmentInfo.user_info.identify_number).age : ''}
          岁
        </Descriptions.Item>
        <Descriptions.Item label="性别">{appointmentInfo?.user_info.gender}</Descriptions.Item>
        <Descriptions.Item label="身份证号">
          {appointmentInfo?.user_info.identify_number}
        </Descriptions.Item>
        <Descriptions.Item label="手机号">{appointmentInfo?.user_info.phone}</Descriptions.Item>
        <Descriptions.Item label="预约编号">{appointmentInfo?.id}</Descriptions.Item>
        <Descriptions.Item label="预约发起时间">
          {appointmentInfo?.create_datetime}
        </Descriptions.Item>
        <Descriptions.Item label="预计体检时间">
          <DatePicker value={checkupTime} onChange={setCheckupTime} />
        </Descriptions.Item>
        <Descriptions.Item label="来源">{appointmentInfo?.appointment_method}</Descriptions.Item>
        <Descriptions.Item label="预约状态">
          {appointmentInfo?.appointment_status}
        </Descriptions.Item>
        <Descriptions.Item label="体检机构">{appointmentInfo?.hospital_name}</Descriptions.Item>
        <Descriptions.Item label="套餐类型">{appointmentInfo?.package_type}</Descriptions.Item>
        <Descriptions.Item label="项目总价">
          <InputNumber value={actualPrice} onChange={setActualPrice} addonBefore="￥" />
        </Descriptions.Item>
      </Descriptions>

      <Spin spinning={packageLoading}>
        <Title level={5}>套餐信息</Title>
        <Radio.Group
          style={{ display: 'flex', flexWrap: 'nowrap', overflowX: 'auto' }}
          onChange={packageChange}
          value={packageValue}
        >
          {recommendedPackagesData.current?.map((item) => (
            <Card
              size="small"
              key={item.id}
              bordered
              style={{ width: 200, margin: 10, flexShrink: 0 }}
            >
              <Radio value={item.id}>
                <Text strong>{item.package_name}</Text>
              </Radio>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: 6,
                }}
              >
                <Text>￥{item.price}</Text>
                <Button type="link" size="small" onClick={() => showPackageDetail(item)}>
                  查看详情
                </Button>
              </div>
            </Card>
          ))}
        </Radio.Group>

        {appointmentInfo?.package_type === '精准推荐' && (
          <>
            <Title level={5} style={{ marginTop: 16 }}>
              风险加项
            </Title>
            <Row>
              <Col span={18} offset={3}>
                {riskInfo.riskNames && (
                  <Text style={{ display: 'block' }} strong>
                    存在【{riskInfo.riskNames}】健康风险
                  </Text>
                )}
                {riskInfo.dataSource.length > 0 ? (
                  <List
                    size="small"
                    dataSource={riskInfo.dataSource}
                    renderItem={(project) => (
                      <List.Item extra={`￥${project.price}`}>
                        <Checkbox
                          checked={
                            riskInfo.selectedItems.findIndex((item) => item.id === project.id) > -1
                          }
                          style={{ paddingRight: '15px' }}
                          onChange={(e) => projectSelectedChange(e, project.id, project)}
                        />
                        <List.Item.Meta
                          title={project.project_name}
                          description={project.indicator_names.join('、')}
                        />
                      </List.Item>
                    )}
                  />
                ) : (
                  <Empty style={{ margin: 'auto' }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                )}
              </Col>
            </Row>
          </>
        )}

        <Title level={5} style={{ marginTop: 16 }}>
          加项信息
        </Title>
        <Row>
          <Col span={18} offset={3}>
            <AppointmentProjectSelector
              defaultSelectedProjectIds={appointmentInfo?.checkup_items.map((item) => item.id)}
              dataSource={projectOption}
              showSearch={false}
              onChange={onSelectedProjects}
            />
          </Col>
        </Row>
      </Spin>
    </Modal>
  );
};

export default EditAppointmentModal;
