import React, { useEffect, useRef, useState } from 'react';
import type { ActionType, ListToolBarProps, ProColumns } from '@ant-design/pro-table';
import { ProTable } from '@ant-design/pro-table';
import type { SearchConfig } from '@ant-design/pro-table/lib/components/Form/FormRender';
import type { FormInstance } from 'antd';
import { Descriptions, Modal } from 'antd';
import { Button, DatePicker, message, Popconfirm, Select, Space } from 'antd';
import moment from 'moment';
import AddAppointmentModal from './components/AddAppointmentModal';
import type { PackageInfoDetailDrawerInstance } from './components/AppointmentDetailDrawer';
import AppointmentDetailDrawer from './components/AppointmentDetailDrawer';
import EditAppointmentModal from './components/EditAppointmentModal';
import type {
  AddAppointmentParams,
  AppointmentListItem,
  EditAppointmentParams,
  QueryAppointmentListParams,
} from '@/pages/AppointmentManage/AppointmentRecords/type';
import { AppointmentStatusEnum } from '@/pages/AppointmentManage/AppointmentRecords/type.d';
import {
  addAppointments,
  editAppointments,
  exportAppointments,
  getAppointmentList,
  getHospitalList,
} from '@/pages/AppointmentManage/AppointmentRecords/services';
import type { DefaultOptionType } from 'antd/lib/select';
import { initiateRefund } from '../../PaymentManage/PaymentHistory/services';

const { RangePicker } = DatePicker;
const { confirm } = Modal;

const AppointmentRecords: React.FC = () => {
  const tableRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const detailModelRef = useRef<PackageInfoDetailDrawerInstance>(null);
  const [hospitalOptions, setHospitalOptions] = useState<DefaultOptionType[]>([]);
  const [showAddModal, setShowAddModal] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [showEditor, setShowEditor] = useState<boolean>(false);
  const [packageInfo, setPackageInfo] = useState<any>(null);

  useEffect(() => {
    getHospitalList().then((res) => {
      setHospitalOptions(
        res.data.map((item) => ({
          label: item.name,
          value: item.id,
        })),
      );
    });
  }, []);

  const initTableData = async (
    params: QueryAppointmentListParams & {
      current: number;
      pageSize: number;
      create_datetime: string;
    },
  ) => {
    const { current, pageSize, create_datetime, checkup_time, ...restParams } = params;
    let create_datetime__gte, create_datetime__lte, checkup_time__gte, checkup_time__lte;
    if (create_datetime?.length) {
      create_datetime__gte = moment(create_datetime[0]).format('YYYY-MM-DD');
      create_datetime__lte = moment(create_datetime[1]).format('YYYY-MM-DD');
    }
    if (checkup_time?.length) {
      checkup_time__gte = moment(checkup_time[0]).format('YYYY-MM-DD');
      checkup_time__lte = moment(checkup_time[1]).format('YYYY-MM-DD');
    }
    const res = await getAppointmentList({
      ...restParams,
      create_datetime__gte,
      create_datetime__lte,
      checkup_time__gte,
      checkup_time__lte,
      page_size: pageSize,
      page_num: current,
    });
    return { data: res.data, success: true, total: res.total };
  };

  const openEditInfoModal = (record: any) => {
    setPackageInfo(record);
    setShowEditor(true);
  };

  const hideEditInfoModal = () => {
    if (packageInfo) {
      setPackageInfo(null);
      setShowEditor(false);
    } else {
      setShowAddModal(false);
    }
  };

  const onSave = async (params: AddAppointmentParams) => {
    await addAppointments(params);
    message.success('保存成功！');
    hideEditInfoModal();
    tableRef.current?.reload();
  };

  const onEdit = async (id: number, params: EditAppointmentParams) => {
    await editAppointments(id, params);
    message.success('保存成功！');
    hideEditInfoModal();
    tableRef.current?.reload();
  };

  const cancelAppointment = async (record: AppointmentListItem) => {
    await editAppointments(record.id, { appointment_status: '已取消' });
    message.success('取消成功！');
    tableRef.current?.reload();
  };

  const refund = (info: AppointmentListItem) => {
    confirm({
      title: '退款',
      icon: null,
      content: (
        <Descriptions column={1}>
          <Descriptions.Item label="支付业务编号">{info.payment_number}</Descriptions.Item>
          <Descriptions.Item label="交易流水号">{info.serial_number}</Descriptions.Item>
          <Descriptions.Item label="退款方式">原路返回</Descriptions.Item>
          <Descriptions.Item label="退款金额">{info.actual_price}</Descriptions.Item>
        </Descriptions>
      ),
      onOk() {
        return initiateRefund(info.id).then((res) => {
          if (res.code === 2000) {
            console.log('OK');
            tableRef.current?.reload();
          }
        });
      },
    });
  };

  const exportAppointmentsList = async () => {
    setExportLoading(true);
    const { checkup_time, appointment_status, create_datetime, hospital, search } =
      formRef.current?.getFieldsValue();
    let create_datetime__gte, create_datetime__lte, checkup_time__gte, checkup_time__lte;
    if (create_datetime?.length) {
      create_datetime__gte = moment(create_datetime[0]).format('YYYY-MM-DD');
      create_datetime__lte = moment(create_datetime[1]).format('YYYY-MM-DD');
    }
    if (checkup_time?.length) {
      checkup_time__gte = moment(checkup_time[0]).format('YYYY-MM-DD');
      checkup_time__lte = moment(checkup_time[1]).format('YYYY-MM-DD');
    }
    try {
      const res = await exportAppointments({
        create_datetime__gte,
        create_datetime__lte,
        checkup_time__gte,
        checkup_time__lte,
        appointment_status,
        hospital,
        search,
      });
      const blobURL = window.URL.createObjectURL(res); // 将文件对象转换成URL链接
      const a = document.createElement('a');
      a.href = blobURL;
      a.setAttribute('download', '预约单导出数据.xlsx');
      a.click();
      a.remove();
    } catch (error) {
      console.error('文件导出下载时出错:', error);
    }
    setExportLoading(false);
  };

  const searchConfig: SearchConfig = {
    labelWidth: 'auto',
    span: 12,
    collapsed: false,
    collapseRender: false,
    optionRender: (config) => [
      <Button key="submit" type="primary" onClick={() => config?.form?.submit()}>
        筛选
      </Button>,
      <Button key="clear" onClick={() => tableRef.current?.reset!()}>
        重置
      </Button>,
    ],
  };

  const toolBarConfig: ListToolBarProps = {
    settings: [],
    search: (
      <Space>
        {/*<Button type="primary" onClick={() => setShowAddModal(true)}>
          新增预约
        </Button>*/}
        <Button type="primary" loading={exportLoading} onClick={exportAppointmentsList}>
          导出订单
        </Button>
      </Space>
    ),
  };

  const columns: ProColumns<AppointmentListItem>[] = [
    {
      title: '预约编号',
      hideInSearch: true,
      dataIndex: 'id',
      align: 'center',
      width: '6rem',
    },
    {
      title: '预约发起时间',
      dataIndex: 'create_datetime',
      align: 'center',
      formItemProps: { label: '预约时间' },
      width: '13rem',
      colSize: 0.5,
      renderFormItem: () => (
        <RangePicker
          mode={['date', 'date']}
          format="YYYY-MM-DD"
          placeholder={['开始时间', '结束时间']}
        />
      ),
    },
    {
      title: '身份证号',
      hideInSearch: true,
      align: 'center',
      width: '13rem',
      render: (_, record) => {
        return record.user_info?.identify_number;
      },
    },
    {
      title: '人员姓名',
      hideInSearch: true,
      align: 'center',
      width: '6rem',
      render: (_, record) => {
        return record.user_info?.name;
      },
    },
    {
      title: '手机号',
      hideInSearch: true,
      dataIndex: 'phone',
      align: 'center',
      width: '8rem',
      render: (_, record) => {
        return record.user_info?.phone;
      },
    },
    {
      title: '预约机构',
      hideInSearch: true,
      dataIndex: 'hospital_name',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '套餐名称',
      hideInSearch: true,
      dataIndex: 'package_name',
      align: 'center',
      ellipsis: true,
      render: (_, record) => {
        return record.checkup_suite?.package_name;
      },
    },
    {
      title: '预约类型',
      hideInSearch: true,
      align: 'center',
      dataIndex: 'appointment_type',
      width: '6rem',
    },
    {
      title: '项目原价',
      hideInSearch: true,
      align: 'center',
      dataIndex: 'total_cost',
      width: '6rem',
    },
    {
      title: '实际价格',
      hideInSearch: true,
      dataIndex: 'actual_price',
      align: 'center',
      width: '6rem',
    },
    {
      title: '预约来源',
      hideInSearch: true,
      dataIndex: 'appointment_method',
      align: 'center',
      width: '7rem',
    },
    {
      title: '体检时间',
      dataIndex: 'checkup_time',
      align: 'center',
      width: '10rem',
      colSize: 0.5,
      renderFormItem: () => (
        <RangePicker
          mode={['date', 'date']}
          format="YYYY-MM-DD"
          placeholder={['开始时间', '结束时间']}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'appointment_status',
      align: 'center',
      width: '5rem',
      colSize: 0.5,
      valueEnum: AppointmentStatusEnum,
    },
    {
      title: '体检机构',
      hideInTable: true,
      dataIndex: 'hospital',
      colSize: 0.5,
      renderFormItem: () => <Select placeholder="请选择" options={hospitalOptions} />,
    },
    {
      title: '是否登记',
      hideInSearch: true,
      dataIndex: 'registration',
      align: 'center',
      width: '5rem',
      render: (_, record) => {
        return record.registration ? '是' : '否';
      },
    },
    {
      title: '请输入编号或姓名搜索',
      dataIndex: 'search',
      colSize: 0.5,
      fieldProps: {
        placeholder: '请输入编号或姓名搜索',
      },
      formItemProps: {
        label: '',
      },
      hideInTable: true,
    },
    {
      title: '操作',
      hideInSearch: true,
      dataIndex: 'operation',
      align: 'center',
      fixed: 'right',
      width: '10rem',
      render: (_, record) => (
        <Space>
          {/*record.appointment_status === '确认中' && (
            <Button type="link" size="small" onClick={() => openEditInfoModal(record)}>
              调整
            </Button>
          )*/}
          <Button type="link" size="small" onClick={() => detailModelRef.current?.show!(record)}>
            查看
          </Button>
          {record.appointment_status === '待退款' ||
          (record.appointment_status === '确认中' &&
            record.appointment_type === '团体预约' &&
            record.is_refund) ||
          (record.appointment_status === '确认中' && record.appointment_type === '个人预约') ? (
            <Button type="link" size="small" onClick={() => refund(record)}>
              去退款
            </Button>
          ) : null}
          {record.appointment_status === '确认中' &&
          record.appointment_type === '团体预约' &&
          !record.is_refund ? (
            <Popconfirm
              title="确定取消该预约信息?"
              onConfirm={() => cancelAppointment(record)}
              okText="确认"
              cancelText="取消"
            >
              <Button type="link" size="small">
                取消预约
              </Button>
            </Popconfirm>
          ) : null}
        </Space>
      ),
    },
  ];

  return (
    <div className="base-content">
      <ProTable
        scroll={{ x: 1600 }}
        rowKey="id"
        formRef={formRef}
        actionRef={tableRef}
        search={searchConfig}
        columns={columns}
        toolbar={toolBarConfig}
        request={initTableData}
        pagination={{ defaultPageSize: 10 }}
      />

      <AddAppointmentModal
        hospitalOptions={hospitalOptions}
        open={showAddModal}
        handleOk={onSave}
        handleCancel={hideEditInfoModal}
      />

      <EditAppointmentModal
        open={showEditor}
        current={packageInfo}
        handleOk={onEdit}
        handleCancel={hideEditInfoModal}
      />

      <AppointmentDetailDrawer ref={detailModelRef} />
    </div>
  );
};

export default AppointmentRecords;
