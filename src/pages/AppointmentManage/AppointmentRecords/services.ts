import request from '@/utils/request';
import type {
  AddAppointmentParams,
  AppointmentListItem,
  DepartmentProjectInfo,
  DepartmentProjectItem,
  EditAppointmentParams,
  GetPackageInfoParams,
  HospitalItem,
  PackageInfo,
  QueryAppointmentListParams,
} from '@/pages/AppointmentManage/AppointmentRecords/type';

export const getHospitalList = () => {
  return request<API.Response<HospitalItem[]>>('/api/hospital/hospital/', {
    method: 'GET',
  });
};

export const getAppointmentList = (params: QueryAppointmentListParams) => {
  return request<API.Response<AppointmentListItem[]>>('/api/reservation/appointment/', {
    method: 'GET',
    params: { ...params },
  });
};

export const getPatientList = (name: string) => {
  return request<API.Response<{ id_card: string; name: string }[]>>('/api/user/reser_users/', {
    method: 'GET',
    params: { name },
  });
};

export const getRecommendedPackages = (params: GetPackageInfoParams) => {
  return request<
    API.Response<{
      projects: DepartmentProjectInfo;
      packages: PackageInfo[];
      risk_items: DepartmentProjectItem[];
      risk_names: string;
    }>
  >('/api/reservation/appointment/checkup_package/', {
    method: 'POST',
    data: { ...params },
  });
};

export const getDepartmentProjects = (package_id: string, risk_item_id?: number[]) => {
  return request<API.Response<DepartmentProjectInfo>>(
    '/api/reservation/appointment/filter_checkup_package/',
    {
      method: 'POST',
      data: { package_id, risk_item_id },
    },
  );
};

export const addAppointments = (params: AddAppointmentParams) => {
  return request<API.Response>('/api/reservation/appointment/', {
    method: 'POST',
    data: { ...params },
    headers: { 'Content-Type': 'application/json' },
  });
};

export const editAppointments = (id: number, params: EditAppointmentParams) => {
  return request<API.Response>(`/api/reservation/appointment/${id}/`, {
    method: 'PATCH',
    data: { ...params },
    headers: { 'Content-Type': 'application/json' },
  });
};

export const exportAppointments = (params: any) => {
  return request<Blob>('/api/reservation/appointment/export_data/', {
    method: 'GET',
    params: { ...params },
    responseType: 'blob',
  });
};
