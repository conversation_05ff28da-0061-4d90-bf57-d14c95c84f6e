export enum AppointmentStatusEnum {
  确认中 = '确认中',
  待支付 = '待支付',
  待退款 = '待退款',
  待体检 = '待体检',
  已体检 = '已体检',
  已取消 = '已取消',
}

export type AppointmentStatusValue = keyof typeof AppointmentStatusEnum;

export type QueryAppointmentListParams = {
  checkup_time?: string;
  create_datetime?: string;
  create_datetime__gte?: string;
  create_datetime__lte?: string;
  checkup_time__gte?: string;
  checkup_time__lte?: string;
  appointment_status?: string;
  hospital?: string;
  search?: string;
} & API.PaginatedParams;

export type AppointmentListItem = {
  id: number;
  create_datetime: string;
  update_datetime: string;
  user_info: {
    phone: string;
    name: string;
    gender: string;
    height: string;
    weight: string;
    birthday: string;
    blood_type: string;
    relationship: string;
    marital_status: string;
    identify_number: string;
  };
  checkup_time: string;
  total_cost: number;
  checkup_suite: {
    id: number;
    price: number;
    suggest: boolean;
    projects: number[];
    group_price: string;
    project_info: PackageProjectInfoItem[];
    individual_price: string;
    customize: boolean;
    package_name: string;
  };
  checkup_items: DepartmentProjectItem[];
  risk_items: DepartmentProjectItem[];
  package_type: string;
  appointment_method: string;
  appointment_status: AppointmentStatusEnum;
  hospital: number;
  order: number;
  hospital_name: string;
  actual_price: number;
  registration: boolean;
  appointment_type: string;
  batch_code: string;
  batch_name: string;
  team_code: string;
  team_name: string;
  price_limit: number;
  checkup_time_period: string;
  payment_id: string;
  payment_number: string;
  serial_number: string;
  is_refund: boolean;
};

export type GetPackageInfoParams = {
  suggest_type: string;
  age: number;
  gender: string;
  identify_number: string;
  marriage_limit?: string;
  hospital_id: string;
};

type PackageProjectInfoItem = { id: number; project_name: string; indicator_names: string[] };

export type PackageInfo = {
  id: number;
  individual_price: string;
  group_price: string;
  customize: boolean;
  price: string;
  package_name: string;
  suggest: boolean;
  project_info: PackageProjectInfoItem[];
  projects: number[];
};

export type DepartmentProjectItem = {
  id: number;
  project_name: string;
  project_code: string;
  department: string;
  price: string;
  indicator_names: string[];
};

export type DepartmentProjectInfo = Record<string, DepartmentProjectItem[]>;

type AppointmentCheckupSuite = {
  id: number;
  package_name: string;
  customize: boolean;
  project_info: PackageProjectInfoItem[];
  individual_price: string;
  group_price: string;
  price: string;
  projects: number[];
  suggest: boolean;
};

type AppointmentCheckupItem = {
  id: number;
  project_name: string;
  project_code: string;
  department: string;
  price: string;
};

export type AddAppointmentParams = {
  hospital: number;
  user_info: {
    name: string;
    gender: string;
    phone: string;
    identify_number: string;
    height: string;
    weight: string;
    birthday: string;
    blood_type: string;
    marital_status: string;
    relationship: string;
  };
  checkup_time: string;
  checkup_suite: AppointmentCheckupSuite;
  checkup_items: AppointmentCheckupItem[];
  risk_items: AppointmentCheckupItem[];
  actual_price: number | string;
  total_cost: number | string;
  package_type: string;
  appointment_method: string;
  appointment_status: keyof typeof AppointmentStatusEnum;
  registration: boolean;
};

export type EditAppointmentParams = {
  checkup_time?: string;
  actual_price?: number | string;
  checkup_suite?: AppointmentCheckupSuite;
  checkup_items?: AppointmentCheckupItem[];
  risk_items?: unknown[];
  appointment_status?: keyof typeof AppointmentStatusEnum;
  registration?: boolean;
};

export type HospitalItem = {
  addr?: string;
  business_hours?: string;
  create_datetime?: string;
  creator?: string;
  creator_name?: string;
  dept_belong_id?: string;
  description?: string;
  id: number;
  institution_code: string;
  modifier?: string;
  modifier_name?: string;
  name: string;
  phone_number?: string;
  update_datetime?: string;
};
