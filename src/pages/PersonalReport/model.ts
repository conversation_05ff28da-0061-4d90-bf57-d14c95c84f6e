import type { Reducer, Effect } from 'umi';
import { getDataPOST, InformationListGET, RadarGET } from './service';
import Url from './url';

type RestFul = string | Record<string, unknown>[];
type GeneralType = string | number | boolean | Record<string, unknown>[] | undefined;
type StateType = string | undefined;
interface state<T extends RestFul, U extends StateType> {
  date?: U;
  checkupId?: U;
  score?: U;
  scoreTotal?: any;
  dataData?: GeneralType;
  dataOption?: any;
  detail: T;
  year: T;
  quantify: T;
  qual: T;
  initState: GeneralType;
  detect: T;
  user_id: string;
  checkup_id: string;
  detailModal: T;
}
type Personal = {
  namespace: string;
  state: state<RestFul, StateType>;
  effects: {
    initial: Effect;
    handleFetch: Effect;
    handleModal: Effect;
    detectDisease: Effect;
  };
  reducers: {
    personal_init: Reducer;
    changeDate: Reducer;
    handleData: Reducer;
    final: Reducer;
    modal: Reducer;
    handleCancel: Reducer;
    getInit: Reducer;
    diSease: Reducer;
    destroy_value: Reducer;
    changeId: Reducer;
  };
};

const personalModel: Personal = {
  namespace: 'personal',
  state: {
    date: sessionStorage.getItem('defaultYear') || undefined,
    checkupId: sessionStorage.getItem('defaultId') || undefined,
    score: undefined,
    scoreTotal: undefined,
    dataData: undefined,
    dataOption: [],
    detail: [],
    year: [],
    quantify: [],
    qual: [],
    detailModal: [],
    initState: undefined,
    detect: [],
    user_id: 'null',
    checkup_id: 'null',
  },
  effects: {
    // 个人报告 检出疾病
    *detectDisease({ payload }, { call, put }) {
      const disease = yield call(RadarGET, { ...payload });
      yield put({ type: 'diSease', payload: { disease } });
    },
    // 个人报告 信息列表
    *initial({ payload }, { call, put }) {
      const initialValue = yield call(InformationListGET, { ...payload });
      yield put({ type: 'getInit', payload: { initialValue } });
    },
    *handleFetch({ payload }, { call, put }) {
      let data: any = [];
      data = yield call(getDataPOST, Url.AbNormal_Quantify, { ...payload });
      if (data.url) {
        data = [];
      }
      yield put({ type: 'final', payload: { data } });
    },
    *handleModal({ payload }, { call, put }) {
      const { l0_name, l1_name, l3_name, user_id, checkup_id, gender, age } = payload;
      const realParams = { l0_name, l1_name, l3_name, checkup_id };
      let result = {
        detailModal: [],
        year: [],
        quantify: [],
        qual: [],
      };
      try {
        result.detailModal = yield call(getDataPOST, Url.perScoreDetail, { ...realParams });
        result.year = yield call(getDataPOST, Url.scoreData, { l0_name, l1_name, l3_name, user_id, age, gender });
        result.quantify = yield call(getDataPOST, Url.QUANTIFY, { ...realParams, abnormal_only: true });
        result.qual = yield call(getDataPOST, Url.QUALITATIVE, { ...realParams, abnormal_only: true });
      } catch (error) {
        result = { ...result };
      }
      const { detailModal, year, quantify, qual } = result;
      yield put({
        type: 'modal',
        payload: { detailModal, year, quantify, qual },
      });
    },
  },
  reducers: {
    // 保存初始信息
    personal_init(state, { payload }) {
      const { user_id, checkup_id } = payload;
      return { ...state, user_id, checkup_id };
    },
    // 个人报告信息列表
    getInit(state, { payload }) {
      return { ...state,initState: payload.initialValue };
    },
    // 个人报告检出疾病
    diSease(state, { payload }) {
      return { ...state, detect: payload.disease };
    },
    handleData(state, { payload }) {
      return { ...state, scoreTotal: payload[1], dataData: payload[0] };
    },
    final(state, { payload }) {
      return { ...state, dataOption: payload.data };
    },
    modal(state, { payload }) {
      const { detailModal, year, quantify, qual } = payload;
      return { ...state, detailModal, year, quantify, qual };
    },
    changeDate(state, { payload }) {
      return { ...state, date: payload.date, checkupId: state.dataData[payload.date], score: payload.score };
    },
    handleCancel(state: any, { payload }) {
      const { key } = payload;
      if (key === 'cancel') {
        return {
          ...state,
          detailModal: [],
          year: [],
          quantify: [],
          qual: [],
          dataOption: [],
        };
      }
      return null;
    },
    changeId(state: state<RestFul, StateType>, { payload }) {
      const { checkup_id } = payload;
      return { ...state, checkup_id };
    },
    destroy_value(state: state<RestFul, StateType>, { payload }) {
      const { user_id, checkup_id } = payload;
      return { ...state, user_id, checkup_id };
    },
  },
};
export default personalModel;
