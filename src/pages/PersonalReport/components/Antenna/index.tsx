import { emptyOption, loadingOption } from '@/utils/handleCharts';
// import { Empty } from 'antd';
import * as echarts from 'echarts';
import React, { useEffect } from 'react';
import { connect } from 'umi';
import styles from '../../index.less';
import { PersonalRadar } from '../../service';

const option = (data: any) => {
  return {
    color: ['rgba(245, 166, 35, 1)', 'rgba(19, 173, 255, 1)'],
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `
        <div style="width:150px;display:flex;justify-content:space-between">
            <label>${data.text[0]}:</label>
            <span >${params.value[0]}</span>
        </div>
        <div style="width:150px;display:flex;justify-content:space-between">
        <label>${data.text[1]}:</label>
        <span >${params.value[1]}</span>
       </div>
        <div style="width:150px;display:flex;justify-content:space-between">
        <label>${data.text[2]}:</label>
        <span >${params.value[2]}</span>
        </div>
        <div style="width:150px;display:flex;justify-content:space-between">
        <label>${data.text[3]}:</label>
        <span >${params.value[3]}</span>
        </div>
        <div style="width:150px;display:flex;justify-content:space-between">
        <label>${data.text[4]}:</label>
        <span >${params.value[4]}</span>
        </div>
        <div style="width:150px;display:flex;justify-content:space-between">
        <label>${data.text[5]}:</label>
        <span >${params.value[5]}</span>
        </div>
        `;
      },
    },
    radar: {
      center: ['50%', '52%'],
      radius: '65%',
      startAngle: 90,
      splitNumber: 3,
      shape: 'circle',
      // grid: {
      //   // 控制图的大小，调整下面这些值就可以，
      //   x: 40,
      //   x2: 100,
      //   y2: 150, // y2可以控制 X轴跟Zoom控件之间的间隔，避免以为倾斜后造成 label重叠到zoom上
      // },
      name: {
        formatter: '{value}',
        textStyle: { color: '#909399 ', fontSize: 14 },
      },
      splitArea: { areaStyle: { color: ['rgba(255,255,255,0.08)'] } },
      axisLabel: {
        show: false,
        fontSize: 18,
        color: 'red',
        fontStyle: 'normal',
        fontWeight: 'normal',
      },
      axisLine: {
        // eslint-disable-next-line global-require
        symbol: ['none', `image://${require('@/../public/personalreport/cirle.svg')}`], // 箭头一端没效果,一端箭头
        symbolOffset: [0, 0], // 箭头段移动8个像素
        symbolSize: 7,
        lineStyle: { color: 'rgba(255, 255, 255, 0.1)' },// 分割线
      },
      splitLine: {
        show: true,
        lineStyle: { color: ['rgba(192, 196, 204, 0.3)'] },
      },
      indicator: [
        { text: `${data.text[0]} : ${data.value[0]}`, max: 100 },
        { text: `${data.text[1]} : ${data.value[1]}`, max: 100 },
        { text: `${data.text[2]} : ${data.value[2]}`, max: 100 },
        { text: `${data.text[3]} : ${data.value[3]}`, max: 100 },
        { text: `${data.text[4]} : ${data.value[4]}`, max: 100 },
        { text: `${data.text[5]} : ${data.value[5]}`, max: 100 },
      ],
    },
    series: [
      {
        name: '',
        type: 'radar',
        symbol: 'circle',
        symbolSize: 0,
        itemStyle: { color: '#fff', borderColor: 'rgba(0, 102, 255, 1)', borderWidth: 0.5 },
        areaStyle: { color: 'rgba(0, 102, 255, 1)', opacity: 0.2 },// 区域透明度
        lineStyle: { width: 2, smooth: true, color: 'rgba(0, 102, 255, 1)' },
        data: [{ value: data.value, name: '' }],
      },
    ],
  };
};

const Antenna: React.FC<any> = (props: any) => {
  function handleData(data: any[]) {
    const nameArray: any[] = [];
    const values: any[] = [];
    for (let i = 0; i < data.length; i += 1) {
      nameArray.push(data[i].name);
      values.push(data[i].score);
    }
    return { text: nameArray, value: values };
  }
  useEffect(() => {
    // const {checkup_id}=props
    if (props.checkup_id === 'null') {
      return;
    }
    const chartDom = document.getElementById('radars');
    if (chartDom == null) {
      return;
    }
    echarts.dispose(chartDom);
    const Le = echarts.init(chartDom, { render: 'svg' });
    window.addEventListener('resize', () => {
      Le.resize();
    });
    Le.showLoading(loadingOption);
    PersonalRadar({ checkup_id: props.checkup_id }).then((res: any) => {
      if (res.length < 1) {
        setTimeout(() => {
          Le.hideLoading();
        }, 100);
        Le.setOption(emptyOption);
        return;
      }
      setTimeout(() => {
        Le.hideLoading();
      }, 100);
      Le.setOption(option(handleData(res)));
    });
  }, [props.checkup_id]);

  return (
    <div className={styles.main}>
      <div id="radars" style={{ height: '15rem' }}></div>
    </div>
  );
};
export default connect((state: any) => {
  return state.personal;
})(Antenna);
