import { useEffect, useState } from 'react';
import { connect } from 'umi';
import styles from './index.less';
import GeneralModule from '@/components/GeneralPurposeModule';

const Asymmetric: React.FC<any> = (props: any) => {
  const [state, setState] = useState<any>({ flag: false, data: [] });
  const [loading, set] = useState(true);
  useEffect(() => {
    if (props.initState !== undefined && props.initState[0]?.name) {
      setTimeout(() => { set(false); }, 300);
      props.initState.map(item => {
        let id = '';
        item.user_id.split('').map((item, ind) => {
          if (ind <= 5) {
            id += item;
          } else if (ind >= 6 && ind <= 15) {
            id += '*'
          } else {
            id += item
          }
        })
        item.user_id = id;
        return item
      })
      setState({ flag: true, data: props.initState });
    } else {
      setTimeout(() => { set(false); }, 300);
      setState({ flag: false, data: [] });
    }
  }, [props.initState]);

  return (
    <div className={styles.Div}>
      <GeneralModule loading={loading} flag={state.flag}>
        {state.data.map((item: any, index: number) => {
          return (
            <ul key={index} className={styles.Ul}>
              <li>
                <span>性别:</span>
                <span>{item.gender}</span>
              </li>
              <li>
                <span>年龄:</span>
                <span>{item.age}</span>
              </li>
              <li>
                <span>ID账号:</span>
                <span>{item.user_id}</span>
              </li>
              <li>
                <span>体检档次:</span>
                <span>{item.type}</span>
              </li>
            </ul>
          );
        })}
      </GeneralModule>
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(Asymmetric);
