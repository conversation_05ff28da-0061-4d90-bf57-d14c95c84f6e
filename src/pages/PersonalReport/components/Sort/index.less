@import '~antd/es/style/themes/default.less';

.title {
  display: flex;
  justify-content: space-between;
  height: 2rem;
  margin-bottom: 0.8rem;
}
.top {
  display: flex;
}
.left {
  // flex: 6;
  // height: 50px;
  // padding: 12px;
  border-bottom: 1px solid #ebeef5;
}
.right {
  flex: 1;
}
.container {
  padding:0 16px;
}
.square {
  float: left;
  width: 4px;
  height: 1.3rem;
  margin-top: 0.25rem;
  margin-right: 8px;
  background-color: rgb(0, 149, 158);
  border-radius: 2px;
}
.titleText {
  height: 52px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
}
// .list {
//   margin-top: -35px;
// }
.box {
  display: flex;
  // justify-content: space-between;
  height: 1.5rem;
  margin: 10px 0;
}
.label {
  // flex: 5rem;
  width: 5rem;
  font-size: 1rem;
  line-height: 1.7rem;
  text-align: left;
}
.sort_suggestion{
  font-size: 14px;
  color: #909399;
  padding-top: 5px;
  margin:0 16px;
}
.content {
  flex: 1;
  // width: 14rem;
}
.images {
  width: 100%;
  height: 72%;
}
.docs {
  width: 16rem;
  .docs_title {
    color: #333333;
    font-size: 14px;
  }
  .docs_content {
    // color: #909399;
    font-size: 12px;
  }
}
