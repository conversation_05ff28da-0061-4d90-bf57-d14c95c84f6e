import { Popover } from 'antd';
import React from 'react';
import styles from './index.less';
import one from '../../../../../public/top_one.svg';
import two from '../../../../../public/top_two.svg';
import thr from '../../../../../public/top_thr.svg';
import forth from '../../../../../public/top_for.svg';
import five from '../../../../../public/top_five.svg';
import six from '../../../../../public/top_six.svg';
import exclamatory from '../../../../../public/exclamatory.svg';

const list = [
  { name: '中枢神经', img: one },
  { name: '心脏', img: two },
  { name: '血管', img: thr },
  { name: '激素', img: forth },
  { name: '酶', img: five },
  { name: '肾', img: six },
];

const Sort: React.FC<any> = () => {
  return (
    <div>
      <div className={styles.left}>
        <div className={styles.titleText}>
          器官重要性排名
          <Popover
            placement="bottomRight"
            content={() => {
              return (
                <div className={styles.docs}>
                  <div className={styles.docs_content}>
                    器官重要性仅适用于本套模型和算法，与体检者是否检查该部位无关，旨在说明本套模型中对人体健康影响最大的器官（组织/部位）。
                    器官（组织/部位）重要性排名越高，说明对人体躯体健康的影响越大。
                  </div>
                </div>
              );
            }}
          >
            <img src={exclamatory} alt="提示" />
          </Popover>
        </div>
      </div>
      <div className={styles.sort_suggestion}>
        仅适用于本套模型和算法，与体检者是否检查该部位无关
      </div>
      <div className={styles.container}>
        <div className={styles.list}>
          {list.map((v) => {
            return (
              <div key={v.name} className={styles.box}>
                <div className={styles.label}>{v.name}</div>
                <div className={styles.content}>
                  <img className={styles.images} src={v.img} alt={v.name} />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Sort;
