@import '~antd/es/style/themes/default.less';

.emptyCard {
  margin-top: 180px;
}
:global {
  .ant-modal-body {
    padding: 0.8rem 1.8rem 1rem 2rem;
    font-size: 0.875rem;
    line-height: 1.5715;
    word-wrap: break-word;
  }
  .ant-progress-line {
    flex: 12rem;
  }
  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #06f;
    font-size: 16px;
  }
  .ant-card-bordered {
    height: auto;
    border: none;
  }
  .ant-tabs-tab-btn {
    font-size: 16px;
    outline: none;
    transition: all 0.3s;
  }
  .ant-tabs-ink-bar {
    position: absolute;
    height: 3px !important;
    background: #06f;
  }
  .ant-tabs-ink-bar::after {
    position: absolute;
    top: -133%;
    left: 46px;
    width: 40px;
    height: 40px;
    background-size: 100%;
    content: '';
  }
  .ant-progress-text {
    color: inherit;
  }

  ::-webkit-scrollbar {
    width: 5px;
    height: 80px;
    background: #d9dee8;
    border-radius: 4px;
    opacity: 1;
  }
  ::-webkit-scrollbar-track {
    background: #d9dee8;
    border-radius: 4px;
  }
  ::-webkit-scrollbar-thumb {
    background: #bfbfbf;
    border-radius: 10px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: rgb(151, 160, 149);
  }
  ::-webkit-scrollbar-corner {
    background: #179a16;
  }

  ::-webkit-scrollbar:horizontal {
    display: none;
  }
}
.rowLeft {
  float: left;
  width: 66%;
}
.modalLeft {
  font-size: 18px;
}
.rowRight {
  float: right;
  width: 66%;
  // cursor: pointer;
}
.grid {
  display: flex;
  flex-direction: row;
  margin: 8px 0;
}
.contentStyle {
  height: 26rem;
  color: #fff;
}
.container {
  margin-top: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  ul {
    display: none;
  }
  .label {
    flex: 64px;
    height: 22px;
    color: #bfc2c6;
    font-size: small;
    line-height: 22px;
  }
  .body {
    width: 100%;
    height: 100%;
    background-image: url('../../../../../public/personalreport/userModel.svg');
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 50% 90%;
  }
  .img {
    margin: 0 auto;
  }
  .bodyColumn {
    background-image: url('../../../../../public/personalreport/bodyCircle.svg');
    background-repeat: no-repeat;
    background-position: 50% 40%;
    background-size: 70% 55%;
  }
  .cardWrapper {
    height: 6.43rem;
    margin-bottom: 2.6rem;
  }
  .rightPart,
  .whiteRightPart {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 24px;
    height: 18px;
    text-align: center;
    border: none;
    border-radius: 4px 0 4px 0;
    cursor: pointer;
    opacity: 1;
  }
  .partIcon,
  .whitePartIcon {
    position: absolute;
    top: 0.8125rem;
    right: 0;
    width: 40px;
    height: 40px;
    // text-align: right;
    background: none;
    border: none;
    cursor: pointer;
    span {
      display: inline-block;
      width: 20px;
      height: 22px;
      color: rgba(0, 149, 158, 1);
      color: white;
      background-repeat: no-repeat;
      background-position: center;
    }
  }
  .right_score {
    flex: 4rem;
  }
  .hoverPro {
    float: right;
    // padding-left: 4px;
    color: white;
  }
  .proScore {
    float: right;
    color: #909399;
  }
  .partIcon > span {
    background-image: url('../../../../../public/personalreport/partPage1.svg');
  }
  .whitePartIcon > span {
    background-image: url('../../../../../public/personalreport/white/partPage1.svg');
  }
  .partIconFooter,
  .whitePartIconFooter {
    top: 30px;
    right: -4px;
    display: inline-block;
    width: 13px;
    height: 16px;
    text-align: center;
    background-repeat: no-repeat;
    background-position: center;
  }
  .whitePartIconFooter {
    background-image: url('../../../../../public/personalreport/white/keyboard-arrow-right.svg');
  }
  .partIconFooter {
    background-image: url('../../../../../public/personalreport/keyboard-arrow-right.svg');
  }
  :global .ant-carousel .slick-dots-bottom {
    bottom: -25px;
    display: block !important;
    min-width: 69px;
    text-align: center;
  }
  :global .ant-carousel .slick-dots li {
    width: 18px;
    height: 6px;
    background: none;
  }
  :global .ant-carousel .slick-dots li.slick-active {
    background: none;
  }
  :global {
    .ant-carousel .slick-dots li button {
      width: 18px;
      height: 6px;
      background-color: #909399;
      border-radius: 6px;
    }
    .ant-carousel .slick-dots li.slick-active button {
      width: 18px;
      height: 6px;
      background-color: #06f;
      border-radius: 8px;
    }
  }
}

.progress {
  :global {
    .ant-progress-inner {
      margin-left: -8px;
      background-color: #eaede8;
      opacity: 1;
    }
  }
}
.WeiTi {
  min-width: 43px;
  height: 19px;
  margin-top: 5px;
  margin-top: 7px;
  color: #06f;
  font-weight: 400;
  font-size: 12px;
  font-family: PingFang SC;
  line-height: 17px;
  line-height: 19px;
  text-align: center;
  background: rgba(0, 102, 255, 0.1);
  opacity: 1;
}
.Whitedd {
  min-width: 38px;
  height: 19px;
  margin-top: 5px;
  margin-top: 7px;
  color: #fff;
  font-weight: 400;
  font-size: 12px;
  font-family: PingFang SC;
  line-height: 17px;
  line-height: 19px;
  text-align: center;
  background: rgba(0, 102, 255, 0.1);
  opacity: 1;
}
.hoverProgress {
  :global {
    .ant-progress-inner {
      background-color: rgba(256, 256, 256, 0.24);
    }
    .ant-progress-status-success .ant-progress-text {
      color: #fff;
    }
  }
}
.modalPageFooter {
  position: relative;
  width: 48px;
  height: 12px;
  margin-left: 14px;
  background-color: #000;
  border: none;
  border-radius: 4px;
  outline: none;
  visibility: visible !important;
  cursor: pointer;
}
.modalPageFooter2 {
  position: relative;
  width: 48px;
  height: 12px;
  margin-left: 14px;
  background-color: #e6d2d2;
  border: none;
  border-radius: 4px;
  outline: none;
  visibility: visible !important;
  cursor: pointer;
  // opacity: 1;
}
