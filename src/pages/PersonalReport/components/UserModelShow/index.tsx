/* eslint-disable react/no-array-index-key */
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Col, Progress, Row, Modal, Carousel, Empty, Tabs } from 'antd';
import EmptyCard from '../../../../components/HaiYulComponents/empty';
import IconCard from '../../../../components/HaiYulComponents/IconCard';
import DetailModel from './detailModel/index';
import AbNormalTab from './choose/index';
import AbNormal from './choose/Qualitative';
import LinePhoto from './choose/linePicture';
import Grading from '@/components/Grading';
import { getDataPOST, handleCardData } from '../../service';
import Url from '../../url';
import { connect } from '@/.umi/plugin-dva/exports';

const { TabPane } = Tabs;
const colors = (score: any) => {
  if (score <= 50) {
    return '#ccc';
  }
  if (score >= 60 && score <= 99) {
    return '#ccc';
  }
  return '#ccc';
};

const Colors = (score: any) => {
  let color = '';
  if (score <= 60) {
    color = 'red';
  }
  if (score >= 60 && score <= 99) {
    color = '#579EF0';
  }
  if (score === 100) {
    color = '#A1D36D';
  }
  return color;
};
const UserModelShow: React.FC<API.ItemProps> = (props) => {
  const [data, setData] = useState<API.UserModelShowState>({
    hoverKey: 'null',
    modal1Visible: false,
    modal2Visible: false,
    modal3Visible: false,
    modal4Visible: false,
    flag: 0,
    GradingFlag: true,
    cardData: undefined,
  });
  const [GradingFlag, setGradingFlag] = useState<any>(true);
  const [params, setParam] = useState<API.params>({
    checkup_id: props.checkup_id,
    l0_name: props.name1,
    l1_name: props.name2,
    l3_name: '',
    l5_name: '',
  });

  const [modalFive, setModalFive] = useState<any>([]);
  const [itemName, setName] = useState<any>(undefined);
  const [flagOne, setFlagOne] = useState(true);
  const [keys, setKeys] = useState('1');
  const { dispatch } = props;
  function changeKeys(key: any) {
    setKeys(key);
    if (key === '2') {
      setGradingFlag(true);
    }
  }
  function setModal2Visible(
    modal2Visible: any,
    l0_name?: string,
    l1_name?: string,
    l3_name?: string,
  ) {
    if (modal2Visible === false) {
      setKeys('1');
      setData({ ...data, modal2Visible, flag: 0 });
      setGradingFlag(true);
      dispatch({ type: 'personal/handleCancel', payload: { key: 'cancel' } });
      return;
    }

    setData({ ...data, modal2Visible });
    setName(l3_name);
    setParam({ ...params, l0_name, l1_name, l3_name });
    // 弹框得分详情接口
    if (l0_name) {
      dispatch({
        type: 'personal/handleModal',
        payload: {
          checkup_id: props.checkup_id,
          l0_name,
          l1_name,
          l3_name,
          user_id: props.user_id,
        },
      });
    }
  }
  /**
   * @name 弹窗二级页面
   */
  function handleChange(e: any, l5_name: any) {
    // 点击详情里面的页面接口
    const { l0_name, l1_name, l3_name, checkup_id } = params;
    dispatch({ type: 'personal/handleFetch', payload: { l0_name, l1_name, l3_name, l5_name, checkup_id } });
    getDataPOST(Url.KEY_WORD, { l0_name, l1_name, l3_name, checkup_id, l5_name }).then((res: any) => {
      if (res === [] || res === {} || res === '' || res.url !== undefined) {
        setModalFive([]);
      } else {
        setModalFive(res);
      }
    });
    setGradingFlag(e);
    setParam({ ...params, l5_name });
  }
  useEffect(() => {
    if (props.checkup_id === 'null') {
      return;
    }
    const { l0_name, l1_name } = params;
    getDataPOST(Url.scoreLeftBottom, { l0_name, l1_name, checkup_id: props.checkup_id }).then((res) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      res.url === undefined ? setFlagOne(true) : setFlagOne(false);
      setData({ ...data, cardData: handleCardData(res) });
    }).catch(() => {
      setFlagOne(false);
    });
  }, [props.checkup_id]);

  if (flagOne) {
    return (
      <div className={styles.container}>
        {data.cardData === undefined ? ('') : (
          <div className={styles.bodyColumn}>
            <div className={styles.body}>
              <Carousel dots arrows={true} dotPosition={'bottom'}>
                {data.cardData.map((v: any, idx: number) => {
                  return (
                    <div key={idx}>
                      <Row key={idx} className={styles.contentStyle}>
                        <Col span={12}>
                          {v.filter((_item: any, index: number) => index % 2 === 0).map((item: any, index: number) => {
                            return (
                              <div key={index} className={styles.rowLeft}>
                                {item.name === undefined ? (<EmptyCard />) : (
                                  <div
                                    className={styles.cardWrapper}
                                    key={`0-${index}`}
                                    onMouseEnter={() => { setData({ ...data, hoverKey: `0-${index}` }); }}
                                    onMouseLeave={() => { setData({ ...data, hoverKey: 'null' }); }}     >
                                    {item.l3_is_checked_flag === false ? (
                                      <IconCard
                                        onClick={() => setModal2Visible(false)}
                                        icon={
                                          data.hoverKey === `0-${index}` ? `icon-a-${item.icon_id}-hover` : `icon-a-${item.icon_id}`
                                        }
                                        desc={item.l3_name_desc}
                                        name={
                                          item.l3_is_checked_flag === false ? (
                                            <div className={data.hoverKey === `0-${index}` ? styles.Whitedd : styles.WeiTi}>
                                              未体检
                                            </div>
                                          ) : null
                                        }
                                        title={item.name}
                                        iconBackgroundColor={data.hoverKey === `0-${index}` ? '#3887FF' : '#F5F9FF'}
                                        bodyStyle={{
                                          backgroundColor: data.hoverKey === `0-${index}` ? '#3887FF' : '#F5F9FF',
                                          borderRadius: 4,
                                        }}
                                        titleStyle={{ color: data.hoverKey === `0-${index}` ? 'white' : '#303133', }}
                                        style={{ marginBottom: 32, borderRadius: 4 }}>
                                        {item.l3_is_checked_flag === false ? (
                                          <div className={styles.grid}>
                                            <label
                                              className={styles.label}
                                              style={{ color: data.hoverKey === `0-${index}` ? 'white' : '#BFC2C6' }}>
                                              得分
                                            </label>
                                            <Progress
                                              className={data.hoverKey === `0-${index}` ? styles.hoverProgress : styles.progress}
                                              style={{ width: '65%' }}
                                              strokeColor={data.hoverKey === `0-${index}` ? 'white' : colors(item.score)}
                                              strokeWidth={5}
                                              percent={item.score}
                                              type={'line'}
                                              showInfo={false} />
                                            <span
                                              className={data.hoverKey === `0-${index}` ? styles.hoverPro : styles.proScore}  >
                                              {item.score}
                                            </span>
                                          </div>
                                        ) : (
                                          <div className={styles.grid}>
                                            <label
                                              className={styles.label}
                                              style={{ color: data.hoverKey === `0-${index}` ? 'white' : '#BFC2C6' }}
                                            >
                                              得分
                                            </label>
                                            <Progress
                                              className={data.hoverKey === `0-${index}` ? styles.hoverProgress : styles.progress}
                                              style={{ width: '65%', color: data.hoverKey === `0-${index}` ? 'white' : '#909399' }}
                                              strokeColor={data.hoverKey === `0-${index}` ? 'white' : Colors(item.score)}
                                              strokeWidth={5}
                                              percent={item.score}
                                              showInfo={false} />
                                            <span
                                              className={data.hoverKey === `0-${index}` ? styles.hoverPro : styles.proScore} >
                                              {item.score}
                                            </span>
                                          </div>
                                        )}
                                      </IconCard>
                                    ) : (
                                      <IconCard
                                        onClick={() => setModal2Visible(true, params.l0_name, params.l1_name, item.name)}
                                        icon={data.hoverKey === `0-${index}` ? `icon-a-${item.icon_id}-hover` : `icon-a-${item.icon_id}`}
                                        desc={item.l3_name_desc}
                                        name={
                                          item.l3_is_checked_flag === false ? (
                                            <div className={data.hoverKey === `0-${index}` ? styles.Whitedd : styles.WeiTi}>
                                              未体检
                                            </div>
                                          ) : null
                                        }
                                        title={item.name}
                                        iconBackgroundColor={
                                          data.hoverKey === `0-${index}` ? '#3887FF' : '#F5F9FF'
                                        }
                                        bodyStyle={{
                                          backgroundColor: data.hoverKey === `0-${index}` ? '#3887FF' : '#F5F9FF',
                                          borderRadius: 4,
                                        }}
                                        titleStyle={{ color: data.hoverKey === `0-${index}` ? 'white' : '#303133' }}
                                        style={{ marginBottom: 32, borderRadius: 4 }}
                                      >
                                        {item.l3_is_checked_flag === false ? (
                                          <div className={styles.grid}>
                                            <label
                                              className={styles.label}
                                              style={{ color: data.hoverKey === `0-${index}` ? 'white' : '#BFC2C6' }}>
                                              得分
                                            </label>
                                            <Progress
                                              className={data.hoverKey === `0-${index}` ? styles.hoverProgress : styles.progress}
                                              style={{ width: '65%' }}
                                              strokeColor={data.hoverKey === `0-${index}` ? 'white' : colors(item.score)}
                                              strokeWidth={5}
                                              percent={item.score}
                                              type={'line'}
                                              showInfo={false}
                                            />
                                            <span className={data.hoverKey === `0-${index}` ? styles.hoverPro : styles.proScore}>
                                              {item.score}
                                            </span>
                                          </div>
                                        ) : (
                                          <div className={styles.grid}>
                                            <label
                                              className={styles.label}
                                              style={{ color: data.hoverKey === `0-${index}` ? 'white' : '#BFC2C6' }}
                                            >
                                              得分
                                            </label>
                                            <Progress
                                              className={data.hoverKey === `0-${index}` ? styles.hoverProgress : styles.progress}
                                              style={{
                                                width: '65%',
                                                color: data.hoverKey === `0-${index}` ? 'white' : '#909399',
                                              }}
                                              strokeColor={data.hoverKey === `0-${index}` ? 'white' : Colors(item.score)}
                                              strokeWidth={5}
                                              percent={item.score}
                                              showInfo={false}
                                            />
                                            <span className={data.hoverKey === `0-${index}` ? styles.hoverPro : styles.proScore}>
                                              {item.score}
                                            </span>
                                          </div>
                                        )}
                                      </IconCard>
                                    )}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </Col>
                        <Col span={12}>
                          {v.filter((item: any, index: number) => index % 2 === 1).map((item: any, index: number) => {
                            return (
                              <div key={index} className={styles.rowRight}>
                                {item.name === undefined ? (<EmptyCard key={`1-${index}`} />) : (
                                  <div
                                    className={styles.cardWrapper}
                                    key={`1-${index}`}
                                    onMouseEnter={() => { setData({ ...data, hoverKey: `1-${index}` }); }}
                                    onMouseLeave={() => { setData({ ...data, hoverKey: 'null' }); }}
                                  >
                                    {item.l3_is_checked_flag === false ? (
                                      <IconCard
                                        onClick={() => setModal2Visible(false)}
                                        icon={
                                          data.hoverKey === `1-${index}` ? `icon-a-${item.icon_id}-hover` : `icon-a-${item.icon_id}`
                                        }
                                        desc={item.l3_name_desc}
                                        name={
                                          item.l3_is_checked_flag === false ? (
                                            <div className={data.hoverKey === `1-${index}` ? styles.Whitedd : styles.WeiTi}>
                                              未体检
                                            </div>
                                          ) : null
                                        }
                                        title={item.name}
                                        iconBackgroundColor={
                                          data.hoverKey === `1-${index}` ? '#3887FF' : '#F5F9FF'
                                        }
                                        bodyStyle={{
                                          backgroundColor: data.hoverKey === `1-${index}` ? '#3887FF' : '#F5F9FF',
                                          borderRadius: 4,
                                        }}
                                        titleStyle={{ color: data.hoverKey === `1-${index}` ? 'white' : '#303133' }}
                                        style={{ marginBottom: 32, borderRadius: 4 }}
                                      >
                                        {item.l3_is_checked_flag === false ? (
                                          <div className={styles.grid}>
                                            <label
                                              className={styles.label}
                                              style={{ color: data.hoverKey === `1-${index}` ? 'white' : '#BFC2C6' }}
                                            >
                                              得分
                                            </label>
                                            <Progress
                                              style={{
                                                width: '65%',
                                                color: data.hoverKey === `1-${index}` ? 'white' : '#909399',
                                              }}
                                              strokeColor={data.hoverKey === `1-${index}` ? 'white' : colors(item.score)}
                                              className={data.hoverKey === `1-${index}` ? styles.hoverProgress : styles.progress}
                                              strokeWidth={5}
                                              showInfo={false}
                                              percent={item.score}
                                            />
                                            <span className={data.hoverKey === `1-${index}` ? styles.hoverPro : styles.proScore}>
                                              {item.score}
                                            </span>
                                          </div>
                                        ) : (
                                          <div className={styles.grid}>
                                            <label
                                              className={styles.label}
                                              style={{ color: data.hoverKey === `1-${index}` ? 'white' : '#BFC2C6' }}
                                            >
                                              得分
                                            </label>
                                            <Progress
                                              style={{
                                                width: '65%',
                                                color: data.hoverKey === `1-${index}` ? 'white' : '#909399',
                                              }}
                                              strokeColor={data.hoverKey === `1-${index}` ? 'white' : Colors(item.score)}
                                              className={data.hoverKey === `1-${index}` ? styles.hoverProgress : styles.progress}
                                              strokeWidth={5}
                                              showInfo={false}
                                              percent={item.score}
                                            />
                                            <span className={data.hoverKey === `1-${index}` ? styles.hoverPro : styles.proScore}>
                                              {item.score}
                                            </span>
                                          </div>
                                        )}
                                      </IconCard>
                                    ) : (
                                      <IconCard
                                        onClick={() => setModal2Visible(true, params.l0_name, params.l1_name, item.name)}
                                        desc={item.l3_name_desc}
                                        icon={
                                          data.hoverKey === `1-${index}` ? `icon-a-${item.icon_id}-hover` : `icon-a-${item.icon_id}`
                                        }
                                        name={
                                          item.l3_is_checked_flag === false ? (
                                            <div className={data.hoverKey === `1-${index}` ? styles.Whitedd : styles.WeiTi}>
                                              未体检
                                            </div>
                                          ) : null
                                        }
                                        title={item.name}
                                        iconBackgroundColor={data.hoverKey === `1-${index}` ? '#3887FF' : '#F5F9FF'}
                                        bodyStyle={{
                                          backgroundColor: data.hoverKey === `1-${index}` ? '#3887FF' : '#F5F9FF',
                                          borderRadius: 4,
                                        }}
                                        titleStyle={{ color: data.hoverKey === `1-${index}` ? 'white' : '#303133' }}
                                        style={{ marginBottom: 32, borderRadius: 4 }}
                                      >
                                        {item.l3_is_checked_flag === false ? (
                                          <div className={styles.grid}>
                                            <label
                                              className={styles.label}
                                              style={{ color: data.hoverKey === `1-${index}` ? 'white' : '#BFC2C6' }}
                                            >
                                              得分
                                            </label>
                                            <Progress
                                              style={{
                                                width: '65%',
                                                color: data.hoverKey === `1-${index}` ? 'white' : '#909399',
                                              }}
                                              strokeColor={data.hoverKey === `1-${index}` ? 'white' : colors(item.score)}
                                              className={data.hoverKey === `1-${index}` ? styles.hoverProgress : styles.progress}
                                              strokeWidth={5}
                                              showInfo={false}
                                              percent={item.score}
                                            />
                                            <span className={data.hoverKey === `1-${index}` ? styles.hoverPro : styles.proScore}>
                                              {item.score}
                                            </span>
                                          </div>
                                        ) : (
                                          <div className={styles.grid}>
                                            <label
                                              className={styles.label}
                                              style={{ color: data.hoverKey === `1-${index}` ? 'white' : '#BFC2C6' }}
                                            >
                                              得分
                                            </label>
                                            <Progress
                                              style={{
                                                width: '65%',
                                                color: data.hoverKey === `1-${index}` ? 'white' : '#909399',
                                              }}
                                              strokeColor={data.hoverKey === `1-${index}` ? 'white' : Colors(item.score)}
                                              className={data.hoverKey === `1-${index}` ? styles.hoverProgress : styles.progress}
                                              strokeWidth={5}
                                              showInfo={false}
                                              percent={item.score}
                                            />
                                            <span className={data.hoverKey === `1-${index}` ? styles.hoverPro : styles.proScore}>
                                              {item.score}
                                            </span>
                                          </div>
                                        )}
                                      </IconCard>
                                    )}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </Col>
                      </Row>
                    </div>
                  );
                })}
              </Carousel>
            </div>
            {!data.modal2Visible ? ('') : (
              <Modal
                title={<div className={styles.modalTitle}>
                  <div className={styles.modalLeft}>{itemName}组织数据</div>
                </div>}
                centered
                visible={data.modal2Visible}
                onOk={() => setModal2Visible(false)}
                onCancel={() => setModal2Visible(false)}
                okButtonProps={{ disabled: true }}
                cancelButtonProps={{ disabled: true }}
                width={717}
                footer={null}
                maskStyle={{ opacity: 0.3 }}
                bodyStyle={{ overflow: 'hidden' }}
              >
                <div className={styles.Tabs}>
                  <Tabs activeKey={keys} onChange={changeKeys}>
                    <TabPane tab={`${itemName}历年得分数据`} key="1">
                      <LinePhoto />
                    </TabPane>
                    <TabPane tab={`${itemName}得分详情`} key="2">
                      {GradingFlag === true ? (
                        <DetailModel changeGrading={(e: any, l5_name: any) => { handleChange(e, l5_name); }} />
                      ) : (
                        <Grading name={params.l5_name} List={modalFive} changeGrading={(e: any) => { setGradingFlag(e); }} />
                      )}
                    </TabPane>
                    <TabPane tab="报告类项目结果" key="3">
                      <AbNormal></AbNormal>
                    </TabPane>
                    <TabPane tab="实验项目项目结果" key="4">
                      <AbNormalTab></AbNormalTab>
                    </TabPane>
                  </Tabs>
                </div>
              </Modal>
            )}
          </div>
        )}
      </div>
    );
  }
  return (
    <div className={styles.container}>
      <Empty style={{ position: 'relative', marginTop: 150 }} />
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(UserModelShow);
