.container {
  height: 420px;
  padding-right: 16px;
  overflow-y: auto;
  scrollbar-width: thin;
  ::-webkit-scrollbar {
    width: 5px;
    height: 80px;
    background: #d9dee8;
    border-radius: 4px;
    opacity: 1;
  }
  ::-webkit-scrollbar-track {
    background: #d9dee8;
    border-radius: 4px;
  }
  ::-webkit-scrollbar-thumb {
    background: #bfbfbf;
    border-radius: 10px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: rgb(151, 160, 149);
  }
  ::-webkit-scrollbar-corner {
    background: #179a16;
  }

  ::-webkit-scrollbar:horizontal {
    display: none;
  }
  .group {
    display: flex;
    justify-content: space-between;
    .left {
      flex: 9;
    }
    .right {
      flex: 1;
      .arrow_back {
        float: right;
        width: 22px;
        height: 22px;
        margin: 20px auto;
        // background-color: rgb(245, 244, 244);
        background-repeat: no-repeat;
        background-position: center;
        border: 1px solid #d0d4dd;
        border-radius: 11px;
        cursor: pointer;
      }
    }
  }
}
