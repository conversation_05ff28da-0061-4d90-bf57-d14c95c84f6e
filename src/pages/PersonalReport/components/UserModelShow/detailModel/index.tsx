import { Empty, Progress } from 'antd';
import styles from './index.less';
import arrow from '@/../public/personalreport/arrow_right.svg';
import { connect } from '@/.umi/plugin-dva/exports';
import { useEffect, useState } from 'react';
import React from 'react';

const DetailModel: React.FC<API.detail> = (props: any) => {
  const [data, setData] = useState<any>(undefined);
  const status = { warning: 'rgba(245, 66, 66, 1)', safe: 'rgba(0, 102, 255, 1)', health: 'rgba(152,211,76,1)' };

  /** @function 更改父组件l5的值,并且返回上层组件 */
  function handleChange(top_name: string) {
    props.changeGrading(false, top_name);
  }

  /** 通用分数颜色 */
  function handleColor(score: any) {
    let color;
    if (score === 100) {
      color = status.health;
    } else if (score > 60) {
      color = status.safe;
    } else {
      color = status.warning;
    }
    return color;
  }

  useEffect(() => {
    if (props.detailModal && props.detailModal.length < 1) {
      return;
    }
    const final = props.detailModal.sort((a: any, b: any) => a.score - b.score);
    setData(final);
  }, [props.detailModal]);

  if (data === undefined || data.length < 1) {
    return (<div className={styles.container} style={{ display: 'flex', alignItems: 'center' }}>
      <Empty style={{ flex: '1' }}></Empty>
    </div>);
  }
  return (
    <div className={styles.container}>
      {props.detailModal.map((v: any, i: any) => {
        return (
          // eslint-disable-next-line react/no-array-index-key
          <div key={i} className={styles.group}>
            <div className={styles.left}>
              <div style={{ marginBottom: 8, height: 20 }}>
                <div style={{ float: 'left', color: '#606266' }}>{v.name}</div>
                <div style={{ float: 'right' }}>
                  <span style={{ fontWeight: 'bolder', color: handleColor(v.score) }}>{v.score}</span>
                  <span style={{ color: 'rgba(192, 196, 204, 1)', marginLeft: 2 }}>/</span>
                  <span style={{ color: 'rgba(192, 196, 204, 1)' }}>100</span>
                </div>
              </div>
              <div style={{ marginBottom: 18 }}>
                <Progress strokeColor={handleColor(Number(v.score))} status="normal" percent={v.score} showInfo={false}></Progress>
              </div>
            </div>
            <div className={styles.right}>
              <div className={styles.arrow_back} onClick={() => handleChange(v.name)} style={{ backgroundImage: `url(${arrow})` }}></div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(DetailModel);
