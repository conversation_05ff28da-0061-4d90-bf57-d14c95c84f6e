import { Popover, Rate, Table } from 'antd';
import styles from './index.less';
import gt from '@/../public/personalreport/gt.svg';
import lt from '@/../public/personalreport/lt.svg';
import Edit from '@/../public/personalreport/edit.svg';
import l3_notice from '../../../../../../public/l3_notice.svg';
import { useEffect, useState } from 'react';
import { connect } from 'umi';
import { compare, handleKeys } from '@/pages/PersonalReport/service';
import Content from '@/components/Grading/content';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Flash } from '@/components/MenuIcon';
import FeedBack from '@/components/FeedBack';
import type { ColumnsType } from 'antd/lib/table';

const MIN_content = () => {
  return <div>体检者乙肝的诊断需结合多个相关定量指标。</div>;
};
const TOG_content = () => {
  return <div>实验室类项目的异常要结合报告类项目才有临床意义。</div>;
};
const HB_content = () => {
  return <div>该指标异常与否要参考绝对值是否异常</div>;
};
/** @function 警告文本 */
const GeneralContentText = (v: any, content: any) => {
  return (
    <div key={v.key}>
      <Popover content={content}><img src={l3_notice} alt={'警告'} /></Popover>
      {v.index_name}
    </div>
  );
};

const GeneralCompare = (v: any, photo: any, color: string) => {
  if (photo === 'null') {
    return (<div key={v.key}><span style={{ color: '#909399' }}>{v.checkup_res}</span></div>);
  }
  return (
    <div key={v.key}>
      <span style={{ color }}>
        {v.checkup_res}
        <img src={photo} style={{ marginLeft: '8px' }} />
      </span>
    </div>
  );
};

function handleColor(star: number) {
  if (star > 3) {
    return '#F00F00';
  }
  if (star > 2) {
    return '#FFB52B';
  }
  return '#06f';
}

const getRowClassName = (_: any, index: number) => {
  let className = '';
  className = index % 2 === 0 ? styles.evenRow : styles.oddRow;
  return className;
};

type state = Omit<API.FeedState, 'changeBack'>;

const AbNormalTab = (props: any) => {
  const [data, set] = useState<any>([]);
  const [loading, setLoad] = useState(true);
  const [state, setState] = useState<state>({
    back: false,
    keyword: '',
    index_coding: '',
    item_coding: '',
    item_name: '',
    index_name: '',
    cancel: false,
    star: 0,
    flag: true,
  });
  /** 点击调起传值函数 */
  function callback(
    star: number,
    keyword: string,
    item_coding?: string,
    item_name?: string,
    index_coding?: string,
    index_name?: string,
    reference_value?: string,
  ) {
    setState({
      ...state,
      back: true,
      keyword: `${keyword} ${reference_value}`,
      star,
      index_coding,
      item_coding,
      item_name,
      index_name,
    });
  }

  const columns: ColumnsType | undefined = [
    {
      title: '项目名称', dataIndex: 'item_name', align: 'left', width: 200,
      render: (item: any, v: any) => {
        if (v.is_ignore_hb === true) {
          return GeneralContentText(v, MIN_content);
        }
        if (v.is_ignore_min === true) {
          return GeneralContentText(v, TOG_content);
        }
        if (v.is_ignore_tog === true) {
          return GeneralContentText(v, HB_content);
        }
        return <div key={v.key}>{v.index_name}</div>;
      },
    },
    { title: '正常值范围', dataIndex: 'reference_value', align: 'left', width: 160 },
    {
      title: '结果', dataIndex: 'checkup_res', width: 150, align: 'left',
      render: (_: any, v: any) => {
        switch (v.checkup_res_contrast) {
          case 'gt': { return GeneralCompare(v, gt, '#FF574B'); }
          case 'lt': { return GeneralCompare(v, lt, '#1677FF'); }
          default: { return GeneralCompare(v, 'null', '#909399'); }
        }
      },
    },
    {
      title: () => {
        return (<div>
          <span>关心程度</span>
          <Popover content={Content}>
            <InfoCircleOutlined className={styles.icon} style={{ position: 'relative', left: 4, top: 1 }} />
          </Popover>
        </div>);
      },
      dataIndex: '关爱程度', width: 140,
      render: (star: any) => {
        return (<Rate character={<Flash />}
          style={{ color: handleColor(Number(star)) }}
          disabled={true}
          value={Number(star)}
          allowHalf={true}
        ></Rate>);
      },
    },
    {
      title: '反馈', dataIndex: 'score', width: 80,
      render: (_: any, v: any) => {
        return (<div
          style={{ cursor: 'pointer' }}
          onClick={() => {
            callback(
              Number(v.关爱程度),
              v.checkup_res,
              v.item_coding,
              v.item_name,
              v.index_coding,
              v.index_name,
              v.reference_value,
            );
          }}>
          <img src={Edit} alt="反馈" />
        </div>);
      },
    },
  ];
  useEffect(() => {
    setTimeout(() => {
      setLoad(false);
    }, 500);
    if (props.quantify.length > 0) {
      set(props.quantify.sort(compare('关爱程度')));
    } else {
      set([]);
    }
  }, [props.quantify]);
  return (
    <div className={styles.table}>
      <Table
        loading={loading}
        columns={columns}
        key={'index'}
        rowKey={'keys'}
        rowClassName={getRowClassName}
        dataSource={handleKeys(data)}
        pagination={false}
      ></Table>
      <FeedBack
        changeBack={(e: boolean) => { setState({ ...state, back: e }); }}
        checkup_id={props.checkup_id}
        data={state}
        back={state.back}
        star={state.star}
      ></FeedBack>
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(AbNormalTab);
