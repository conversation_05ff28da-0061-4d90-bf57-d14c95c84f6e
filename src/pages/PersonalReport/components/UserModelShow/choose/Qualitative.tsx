import { connect } from 'umi';
import styles from './Qualitative.less';
import { useEffect, useState } from 'react';
import { Popover, Rate, Table } from 'antd';
import { Flash } from '@/components/MenuIcon';
import FeedBack from '@/components/FeedBack';
import Edit from '@/../public/personalreport/edit.svg';
import { InfoCircleOutlined } from '@ant-design/icons';
import Content from '@/components/Grading/content';
import { handleColor } from '@/services/person_report';
import { compare } from '@/pages/PersonalReport/service';
// import l3_notice from '../../../../../../public/l3_notice.svg';

const getRowClassName = (_: any, index: number) => {
  let className = '';
  className = index % 2 === 0 ? styles.evenRow : styles.oddRow;
  return className;
};
const Qualitative = (props: any) => {
  const [data, set] = useState<any>([]);
  const [loading, setLoad] = useState(true);
  const [state, setState] = useState<any>({
    back: false,
    keyword: '',
    item_coding: '',
    item_name: '',
    index_coding: '',
    index_name: '',
    star: 0,
  });
  function callback(
    star: number,
    keyword: string,
    item_coding?: string,
    item_name?: string,
    index_coding?: string,
    index_name?: string,
  ) {
    setState({
      ...state,
      back: true,
      keyword,
      star,
      item_coding,
      item_name,
      index_coding,
      index_name,
    });
  }
  const columns: any = [
    {
      title: '项目名称', dataIndex: '项目名称', align: 'left',
      render: (_: any, v: any) => {
        return (<div key={v.key}>
          {v.指标名称}
          {v.项目名称}
        </div>);
      },
    },
    { title: '关键词', dataIndex: '关键词', align: 'left' },
    {
      title: () => {
        return (
          <><span>关心程度</span>
            <Popover content={<Content></Content>}>
              <InfoCircleOutlined className={styles.icon} style={{ position: 'relative', left: 4, top: 1 }} />
            </Popover></>
        );
      },
      dataIndex: '关爱程度',
      render: (v: any) => {
        return (<Rate
          character={<Flash />}
          style={{ color: handleColor(Number(v)) }}
          disabled={true}
          value={Number(v)}
          allowHalf={true}
        ></Rate>);
      },
    },
    {
      title: '反馈', dataIndex: '扣分', width: 80,
      render: (_: any, v: any) => {
        return (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => {
              callback(Number(v.关爱程度), v.关键词, v.项目编码, v.项目名称, v.指标编码, v.指标名称);
            }}>
            <img src={Edit} alt="反馈" />
          </div>
        );
      },
    },
  ];
  useEffect(() => {
    setTimeout(() => {
      setLoad(false);
    }, 500);
    if (props.qual.length > 0) {
      set(props.qual.sort(compare('关爱程度')));
    } else {
      set([]);
    }
  }, [props.qual]);
  return (
    <div className={styles.table}>
      <Table
        loading={loading}
        columns={columns}
        key={'index'}
        rowKey={'keys'}
        rowClassName={getRowClassName}
        dataSource={data}
        pagination={false}
      ></Table>
      <FeedBack
        changeBack={(e: boolean) => { setState({ ...state, back: e }); }}
        checkup_id={props.checkup_id}
        data={state}
        back={state.back}
        star={state.star}
      ></FeedBack>
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(Qualitative);
