/* eslint-disable prefer-destructuring */
import React, { useEffect } from 'react';
import * as echarts from 'echarts';
import { connect } from 'umi';
import styles from './index.less';
import { range } from '../../../service';
import { emptyOption, loadingOption } from '@/utils/handleCharts';

const option = (total: any, man: any) => {
  const xData = total;
  return {
    grid: { bottom: 20, left: 32 },
    legend: {
      data: ['本人', '同质人群'],
      top: 0,
      right: 0,
      icon: 'square',
    },
    xAxis: {
      data: xData.map((v: any) => {
        return v.checkup_time;
      }),
      boundaryGap: false,
      axisTick: { show: false },
      axisLine: { lineStyle: { color: 'rgba(192, 196, 204, 1)' } },
    },
    yAxis: {
      type: 'value',
      min: range('score', total, man)[0],
      max: 100,
      axisLine: { lineStyle: { color: 'rgba(192, 196, 204, 1)' } },
      splitLine: { lineStyle: { type: 'dashed' } },
    },
    tooltip: { show: true, trigger: 'axis' },
    series: [
      {
        name: '本人',
        type: 'line',
        data: total.map((v: any) => v.score),
        smooth: true,
        showSymbol: true,
        symbolSize: 8,
        itemStyle: { color: 'rgba(0, 102, 255, 1)' },
        emphasis: { focus: 'self' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 102, 255, 0.2)', },// 0% 处的颜色
              { offset: 1, color: 'rgba(255, 255, 255, 0.1)', },// 100% 处的颜色
            ],
            global: false, // 缺省为 false
          },
        },
      },
      {
        name: '同质人群',
        type: 'line',
        data: man && man.map((v: any) => v.score),
        smooth: true,
        showSymbol: true,
        symbolSize: 8,
        itemStyle: { color: 'rgba(192, 196, 204, 1)' },
        emphasis: { focus: 'self' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(192, 196, 204, 0.2)' },// 0% 处的颜色
              { offset: 1, color: 'rgba(255, 255, 255, 0.1)' },// 100% 处的颜色
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  };
};

interface lineState {
  year?: any[];
}
/**
 * @name 历年躯体健康指数趋势图
 */
const LinePicture: React.FC<lineState> = (props: any) => {
  useEffect(() => {
    const dom = document.getElementById('linePicture');
    if (dom == null) {
      return;
    }
    echarts.dispose(dom);
    const currentDom = echarts.init(dom);
    window.addEventListener('resize', () => {
      currentDom.resize();
    });

    currentDom.showLoading(loadingOption);
    if (props.year.hasOwnProperty('本人') && props.year.本人.length > 0) {
      setTimeout(() => {
        currentDom.hideLoading();
      }, 100);
      // eslint-disable-next-line no-inner-declarations
      const handleArray = (data: any, same: any) => {
        const score = same[0].score
        const newData = data.map(() => {
          return { score };
        });
        return newData;
      }
      // eslint-disable-next-line no-inner-declarations
      currentDom.setOption(
        option(props.year.本人, handleArray(props.year.本人, props.year.同人群)),
      );
    } else {
      currentDom.setOption(emptyOption);
    }
    setTimeout(() => {
      currentDom.hideLoading();
    }, 1000);
  }, [props.year]);
  return <div className={styles.line} id={'linePicture'}></div>;
};

export default connect((state: any) => {
  return state.personal;
})(LinePicture);
