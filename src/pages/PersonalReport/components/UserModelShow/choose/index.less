@import '~antd/es/style/themes/default.less';
.evenRow {
  background: rgba(255, 255, 255, 1);
}
.oddRow {
  background: rgba(235, 238, 245, 1);
}
.arrows {
  position: relative;
  top: 3px;
  left: 4px;
  display: inline-block;
  width: 16px;
  height: 16px;
  background-repeat: no-repeat;
  background-position: center;
}

.table {
  height: 410px;
  :global {
    .ant-table-body {
      overflow: auto;
      max-height: 387px;
      &::-webkit-scrollbar {
        width: 4px;
        height: 2px;
      }
      &::-webkit-scrollbar-thumb {
        background: rgba(129, 129, 128, 0.85);
        border-radius: 5px;
      }
      &::-webkit-scrollbar-track {
        background: rgba(0, 4, 40, 0.06);
        border-radius: 0;
      }
    }
    .ant-table-thead > tr > th {
      background-color: rgba(239, 241, 247, 1);
    }
    .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
      width: 0;
    }
    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td,
    .ant-table tfoot > tr > th,
    .ant-table tfoot > tr > td {
      padding: 6px;
    }
  }
}

.line {
  height: 410px;
}
