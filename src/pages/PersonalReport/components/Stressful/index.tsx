/* eslint-disable vars-on-top */
/* eslint-disable no-var */
import React, { useEffect } from 'react';
import styles from '../../index.less';
import * as echarts from 'echarts';
import { HealthYearsGET } from '../../service';
import { handleEveryScore } from '@/pages/PersonalReport/service';
import { connect } from '@/.umi/plugin-dva/exports';

const option = (scoreData: any) => {
  return {
    grid: { top: '20%', bottom: '10%', left: 40 },
    // dataZoom: [
    //   {
    //     type: 'inside',
    //     show: false,
    //     start: 50,
    //   },
    // ],
    legend: {
      data: ['个人分数'],
      top: '4%',
      right: '3%',
      icon: 'square',
    },
    xAxis: {
      nameLocation: 'center',
      data: scoreData.time,
      axisTick: { show: false },
      nameTextStyle: { fontSize: 12, padding: [0, 0, 0] },
      boundaryGap: false,
      axisLine: { lineStyle: { color: 'rgba(192, 196, 204, 1)' } },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 100,
      axisLine: { lineStyle: { color: 'rgba(192, 196, 204, 1)' } },
      splitLine: { lineStyle: { type: 'dashed' } },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'line' },// 坐标轴指示器，坐标轴触发有效  // 默认为直线，可选为：'line' | 'shadow'
    },
    series: [
      {
        name: '个人分数',
        type: 'line',
        data: scoreData.score,
        smooth: true,
        itemStyle: { color: 'rgba(0, 102, 255, 1)' },
        showSymbol: true,
        symbolSize: 8,
        areaStyle: {
          // 区域填充渐变颜色
          normal: {
            // 颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 102, 255, 0.2)' },
              { offset: 1, color: 'rgba(0, 102, 255, 0)' },
            ]),
          },
        },
      },
      // {
      //   name: '同年龄同性别',
      //   type: 'line',
      //   data:  scoreData.sum,
      //   smooth: true,
      //   itemStyle: {
      //     color: 'rgba(192, 196, 204, 1)',
      //   },
      //   // symbolSize: (value: any, param: any) => {
      //   //   // eslint-disable-next-line eqeqeq
      //   //   return param.dataIndex == emphasisIndex ? 13 : 5;
      //   // },
      //   areaStyle: {
      //     // 区域填充渐变颜色
      //     normal: {
      //       // 颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
      //       color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      //         {
      //           offset: 0,
      //           color: 'rgba(192, 196, 204, 0.3)',
      //         },
      //         {
      //           offset: 0.34,
      //           color: 'rgba(192, 196, 204, 0.2)',
      //         },
      //         {
      //           offset: 1,
      //           color: 'rgba(192, 196, 204, 0.3)',
      //         },
      //       ]),
      //     },
      //   },
      // },
    ],
  };
};

const ExaminationScore: React.FC<any> = (props: any) => {
  const emptyOption = {
    title: {
      text: '暂无数据',
      x: 'center',
      y: 'center',
      textStyle: { color: '#06f', fontWeight: 'normal', fontSize: 24 },
    },
  };
  const loadingOption = {
    text: 'loading',
    textColor: '#06f',
    color: ' #06f',
    maskColor: 'rgba(255, 255, 255, 0.8)',
    zlevel: 0,
  };
  const { dispatch } = props;
  // const [health, setHealth] = useState([]);
  const handleDispatch = (values: any) => {
    dispatch({ type: 'personal/handleData', payload: [...values] });
  };

  // 历年躯体健康指数趋势图
  const HealthHover = (condition: any) => {
    HealthYearsGET(condition).then((res: any) => {
      sessionStorage.setItem('HealthYears', JSON.stringify(res))
      handleDispatch(res);
      if (document.getElementById('stressful') == null) {
        return;
      }
      const Echarts: any = document.getElementById('stressful');
      echarts.dispose(Echarts);
      const myEcharts = echarts.init(Echarts);
      myEcharts.showLoading(loadingOption);
      if (res.length > 0) {
        setTimeout(() => {
          myEcharts.hideLoading();
        }, 300);
        window.addEventListener('resize', () => {
          setTimeout(() => {
            myEcharts.resize();
          }, 300);
        });
        const data: any = handleEveryScore(res);
        myEcharts.setOption(option(data));
      } else {
        setTimeout(() => {
          myEcharts.hideLoading();
        }, 100);
        myEcharts.setOption(emptyOption);
      }
    });
  };
  useEffect(() => {
    if (props.user_id === 'null') {
      return;
    }
    HealthHover({ user_id: props.user_id });
  }, [props.user_id]);
  return (
    <div className={styles.stressful} id={'stressful'}></div>
  );
};
export default connect((state: any) => {
  const { data, checkupId, score, user_id } = state.personal;
  return { data, checkupId, score, user_id };
})(ExaminationScore);
