import { useEffect } from 'react';
import * as echarts from 'echarts';
// import styles from "@/pages/GuideFill.less";
import Data from './Line.json';

const Line = () => {
  useEffect(() => {
    const Dom = document.getElementById('LineChart');
    if (Dom == null) {
      return;
    }
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    echarts.dispose(Dom);
    const currentDom = echarts.init(Dom);
    window.addEventListener('resize', () => {
      currentDom.resize();
    });
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    // eslint-disable-next-line global-require
    // const echarts = require("echarts")
    // // 基于准备好的dom，初始化echarts实例
    // const myChart = echarts.init(document.getElementById("LineChart"), 'macarons')
    let option;
    // eslint-disable-next-line prefer-const
    option = {
      grid: {
        width: '100%',
        height: '90%',
        top: '26px',
        bottom: '23px',
        left: '16px',
        containLabel: true,
      },
      tooltip: { trigger: 'axis', axisPointer: { type: 'cross' } },
      xAxis: {
        type: 'category',
        data: Data.xData,
        axisTick: { show: false },// 刻度线// 去掉刻度线
        axisLabel: {
          margin: 30,
          textStyle: {
            width: '17px',
            height: '20px',
            // fontFamily: { PingFangSC },
            fontWeight: 500,
            opacity: 0.8,
            color: '#C0C4CC', // 更改坐标轴文字颜色
            fontSize: 14, // 更改坐标轴文字大小
          },
        },
        axisLine: { lineStyle: { color: '#C0C4CC' } },// 更改坐标轴颜色
      },
      yAxis: {
        type: 'value',
        interval: 25,
        splitLine: {
          lineStyle: { type: 'dashed' },// 网格线 设置网格线类型 dotted：虚线   solid:实线
          show: true, // 隐藏或显示
        },
        axisLine: { lineStyle: { color: 'rgba(112, 112, 112, 0.4)' } },// 更改坐标轴颜色
        axisLabel: {
          formatter: '{value}',
          textStyle: {
            width: '17px',
            height: '20px',
            // fontFamily: { PingFangSC },
            fontWeight: 500,
            opacity: 0.8,
            color: '#C0C4CC', // 更改坐标轴文字颜色
            fontSize: 14, // 更改坐标轴文字大小
          },
        },
        axisPointer: { snap: true },
        min: 0,
        max: 100,
      },
      series: [
        {
          type: 'bar',
          symbolSize: 10,
          data: Data.seriesBar,
          barWidth: 25,
          itemStyle: { normal: { color: Data.seriesBarColor } },
        },
        {
          type: 'line',
          symbolSize: 10,
          data: Data.seriesLine,
          lineStyle: {
            normal: {
              color: 'rgba(0, 102, 255, 1)', // 折线的颜色
              width: '2', // 折线粗细
            },
          },
          itemStyle: {
            normal: {
              color: Data.seriesLineColor, // hover拐点颜色定义
              borderColor: '#FFFFFF', // 拐点边框颜色
              borderWidth: 4, // 拐点边框大小
            },
          },
        },
      ],
    };
    // eslint-disable-next-line no-unused-expressions
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    option && currentDom.setOption(option);
  });

  return (
    <div style={{ width: '100%' }}>
      <div id={'LineChart'} style={{ width: '100%', height: '15rem' }} />
    </div>
  );
};

export default Line;
