import React, { useEffect } from 'react';//  useState
import * as echarts from 'echarts';
import { InformationListGET } from '../../service';
import { connect } from 'umi';
// 仪表指针数据
const option = (pointerData: any, info: any, last: any) => {
  let name = info.split('')[0];
  if (info.split('').length <= 2) {
    name += '*'
  } else {
    name += '*'
    info.split('').map((item, ind) => {
      if (ind >= 2) {
        name += item;
      }
    })
  }
  info = name
  return {
    backgroundColor: '#fff',
    grid: { top: '50%' },
    title: [
      {
        text: `${pointerData}`,
        x: 'center',
        y: 'center',
        textStyle: { fontWeight: 500, fontSize: 50, color: 'rgba(0, 102, 255, 1)' },
      },
    ],
    series: [
      {
        type: 'gauge',
        radius: '110%',
        center: ['50%', '60%'],
        startAngle: 225,
        endAngle: -45,
        splitNumber: 60,
        splitLine: {
          show: true,
          length: 12,
          distance: -10,
          lineStyle: { color: '#fff', width: 5 },
        },
        detail: {
          show: true,
          offsetCenter: [0, 40],
          fontSize: 22,
          formatter: [`{a|${info}}`, `{b|${last}}`].join('\n'),
          rich: {
            a: { fontSize: 14, color: 'black' },
            b: { fontSize: 14, color: '#909399' },
          },
        },
        // 仪表盘的线，颜色值为一个数组
        axisLine: {
          show: true,
          // 两端是否设置为圆角；在5.0之后的版本有效
          roundCap: false,
          lineStyle: {
            width: 14,
            color: [[pointerData / 100, 'rgba(59, 137, 255, 1)'], [1, 'rgba(0,0,0,0.15)']],
          },
        },
        // 仪表盘刻度标签
        axisLabel: { show: false },
        // 刻度
        axisTick: { show: false },
        // 指针，此设置仅对5.0以上的版本生效
        pointer: { show: false },
        anchor: {
          show: false,
          icon: 'circle',
          showAbove: true,
          size: 20,
          itemStyle: { borderWidth: 6, borderColor: 'rgba(84, 108, 198, 0.85)' },
        },
        data: [pointerData],
      },
    ],
  };
};
/**
 * @Component 团体报告基本信息仪表盘
 * @returns
 */
const DashBoard: React.FC<any> = (props: any) => {
  const { select_year } = props
  // const [flag, setFlag] = useState(true);
  // const [information, setInformation] = useState<any>([]);
  const HealthYearsSession = JSON.parse(sessionStorage.getItem('HealthYears') as string)
  const InformationList = (condition: any) => {
    // InformationListGET(condition).then((res: any) => {
    const Dash_Board: any = document.getElementById('Instrument');
    // 调用showLoading方法
    if (Dash_Board === null) {
      return;
    }
    echarts.dispose(Dash_Board);
    const a = echarts.init(Dash_Board, { render: 'svg' });
    window.addEventListener('resize', () => {
      a.resize();
    });
    if (HealthYearsSession) {
      HealthYearsSession.forEach((item: any) => {
        if (select_year) {
          if (item.checkup_time === select_year) {
            a.setOption(option(item.score, props.initState[0].name, item.checkup_time));
          }
        } else {
          a.setOption(option(HealthYearsSession[HealthYearsSession.length - 1].score, props.initState[0].name, HealthYearsSession[HealthYearsSession.length - 1].checkup_time),);
        }
      })
    } else {
      a.setOption(option(props.initState[0] && props.initState[0].last_check_score, props.initState[0] && props.initState[0].name, props.initState[0] && props.initState[0].last_check_time,),);
    }
    // setTimeout(() => {
    //   // setOption前隐藏loading事件
    //   a.hideLoading();
    //   a.setOption(
    //     option(
    //       res[0] && res[0].last_check_score,
    //       res[0] && res[0].name,
    //       res[0] && res[0].last_check_time,
    //     ),
    //   );
    // }, 10);
    // });
  };
  useEffect(() => {
    if (props.initState != null) {
      InformationList({ user_id: props.user_id });
    }
  }, [props.initState, HealthYearsSession])
  // const Data = 65;
  useEffect(() => {

  }, [props.initState])
  useEffect(() => {
    if (props.user_id === 'null') {
      return;
    }
    if (props.initState != null) {
      InformationList({ user_id: props.user_id });
    }
  }, [props.user_id, select_year]);

  return <div style={{ height: '14rem' }} id="Instrument"></div>;
};

export default connect((state: any) => {
  return state.personal;
})(DashBoard);
