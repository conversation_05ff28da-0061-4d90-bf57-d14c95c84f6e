import styles from './index.less';
import { Tooltip } from 'antd';
/**
 * @Component 分数条组件
 * @param props
 * @returns
 */
const SliderBar = (props: any) => {
  const { score, okl } = props;

  return (
    <div className={styles.gutter} style={{ minWidth: 200 }}>
      <img src={props.img} alt={props.title} className={styles.imgSize} />
      <div className={styles.right}>
        <div className={styles.title}>
          <span>{props.title}平均分:</span>
          <span className={styles.span}>{score.avg}</span>
        </div>
        <div className={styles.slider} style={{ minWidth: 200 }}>
          <img src={props.mark} alt={'平均分'} style={{ position: 'relative', left: `${okl}%` }} />
          <div className={styles.sliderBar}>
            <Tooltip title={`min:${score.min}`}>
              <div className={styles.circle} style={{ left: `${score.min}%`, backgroundColor: 'rgba(152, 211, 76, 1)' }}></div>
            </Tooltip>
            <Tooltip title={`avg:${score.avg}`}>
              <div className={styles.circle} style={{ left: `${score.avg}%`, backgroundColor: 'rgba(0, 102, 255, 1)' }}>
                <span className={styles.text}>平均分</span>
              </div>
            </Tooltip>
            <Tooltip title={`max:${score.max}`}>
              <div className={styles.circle} style={{ left: `${score.max}%`, backgroundColor: 'rgba(255, 0, 0, 1)' }}></div>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};
export default SliderBar;
