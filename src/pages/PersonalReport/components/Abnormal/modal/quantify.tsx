import { Popover, Rate, Table } from 'antd';
import styles from './qual.less';
import lt from '@/../public/personalreport/lt.svg';
import gt from '@/../public/personalreport/gt.svg';
import { useState, useEffect } from 'react';
import { handleKeys, compare } from '@/pages/PersonalReport/service';
// import exc from '@/assets/HealthList/exc.svg';
import Edit from '@/../public/personalreport/edit.svg';
import Content from '@/components/Grading/content';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Flash } from '@/components/MenuIcon';
import { handleColor } from '@/services/person_report';
import FeedBack from '@/components/FeedBack';

const getRowClassName = (_: any, index: number) => {
  let className = '';
  className = index % 2 === 0 ? styles.evenRow : styles.oddRow;
  return className;
};

function ResultComponent(v: any, flag: any, color: string, php?: any) {
  if (flag === 'gt' || flag === 'lt') {
    return (<div key={v.key}>
      <span style={{ color }}>{v.checkup_res}
        <img src={php} style={{ marginLeft: '8px' }} />
      </span>
    </div>);
  }
  return (<div key={v.key}>
    <span style={{ color }}>{v.checkup_res}</span>
  </div>);
}

type Omit<T, K> = Pick<T, Exclude<keyof T, K>>;
type stateType = Omit<API.FeedState, 'changeBack'>;

const Quantify = (props: any) => {
  const [state, setState] = useState({ data: [], loading: true });
  const [feed, setFeed] = useState<stateType>({
    back: false,
    keyword: '',
    index_name: '',
    item_name: '',
    cancel: false,
    star: 0,
    flag: true,
    marks: 0,
    item_coding: '',
    index_coding: '',
  });
  // 点击回调值给反馈页面
  function callback(
    star: number,
    keyword: string,
    item_name?: string,
    index_name?: string,
    marks?: any,
    item_coding?: string,
    index_coding?: string,
  ) {
    setFeed({
      ...feed,
      back: true,
      keyword,
      star,
      item_name,
      index_name,
      marks,
      item_coding,
      index_coding,
    });
  }

  const columns: any = [
    {
      title: '检查指标', dataIndex: 'item_name', key: 'item_name', align: 'left', width: 180,
      render: (value: any, v: any) => {
        return (<div key={v.key}>
          {value}
          {v.index_name}
        </div>);
      },
    },
    { title: '正常值范围', className: styles.scopeStyle, dataIndex: 'reference_value', align: 'left', width: 160, },
    {
      title: '结果', dataIndex: 'checkup_res', key: 'checkup_res', align: 'left', width: 120,
      render: (_: any, v: any) => {
        switch (v.checkup_res_contrast) {
          case 'normal': { return ResultComponent(v, 'normal', '#909399'); }
          case 'gt': { return ResultComponent(v, 'gt', '#FF574B', gt); }
          case 'lt': { return ResultComponent(v, 'lt', '#1677FF', lt); }
          default: { return ResultComponent(v, 'default', '#ff7a45'); }
        }
      },
    },
    {
      title: () => {
        return (
          <div>
            <span>关心程度</span>
            <Popover content={Content}>
              <InfoCircleOutlined className={styles.icon} style={{ position: 'relative', left: 4, top: 1 }} />
            </Popover>
          </div>
        );
      },
      dataIndex: '关爱程度', align: 'left', width: 120,
      render: (level: any) => {
        return (<Rate
          character={<Flash />}
          style={{ color: handleColor(level) }}
          disabled={true}
          value={Number(level)}
          allowHalf={true}
        ></Rate>);
      },
    },
    {
      title: '反馈', dataIndex: 'score', width: 80,
      render: (score: any, it: any) => {
        return (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => {
              callback(
                Number(it.关爱程度),
                `${it.checkup_res} ${it.reference_value}`,
                it.item_name,
                it.index_name,
                score,
                it.item_coding,
                it.index_coding,
              );
            }}
          ><img src={Edit} alt="反馈" /></div>
        );
      },
    },
  ];

  useEffect(() => {
    // 判断有数据则通过关爱程度排序赋值列表
    if (props.quantify.length > 0) {
      const timer = setTimeout(() => {
        setState({ data: props.quantify.sort(compare('关爱程度')), loading: false });
        clearTimeout(timer)
      }, 300)
    } else if (props.quantify.length == 0) {
      const timer = setTimeout(() => {
        setState({ data: [], loading: false });
        clearTimeout(timer)
      }, 300)
    }
  }, [props.quantify]);

  return (
    <div className={styles.table}>
      <Table
        loading={state.loading}
        columns={columns}
        key={'index'}
        rowKey={'keys'}
        rowClassName={getRowClassName}
        dataSource={handleKeys(state.data)}
        pagination={false}
      ></Table>
      <FeedBack
        data={feed}
        star={feed.star}
        back={feed.back}
        checkup_id={props.checkup_id}
        changeBack={(e: boolean) => {
          setFeed({ ...feed, back: e });
        }}
      ></FeedBack>
    </div>
  );
};
export default Quantify;
