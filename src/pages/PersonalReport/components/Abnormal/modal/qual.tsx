import React, { useState, useEffect } from 'react';
import styles from './qual.less';
import type { TableColumnsType } from 'antd';
import { Rate, Popover, Table } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Flash } from '@/components/MenuIcon';
import Content from '@/components/Grading/content';
import Edit from '@/../public/personalreport/edit.svg';
import { handleKeys, compare } from '@/pages/PersonalReport/service';
import FeedBack from '@/components/FeedBack';

function handleColor(star: number) {
  if (star > 3) {
    return '#F00F00';
  }
  if (star > 2) {
    return '#FFB52B';
  }
  return '#06f';
}

/** @name 报告类总详情页 */
const KeyWord: React.FC<any> = (props: any) => {
  const columns: TableColumnsType<any> | undefined = [
    { title: '项目名称', dataIndex: '项目名称' },
    { title: '异常关键词', dataIndex: '关键词' },
    {
      title: () => {
        return (<div>
          <span>关心程度</span>
          <Popover content={Content}>
            <InfoCircleOutlined className={styles.icon} style={{ position: 'relative', left: 4, top: 1 }} />
          </Popover>
        </div>);
      },
      dataIndex: '关爱程度', width: 140,
      render: (star: any) => {
        return (<Rate
          character={<Flash />}
          style={{ color: handleColor(star) }}
          disabled={true}
          value={Number(star)}
          allowHalf={true}
        ></Rate>);
      },
    },
    {
      title: '反馈', width: 60,
      render: (_: any, v: any) => {
        return (
          <div
            onClick={() => { callback(Number(v.关爱程度), v.关键词, v.项目编码, v.项目名称, v.指标编码, v.指标名称); }}
            className={styles.call}>
            <img src={Edit} alt="编辑" />
          </div>
        );
      },
    },
  ];

  const [list, setList] = useState<any>(props.list);
  const [loading, set] = useState(true);
  const [state, setState] = useState<API.feedData>({
    back: false,
    keyword: '',
    index_coding: '',
    index_name: '',
    item_coding: '',
    item_name: '',
    cancel: false,
    star: 0,
    flag: true,
  });

  function callback(
    star: number,
    keyword: string,
    item_coding?: string,
    item_name?: string,
    index_coding?: string,
    index_name?: string,
  ) {
    setState({
      ...state,
      back: true,
      keyword,
      star,
      index_coding,
      index_name,
      item_coding,
      item_name,
    });
  }

  useEffect(() => {
    setTimeout(() => {
      set(false);
      setList(props.list.sort(compare('关爱程度')));
    }, 500);
  }, [props.list]);

  const getRowClassName = (_: any, index: number) => {
    let className = '';
    className = index % 2 === 0 ? styles.evenRow : styles.oddRow;
    return className;
  };

  return (
    <div className={styles.table}>
      <Table
        loading={loading}
        columns={columns}
        key={'index'}
        rowKey={'keys'}
        rowClassName={getRowClassName}
        dataSource={handleKeys(list)}
        pagination={false}
      />
      <FeedBack
        star={state.star}
        back={state.back}
        data={state}
        checkup_id={props.checkup_id}
        changeBack={(e: boolean) => { setState({ ...state, back: e }); }}
      ></FeedBack>
    </div>
  );
};
export default KeyWord;
