.Ul {
  width: 100%;
  height: 100px;
}
.<PERSON><PERSON> {
  font-size: 18px;
}
.anomaly {
  display: flex;
  flex-direction: row;
}
.Select {
  padding-left: 400px;
  :global {
    .ant-form-item-control-input {
      position: relative;
      display: -ms-flexbox;
      display: flex;
      align-items: center;
      width: 180px;
      min-height: 2rem;
      -ms-flex-align: center;
    }
  }
}
.Div {
  width: 100%;
  height: 100px;
  padding: 16px 16px 16px 16px;
  font-size: 16px;
}


.check{
  border-bottom: 1px solid #E0E9EE;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 378px;
  height: 50px;
  border-bottom: 1px solid rgba(192, 196, 204, 0.2);
}
.IMG {
  padding-right: 16px;
  flex: 1;
}
.text{
  flex: 8;
}
.enter{
  flex:2;
}
.gutter{
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 378px;
  height: 50px;
}
.YIJ {
  padding-right: 4px;
  color: #909399;
}
.Span {
  margin-left: 2px;
  padding-right: 3px;
  color: red;
}
.Cla {
  padding-right: 4px;
  // color: #909399;
  color: #0068FF;
  cursor: pointer;
}
.SPAN {
  padding-left: 10px;
  color: #0066ff;
  cursor: pointer;
  :global {
    .anticon svg {
      width: 1rem;
      height: 1rem;
    }
  }
}
.Look {
  color: #606266;
  cursor: pointer;
}
.modalTitle {
  display: flex;
  justify-content: space-between;
  width: 100%;
  :global {
    .ant-cascader-picker {
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .modalLeft {
    flex: 5;
    height: 100%;
    line-height: 30px;
    font-size: 18px;
  }
  .modalRight {
    flex: 3;
    label {
      margin-right: 8px;
    }
  }
}
