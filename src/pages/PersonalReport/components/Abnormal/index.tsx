import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Modal } from 'antd';
import { DoubleRightOutlined } from '@ant-design/icons';
import Light from '@/../public/personalreport/dengpao.svg';
import KeyWord from './modal/qual';
import { getDataPOST } from '../../service';
import Url from '../../url';
import { connect } from '@/.umi/plugin-dva/exports';
import Quantify from './modal/quantify';

const AbNormalProject: React.FC<any> = (props: any) => {
  const [Title, setTitle] = useState<any>('');
  const [data, setData] = useState<any>({});
  const [list, getList] = useState<any>({
    quantify: [],
    qual: [],
  });
  const [state, setState] = useState<any>({ isModalVisible: false, flag: true });

  const { checkup_id } = props;
  const showModal = (params: any, title_name: any) => {
    setTitle(title_name);
    if (params === '定性') {
      setState({ flag: true, isModalVisible: true });
      getDataPOST(Url.KEY_WORD, { checkup_id, abnormal_only: 'True' }).then((res) => {
        if (res.url !== undefined) {
          getList({ quantify: [], qual: [] });
          return;
        }
        getList({ ...list, qual: res });
      }).catch(() => {
        getList({ quantify: [], qual: [] });
      });
    } else {
      setState({ flag: false, isModalVisible: true });
      getDataPOST(Url.AbNormal_Quantify, { checkup_id, abnormal_only: 'True' }).then((res) => {
        if (res.url !== undefined) {
          getList({ quantify: [], qual: [] });
          return;
        }
        getList({ ...list, quantify: res });
      }).catch(() => {
        getList({ quantify: [], qual: [] });
      });
    }
  };

  const handleOk = () => {
    setState({ flag: true, isModalVisible: false });
    getList({ qual: [], quantify: [] });
  };

  const handleCancel = () => {
    setState({ flag: true, isModalVisible: false });
    getList({ qual: [], quantify: [] });
  };
  useEffect(() => {
    if (props.checkup_id === 'null') {
      return;
    }
    getDataPOST(Url.AbNORMAL_COUNT, { checkup_id: props.checkup_id }).then((res) => { setData({ ...res }); });
  }, [props.checkup_id]);
  return (
    <div title={'异常关键字'} className={styles.checkOutDisease}>
      <div title={'异常关键字'}> </div>
      <div className={styles.Div}>
        <div className={styles.Ul}>
          <div className={styles.check}>
            <img src={Light} alt={'报告'} className={styles.IMG} />
            <div className={styles.text}>
              <span style={{ marginRight: 8 }}> 实验室</span>
              <span className={styles.YIJ}>检查</span>
              <span className={styles.Cla}>{data.quantitative_data_sum}</span>
              <span style={{ color: '#909399' }}>项,</span>
              <span className={styles.YIJ}>异常</span>
              <span className={styles.Span}>{data.quantitative_data_count}</span>
              <span style={{ color: '#909399' }}>项</span>
            </div>
            <div className={styles.enter} onClick={() => showModal('定量', '实验室')}>
              <span className={styles.Look}>查看
                <span className={styles.SPAN}><DoubleRightOutlined /></span>
              </span>
            </div>
          </div>
          <div className={styles.gutter}>
            <img src={Light} alt={'报告'} className={styles.IMG} />
            <div className={styles.text}>
              <span style={{ marginRight: 8 }}> 报告类</span>
              <span className={styles.YIJ}>检查</span>
              <span className={styles.Cla}>{data.qualitative_data_sum}</span>
              <span style={{ color: '#909399' }}>项,</span>
              <span className={styles.YIJ}>异常</span>
              <span className={styles.Span}>{data.qualitative_data_count}</span>
              <span style={{ color: '#909399' }}>项</span>
            </div>
            <div className={styles.enter} onClick={() => showModal('定性', '报告类')}>
              <span className={styles.Look}>查看
                <span className={styles.SPAN}><DoubleRightOutlined /></span>
              </span>
            </div>
          </div>
        </div>
      </div>
      <Modal
        className={styles.Modal}
        title={<div className={styles.modalTitle}><div className={styles.modalLeft}>{Title}项目结果</div></div>}
        centered
        okButtonProps={{ disabled: true }}
        cancelButtonProps={{ disabled: true }}
        width={800}
        footer={null}
        maskStyle={{ opacity: 0.3 }}
        visible={state.isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        {state.flag ? (<KeyWord list={list.qual} checkup_id={props.checkup_id}></KeyWord>) : (
          <Quantify quantify={list.quantify} checkup_id={props.checkup_id}></Quantify>
        )}
      </Modal>
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(AbNormalProject);
