import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import { CoverGet } from '../../service';
import { Popover } from 'antd';
import exclamatory from '../../../../../public/exclamatory.svg';

const Cover: React.FC<any> = (props: any) => {
  const content = (
    <div>
      <p>可测部位占比越高，说明本次体检得分对体检者躯体健康状态量化评估的准确性越高。可测部位</p>
      <p>占比：以市面上所有体检项目覆盖的部位及其异常标识的总计数为分母，本次体检项目覆盖的部</p>
      <p style={{ marginBottom: '0' }}> 及其异常标识计数为分子，计算得到。</p>
    </div>
  );
  const [state, setState] = useState<any>('');
  useEffect(() => {
    if (props.checkup_id === 'null') {
      return;
    }
    CoverGet({ checkup_id: props.checkup_id }).then((res) => {
      setState(res[0].checkup_coverage);
    }).catch((err) => {
      console.log(err);
    });
    // return () => {
    // }
  }, [props.checkup_id]);
  return (
    <div style={{ textAlign: 'center' }}>
      <span>可测部位占比:</span>
      <span style={{ color: 'red', paddingLeft: '4px' }}>{state}</span>
      <span style={{ paddingLeft: '4px' }}>
        <Popover placement="topRight" content={content}><img src={exclamatory} alt="提示" /></Popover>
      </span>
    </div>
  );
};
export default connect((state: any) => {
  return state.personal;
})(Cover);
