/* eslint-disable no-param-reassign */
/* eslint-disable react/no-array-index-key */
import { Badge } from 'antd';
import Url from '../../url';
import styles from './index.less';
import { Empty, Spin, Tabs } from 'antd';
import { useState, useEffect } from 'react';
import UserModelShow from '../UserModelShow/index';
import { getDataGET, recursion } from '../../service';
import { connect } from '@/.umi/plugin-dva/exports';
import Lightbulb from '@/../public/personalreport/Light_bulb.png';

const { TabPane } = Tabs;
const ScoreDetail = (props: any) => {
  const [tabData, setTab] = useState<any>([]);
  const [loading, setLoad] = useState<any>(true);
  /** @function 点击消除红点 */
  const handleChange = (index: number, tab: any, item: any) => {
    const item_name = item.split(' ')[0];
    const e = tabData[index];
    if (e.name === tab.name) {
      e.children = recursion(tabData, item_name, false);
      const newTab: any[] = [];
      tabData.map((v: any, _: number, array: any) => {
        if (array[index].name === v.name) {
          const resultArr = e.children.filter((a: any) => a.score_flag === true);
          if (resultArr.length < 1) {
            v = { ...e, children: e.children, score_flag: false };
          } else {
            v = { ...e, children: e.children };
          }
        }
        newTab.push(v);
        return null;
      });
      setTab(newTab);
    }
  };
  /** @function 增大消除红点点击面积 */
  function handleTabs(order: any, e: any, tab: any) {
    let currentText: any;
    const value: any = e.target.innerText;
    const text = Number(value);
    if (Number.isNaN(text)) {
      if (Number.isNaN(Number(value.split(' ')))) {
        currentText = value;
      } else {
        [currentText] = value.split(' ');
      }
    } else {
      const older = e.target.parentNode.innerText;
      [currentText] = older.split(' ');
    }
    const index = order.slice(0, 1);
    handleChange(index, tab, currentText);
  }

  useEffect(() => {
    if (props.checkup_id === 'null' || props.user_id === 'null') {
      return;
    }
    getDataGET(Url.scoreDetail, { checkup_id: props.checkup_id, }).then((res) => {
      setTab(res);
    }).catch((err) => {
      // eslint-disable-next-line no-console
      console.error(`得分详情${err}`);
    }).finally(() => {
      // setTimeout(() => {
      setLoad(false);
      // }, 50000);
    });
    // eslint-disable-next-line consistent-return
    return (): void => {
      setLoad(true);
    };
  }, [props.checkup_id, props.user_id]);
  if (loading) {
    return (<div className={styles.loadingContainer}>
      <Spin size={'large'} style={{ flex: 1 }}></Spin>
    </div>);
  }
  if (tabData.length && tabData.length > 0) {
    return (
      <div className={styles.scoreTab}>
        <Tabs defaultActiveKey="0" type={'card'}>
          {tabData.map((tab: any, index: number) => {
            return (
              <TabPane
                tab={<div><Badge count={tab.score_flag === true ? 1 : 0} offset={[3, -2]} dot><span>{`${tab.name}功能分析`} </span></Badge></div>}
                key={index}
                style={{ marginTop: 0, height: '100%' }}
                className={styles.innerTab}>
                <Tabs
                  defaultActiveKey={`${index}-0`}
                  onTabClick={(i, e) => handleTabs(i, e, tab)}
                  type={'card'}>
                  {tab.children.map((t: any, i: number) => {
                    return (
                      <TabPane
                        tab={<Badge count={t.score_flag === true ? 1 : 0} offset={[2, -2]} dot>
                          <span>{`${t.name} `}</span>
                          <span>{t.value}</span>
                        </Badge>}
                        key={`${index}-${i}`}
                        style={{ marginTop: 0 }}>
                        <div className={styles.suggestion}>
                          <img src={Lightbulb} alt="图片" />
                          <span className={styles.span}>提示:</span>
                          <span className={styles.span}>
                            {tab.name} : {tab.l0_name_desc} ; {t.name} : {t.l1_name_desc}
                          </span>
                        </div>
                        <UserModelShow name1={tab.name} name2={t.name} />
                      </TabPane>
                    );
                  })}
                </Tabs>
              </TabPane>
            );
          })}
        </Tabs>
      </div>
    );
  }
  return (
    <div className={styles.scoreTab}>
      <Empty style={{ flex: 1 }}></Empty>
    </div>
  );
};
export default connect((state: any) => {
  return state.personal;
})(ScoreDetail);
