@import '~antd/es/style/themes/default.less';

.loadingContainer {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
}
.scoreTab {
  .spann{
    color: red;
  }
  :global {
    .ant-tabs > .ant-tabs-nav .ant-tabs-nav-wrap,
    .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-wrap {
      display: block;
      width: 100%;
    }
    .ant-badge {
      color: inherit;
    }
    .ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab {
      flex: 1;
      justify-content: center;
      height: 47px;
      margin-right: 0;
      margin-left: 0;
      padding: 0;
      color: #909399;
      background: #fafafa;
      border: none;
      opacity: 1;
      padding: 0 10px;
    }
    .ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active {
      color: #303133;
      font-weight: 500;
      font-size: 14px;
      background: white;
    }
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #303133;
      text-shadow: 0 0 0.25px #303133;
    }
    .ant-tabs-top > .ant-tabs-nav::before,
    .ant-tabs-bottom > .ant-tabs-nav::before,
    .ant-tabs-top > div > .ant-tabs-nav::before,
    .ant-tabs-bottom > div > .ant-tabs-nav::before {
      border-bottom: none;
    }
    .ant-tabs-card > .ant-tabs-nav .ant-tabs-ink-bar,
    .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-ink-bar {
      width: 0 !important;
    }
    .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab,
    .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab {
      border: none;
    }
  }
}
.suggestion{
  position: relative;
  top: -4px;
}
.span{
  position: relative;
  top: 2px;
  color: #f60f60;

}
.loading {
  position: absolute;
  top: 20rem;
  left: 30rem;
  // margin: 4rem 0 0 4rem;
  text-align: center;
}
.innerTab {
  padding-right: 24px;
  padding-left: 24px;
  :global {
    .ant-tabs > .ant-tabs-nav .ant-tabs-nav-wrap,
    .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-wrap {
      display: flex;
    }

    .ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab {
      justify-content: center;
      height: 30px;
      margin-right: 20px;
      padding-right: 16px;
      // width: 74px;
      padding-left: 16px;
      padding-left: 10px !important;
      color: #c0c4cc;
      background: white;
      border: 1px solid rgba(211, 211, 211, 0.4);
      border-radius: 24px;
      opacity: 1;
      
    }
    .ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active {
      color: rgba(56, 135, 255, 1) !important;
      font-weight: 500;
      font-size: 14px;
      background: #f2f4f8 !important;
      border: 1px solid transparent;
    }
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: rgba(56, 135, 255, 1);
      text-shadow: 0 0 0.25px rgba(56, 135, 255, 1);
    }
    .ant-tabs-top > .ant-tabs-nav::before,
    .ant-tabs-bottom > .ant-tabs-nav::before,
    .ant-tabs-top > div > .ant-tabs-nav::before,
    .ant-tabs-bottom > div > .ant-tabs-nav::before {
      border-bottom: none;
    }
  }
}
