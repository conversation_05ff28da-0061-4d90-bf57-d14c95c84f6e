import styles from '../../index.less';
// 折现面积图
import Stressful from '../Stressful/index';
import { HealthYearsGET } from '../../service';
import exclamatory from '../../../../../public/exclamatory.svg';
import { useEffect, useState } from 'react';
import { Popover } from 'antd';

const Calendar: React.FC<any> = () => {
  const content = (
    <div>
      <p style={{ marginBottom: '0' }}>点击历史数据点，可切换到指定体检时间的报告</p>
      <p>同质人群得分：基于近50万人次体检数据，按＜30岁、30-39岁、40-49岁、50-59岁、</p>
      <p style={{ marginBottom: '0' }}>≥60岁年龄段区间,得出的同年龄同性别人群的躯体健康得分平均分</p>
    </div>
  );
  const [health, setHealth] = useState([]);
  // 历年躯体健康指数趋势图
  const Healthover = (condition: any) => {
    HealthYearsGET(condition).then((res: any) => { setHealth(res); });
  };

  useEffect(() => {
    Healthover({ user_id: 19913 });
  }, []);
  return (
    <div className={styles.CalendarMin}>
      <div className={styles.CalendarTop}>
        历年躯体健康得分趋势图
        <Popover placement="bottomRight" content={content}><img src={exclamatory} alt="提示" /></Popover>
      </div>
      <div className={styles.CalendarCharts}>{health ? <Stressful health={health}></Stressful> : null}</div>
    </div>
  );
};
export default Calendar;
