import React, { useEffect, useState } from 'react';
import styles from './index.less';
import personMark from '@/../public/team_report/personMark.svg';
import businessMark from '@/../public/team_report/businessMark.svg';
import company from '@/../public/personalreport/company.svg';
import allPerson from '@/../public/personalreport/allPerson.svg';
import { AverageGET } from '../../service';
import { connect } from 'umi';
import GeneralModule from '@/components/GeneralPurposeModule';
import GlobalCard from '@/components/GlobalCard';

const IndustryHealth: React.FC<any> = (props: any) => {
  const [average, setAverage] = useState<any>({ flag: false, data: [] });
  const [loading, set] = useState(true);
  useEffect(() => {
    if (props.initState !== undefined && props.initState.length > 0 && props.checkup_id !== 'null') {
      AverageGET({
        checkup_id: props.checkup_id,
        last_check_score: props.initState[0].last_check_score,
      }).then((res: any) => {
        if (res !== undefined && res !== []) {
          setTimeout(() => { set(false); }, 300);
          setAverage({ flag: true, data: res });
        } else {
          setTimeout(() => { set(false); }, 300);
          setAverage({ flag: false, data: [] });
        }
      });
    }
  }, [props.initState, props.checkup_id]);

  return (
    <div className={styles.content}>
      <div className={styles.grid}>
        <div className={styles.container}>
          <GeneralModule loading={loading} flag={average.flag}>
            <GlobalCard title={'同团体人群对比'} img={company} mark={businessMark} background={'#589AFF'} data={average.data.company}></GlobalCard>
          </GeneralModule>
        </div>
      </div>
      <div className={styles.container}>
        <GeneralModule loading={loading} flag={average.flag}>
          <GlobalCard title={' 同年龄段同性别对比'} img={allPerson} mark={personMark} background={'#98D34C'} data={average.data.sex_age}></GlobalCard>
        </GeneralModule>
      </div>
    </div>
  );
};
export default connect((state: any) => {
  return state.personal;
})(IndustryHealth);
