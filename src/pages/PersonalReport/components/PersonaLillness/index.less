@import '~antd/es/style/themes/default.less';
.emptyCard {
  margin-top: 40px;
}
.img {
  margin-right: 16px;
}

.spans {
  min-width: 250px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.div:last-child {
  border-bottom: none;
}
.div {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  min-width: 250px;
  min-height: 50px;
  color: #606266;
  font-size: 16px;
  border-bottom: 1px solid rgba(192, 196, 204, 0.2);
}

.checkOutDisease {
  width: 100%;
  height: 300px;
  padding: 10px 16px 16px 10px;
  overflow:auto;
  scrollbar-color: red;
  scrollbar-width: thin;
  scrollbar-width: 2px;
  ::-webkit-scrollbar {
    width: 5px;
    height: 80px;
    background: #d9dee8;
    border-radius: 4px;
    opacity: 1;
  }
  ::-webkit-scrollbar-track {
    background: #d9dee8;
    border-radius: 4px;
  }
  ::-webkit-scrollbar-thumb {
    background: #bfbfbf;
    border-radius: 10px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: rgb(151, 160, 149);
  }
  ::-webkit-scrollbar-corner {
    background: #179a16;
  }

  ::-webkit-scrollbar:horizontal {
    display: none;
  }
}

.checkOutDiseaseTop {
  position: sticky;
  top: 0;
  background-color: white;
}
