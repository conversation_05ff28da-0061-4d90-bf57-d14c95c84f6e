import React, { useState, useEffect } from 'react';
import xinJi from '@/../public/personalreport/xinji.svg';
import { Tooltip } from 'antd';
import styles from './index.less';
import { connect } from 'umi';
import GeneralModule from '@/components/GeneralPurposeModule';

/** @name 检出疾病 */
const PersonaIllness: React.FC<any> = (props: any) => {
  const [detect, setDetect] = useState<any>({ flag: false, data: [], loading: true });
  /** @function 组件初始化状态和值 */
  function detectChange(flag: boolean, data: any[]) {
    setDetect({ flag, data, loading: false });
  }
  useEffect(() => {
    if (props.detect.length === 0) {
      detectChange(false, []);
      return;
    }
    detectChange(true, props.detect);
  }, [props, props.detect]);

  return (
    <div className={styles.checkOutDisease}>
      <GeneralModule loading={detect.loading} flag={detect.flag}>
        {detect.data.map((item: any, index: number) => {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <div className={styles.checkOutDiseaseContainer} key={index}>
              {item.checkup_conclusion.map((tab: any, i: number) => {
                return (
                  <div key={i} className={styles.div}>
                    <span className={styles.spans}>
                      <img className={styles.img} src={xinJi} alt="illness" />
                      <Tooltip placement="topLeft" title={tab} arrowPointAtCenter>
                        <span>{tab}</span>
                      </Tooltip>
                    </span>
                  </div>
                );
              })}
            </div>
          );
        })}
      </GeneralModule>
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(PersonaIllness);
