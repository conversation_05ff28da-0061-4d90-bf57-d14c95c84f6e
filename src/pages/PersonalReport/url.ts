/**
 * @TeamDetailPagesURL
 */
const Url: url.person_report = {
  /**
   * @体检详情_左上_用户基本信息
   */
  home: '/health/health/user_basic_info/',
  /**
   * @体检详情_右上_历年体检分数
   */
  everyYear: '/health/health/user_year_score_chart/',
  /**
   * @体检详情_平均分卡片通用接口
   */
  avgScore: '/health/health/avg_score_common/',
  /**
   * @体检详情_右中_雷达图
   * @params
   */
  radar: '/health/check/user_score_radar_chart/',
  /**
   * @得分详情_左中_红点标记
   */
  scoreDetail: '/health/health/tree_l1_l2_badge_flag/',
  /**
   * @体检详情_左下
   */
  scoreLeftBottom: '/health/health/tree_l3_score_list/',
  /**
   * @体检详情_左下_XX历年得分数据
   */
  scoreData: '/health/health/tree_l5_score_year_chart/',
  /**
   * @体检详情_左下_xx得分详情
   */
  perScoreDetail: '/health/health/tree_l5_score_list/',
  /**
   * @体检详情_左下_XX异常检查项目指标
   */
  abNormal: '/health/health/tree_score_checkup_id_l0_l1_l3_dialog2/',
  /**
   * @体检详情_左下_XX异常检查项目指标_定量
   */
  QUANTIFY: '/health/health/tree_l3_quantitative_data/',
  /**
   * @体检详情_左下_XX异常检查项目指标_定性
   */
  QUALITATIVE: '/health/health/tree_l3_qualitative_data/',
  /**
   * @体检详情_检出疾病
   */
  CHECKUP_ILLNESS: '/health/check/user_check_illness/',
  /**
   * TODO: 定量
   */
  AbNormal_Quantify: '/health/health/tree_l5_score_quantitative_detail/',
  /**
   *TODO: 异常项
   */
  AbNORMAL_COUNT: '/health/health/tree_l3_abnormal_count/',
  /**
   *TODO: 定性
   */
  KEY_WORD: '/health/health/tree_l5_score_qualitative_detail/',


};

export default Url;
