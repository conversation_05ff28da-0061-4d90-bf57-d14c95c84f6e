export const List = [
  {
    检查项目: 'X线计算机体层(CT)平扫(64排及以上)-胸部',
    检查指标: 'X线计算机体层(CT)平扫(64排及以上)-胸部',
    关键词: '右房增大',
    扣分: '3',
  },

  {
    检查项目: 'X线计算机体层(CT)平扫(64排及以上)-胸部',
    检查指标: 'X线计算机体层(CT)平扫(64排及以上)-胸部',
    关键词: '右心房区低密度影',
    扣分: '3',
  },
  {
    检查项目: 'X线计算机体层(CT)平扫(64排及以上)-胸部',
    检查指标: 'X线计算机体层(CT)平扫(64排及以上)-胸部',
    关键词: '双侧心房影略增大',
    扣分: '3',
  },

  {
    检查项目: 'X线计算机体层(CT)平扫(含成像)-胸部',
    检查指标: 'X线计算机体层(CT)平扫(含成像)-胸部',
    关键词: '右房体积增大',
    扣分: '2',
  },
  {
    检查项目: 'X线计算机体层(CT)平扫(含成像)-胸部',
    检查指标: 'X线计算机体层(CT)平扫(含成像)-胸部',
    关键词: '左房、右房体积增大',
    扣分: '2',
  },
  {
    检查项目: '心脏彩色多普勒超声',
    检查指标: '拟诊',
    关键词: '双房增大',
    扣分: '4',
  },

  {
    检查项目: '心脏彩色多普勒超声',
    检查指标: '拟诊',
    关键词: '右房室扩大',
    扣分: '2',
  },

  {
    检查项目: '心脏彩色多普勒超声',
    检查指标: '拟诊',
    关键词: '双房增大',
    扣分: '4',
  },
  {
    检查项目: '心脏彩色多普勒超声',
    检查指标: '拟诊',
    关键词: '左右心房扩大',
    扣分: '4',
  },
  {
    检查项目: 'X线计算机体层(CT)平扫(64排及以上)-胸部',
    检查指标: '拟诊',
    关键词: '左室及右室、右房增大',
    扣分: '2',
  },
];
/**
 * @name 历年得分数据
 *
 */
export const One = {
  本人: [
    { sum: 30, year: '2013' },
    { sum: 87, year: '2013' },
    { sum: 66, year: '2014' },
    { sum: 36, year: '2015' },
    { sum: 50, year: '2016' },
    { sum: 85, year: '2017' },
    { sum: 75, year: '2018' },
    { sum: 89, year: '2019' },
  ],
  同年龄平均分: [
    { sum: 40, year: '2012' },
    { sum: 83, year: '2013' },
    { sum: 62, year: '2014' },
    { sum: 78, year: '2015' },
    { sum: 56, year: '2016' },
    { sum: 71, year: '2017' },
    { sum: 88, year: '2018' },
    { sum: 78, year: '2019' },
  ],
};

/**
 *
 */
export const abNormal: any[] = [
  { key: '1', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'unnormal' },
  { key: '2', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'normal' },
  { key: '3', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'unnormal' },
  { key: '4', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'normal' },
  { key: '5', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'unnormal' },
  { key: '6', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'unnormal' },
  { key: '7', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'unnormal' },
  { key: '8', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
  { key: '9', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
  { key: '10', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
  { key: '11', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
  { key: '12', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
];
export const BNormaL = [
  { key: '1', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'unnormal' },
  { key: '2', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'normal' },
  { key: '3', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'normal' },
  { key: '4', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'normal' },
  { key: '5', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'unnormal' },
  { key: '6', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'unnormal' },
  { key: '7', name: '血常规-A2356', age: '12.34~15.98ml', address: '30', icon: 'unnormal' },
  { key: '8', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
  { key: '9', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
  { key: '10', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
  { key: '11', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
  { key: '12', name: '血常规-A2356', age: '12.34~15.98ml', address: '40', icon: 'unnormal' },
];
export const detailMock: any[] = [
  { name: ' 右房腔体', score: 86 },
  { name: '三尖瓣', score: 79 },
  { name: '右室腔体', score: 100 },
  { name: '左房腔体', score: 63 },
  { name: '二尖瓣', score: 86 },
  { name: '心房间隔', score: 58 },
  { name: '心室间隔', score: 66 },
  { name: '主动脉瓣', score: 83 },
  { name: '心外膜', score: 66 },
  { name: '心包', score: 76 },
  { name: '心内膜', score: 86 },
  { name: '心肌', score: 82 },
  { name: '交感神经', score: 79 },
  { name: '副交感神经', score: 66 },
];
