import request from '@/utils/request';
import Url from './url';

export async function getUserMessage(params: string) {
  return request(`/api${Url.home}`, {
    method: 'GET',
    params: {
      user_id: params,
    },
  });
}

export function handleData(data: any) {
  const arrKey: any[] = [];
  const values: any[] = [];
  for (let index = 0; index < data.length; index += 1) {
    const key = Object.keys(data[index]);
    const value = Object.values(data[index]);
    arrKey.push(key[0]);
    values.push(value[0]);
  }
  return { time: arrKey, score: values };
}
export function handleEveryScore(data: any) {
  const arr: any[] = [];
  const score: any[] = [];
  const sum: any = [];
  for (let index = 0; index < data.length; index += 1) {
    const { checkup_time } = data[index];
    const value = data[index].score;
    const Aum = data[index].sum;
    arr.push(checkup_time);
    score.push(value);
    sum.push(Aum);
  }
  return { time: arr, score, sum };
}

export function range(params: string, data1: any, data2?: any, data3?: any) {
  let min: any;
  let min1: any;
  let min2: any;
  let min3: any;
  let max: any;
  let max1: any;
  let max2: any;
  let max3: any;

  if (data1) {
    const arr1: any = data1.map((v: any) => {
      return v[params];
    });
    // eslint-disable-next-line prefer-destructuring
    min1 = arr1.sort((a: any, b: any) => a - b)[0];
    max1 = arr1.sort((a: any, b: any) => a - b)[arr1.length - 1];
  }
  if (data2) {
    const arr2: any = data2.map((v: any) => {
      return v[params];
    });
    // eslint-disable-next-line prefer-destructuring
    min2 = arr2.sort((a: any, b: any) => a - b)[0];
    max2 = arr2.sort((a: any, b: any) => a - b)[arr2.length - 1];
  }
  if (data3) {
    const arr3: any = data3.map((v: any) => {
      return v[params];
    });
    // eslint-disable-next-line prefer-destructuring
    min3 = arr3.sort((a: any, b: any) => a - b)[0];
    max3 = arr3.sort((a: any, b: any) => a - b)[arr3.length - 1];
  }
  if (data1 && data2 && data3) {
    if (min1 <= min2 && min1 <= min3) {
      min = min1;
    } else if (min2 <= min1 && min2 <= min3) {
      min = min2;
    } else {
      min = min3;
    }

    if (max1 > max2 && max2 > max3) {
      max = max1;
    } else if (max2 > max3 && max2 > max1) {
      max = max2;
    } else {
      max = max3;
    }
  } else if (data2 && data2) {
    min = min1 < min2 ? min1 : min2;
    max = max1 > max2 ? max1 : max2;
  } else {
    min = min1;
    max = max1;
  }

  let currentMin = Math.round((min - 5) / 10) * 10;
  let currentMax = Math.round((max + 5) * 0.1) * 10;
  if (currentMax >= 100) {
    currentMax = 100;
  }
  if (currentMin <= 0) {
    currentMin = 0;
  }
  return [currentMin, currentMax];
}
export async function getDataGET(url: string, params: any) {
  return request(`/api${url}`, {
    method: 'GET',
    params,
  });
}
export async function getDataPOST(url: string, data: any) {
  return request(`/api${url}`, {
    method: 'POST',
    data,
  });
}
export async function detailsPOST(url: string, data: any) {
  return request(`/api${url}`, {
    method: 'POST',
    data,
  });
}

export async function ExceptionPOST(url: string, data: any) {
  return request(`/api${url}`, {
    method: 'POST',
    data,
  });
}

export async function QualitativePOST(url: string, data: any) {
  return request(`/api${url}`, {
    method: 'POST',
    data,
  });
}

export const handleChangeYear = (params: any) => {
  let obj = {};
  let score = {};
  params.map((v: any) => {
    obj = { ...obj, [v.checkup_time]: v.checkup_id };
    score = { ...score, [v.checkup_time]: v.score };
    return null;
  });
  return [obj, score];
};

/**
 * 左下详情红点递归
 * @data_总数据
 * @current_当前数据
 * @setData_点击红点后的Boolean值
 * @version 1.0
 */

export const recursion = (data: any, current: any, setData: boolean): any => {
  let result: any = null;
  if (!data) {
    return;
  }
  Object.keys(data).map((v: any, i: any): any => {
    if (result !== null) {
      return;
    }
    const item = data[i];
    if (item.name === current) {
      item.score_flag = setData;
      result = data;
    } else if (item.children && item.children.length > 0) {
      result = recursion(item.children, current, setData);
    }
    // eslint-disable-next-line consistent-return
    return null;
  });
  // eslint-disable-next-line consistent-return
  return result;
};

/**
 * @_缺省数组处理
 */
export const handleCardData = (res: any) => {
  const arr: any = [];
  for (let i = 0; i < res.length; i += 6) {
    const resArr = res.slice(i, i + 6);
    if (resArr.length < 6) {
      for (let index = 0; resArr.length < 6; index += 1) {
        resArr.push('');
      }
    }
    arr.push(resArr);
  }
  return arr;
};
/**
 * @handleAbNormal_处理定量数据
 */
export const handleAbNormal = (data: any[]) => {
  const array: any[] = [];
  data.map((v: any, i: number) => {
    const result: any = [];
    result.push({
      res: v.checkup_res,
      icon: v.checkup_res_contrast,
    });
    array.push({
      key: i,
      name: v.item_name + v.index_name,
      scope: v.reference_value,
      result,
    });
    return null;
  });
  return array;
};

// 关心程度排序
export const compare = (property: string | number) => {
  return (a: Record<string, any>, b: Record<string, any>) => {
    const value1 = a[property];
    const value2 = b[property];
    return value2 - value1;
  };
};
/**
 * @param {接收到的数据} res
 * @returns
 */
export const handleDate: any = (res: any) => {
  return Object.keys(res);
};
// 个人报告 右上历年健康趋势图
export function Healthover(params?: any) {
  return request('/api/health/health/user_year_score_chart/', {
    method: 'GET',
    params: { ...params },
    signal: new AbortController().signal,
  });
}
// 个人报告 雷达图的接口
export function PersonalRadar(params?: any) {
  return request('/api/health/health/user_score_radar_chart/', {
    method: 'GET',
    params: { ...params },
    signal: new AbortController().signal,
  });
}
// 覆盖率
export function CoverGet(params: any) {
  return request('/api/health/health/checkup_coverage/', {
    method: 'GET',
    params: { ...params },
    signal: new AbortController().signal,
  });
}
/**
 * @function 追加主键
 */

export function handleKeys(param: any) {
  const arr: any[] = [];
  param.map((v: any, i: number): any => {
    arr.push({
      ...v,
      keys: `${i}`,
    });
    return null;
  });
  return arr;
}
function RadarGET(params: any) {
  return request('/api/health/health/user_check_illness/', {
    method: 'GET',
    params: { ...params },
    // signal: new AbortController().signal,
  });
}

function AverageGET(params: any) {
  return request('/api/health/health/avg_score_common/', {
    method: 'GET',
    params: { ...params },
    // signal: new AbortController().signal,
  });
}

function HealthYearsGET(params: any) {
  return request('/api/health/health/user_year_score_chart/', {
    method: 'GET',
    params: { ...params },
    // signal: new AbortController().signal,
  });
}

// 信息列表
function InformationListGET(params: any) {
  return request('/api/health/health/user_basic_info/', {
    method: 'GET',
    params: { ...params },
    signal: new AbortController().signal,
  });
}

// 信息列表
function Inform(params: any) {
  return request('/api/health/health/checkup_time_select/', {
    method: 'GET',
    params: { ...params },
    signal: new AbortController().signal,
  });
}

export { RadarGET, AverageGET, HealthYearsGET, InformationListGET, Inform };
