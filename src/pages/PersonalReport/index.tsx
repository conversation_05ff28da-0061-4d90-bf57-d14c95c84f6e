/* eslint-disable no-useless-return */
import { useEffect, useState } from 'react';
import styles from './index.less';
import Instrument from './components/Instrument/index';
import IndustryHealth from './components/IndustryHealth/index';
import Asymmetric from './components/Asymmetric/index';
import Antenna from './components/Antenna/index';
import TeamCheck from './components/PersonaLillness/personaliLlness';
import ScoreDetail from './components/scoreDetail/index';
import Abnormal from './components/Abnormal/index';
import Sort from './components/Sort/index';
import exclamatory from '../../../public/exclamatory.svg';
import { connect } from '@/.umi/plugin-dva/exports';
import { Button, Popover } from 'antd';
import Header from '@/components/Header';
import Note from '@/components/Note';
import { Select, Spin } from 'antd';
import Cover from './components/Cover';
import Stressful from './components/Stressful';
import { Inform } from './service';
import { exportWordList } from '@/services/person_list';

const PersonalReport: React.FC<any> = (props: any) => {
  let last_checkup_id: string | undefined;
  let user_id: any | null;
  // let last_check_time;
  const content = (
    <div>
      <p style={{ marginBottom: '0' }}>
        报告类项目：基于医生丰富的临床经验或借助仪器设备辅助检查的项目
      </p>
      <p style={{ marginBottom: '0' }}>实验室项目：以血、尿、便等为标本进行实验室检测的项目</p>
    </div>
  );
  const { dispatch, location } = props;
  if (location.query?.user_id) {
    const { query } = location;
    last_checkup_id = query.last_checkup_id;
    user_id = query.user_id;
    // last_check_time = query.last_check_time;
  } else {
    user_id = JSON.parse(localStorage.getItem('user') as string).user_id;
    user_id = '' + user_id
  }

  const [option, setOpt] = useState<any[]>([]);
  const [SelectYear, setSelectYear] = useState<string>();
  const [instrument, setInstrument] = useState<boolean>(true);
  const [CheckUpId, setCheckUpId] = useState<string>();
  const [IsSpin, setIsSpin] = useState<boolean>(false);
  useEffect(() => {
    setCheckUpId(location.query.last_checkup_id)
    // 信息列表
    dispatch({ type: 'personal/initial', payload: { user_id } });
    if (last_checkup_id) {
      Inform({ user_id }).then((res) => {
        setOpt(res);
      });
      setInstrument(false);
      // 保存数据
      dispatch({ type: 'personal/personal_init', payload: { user_id, checkup_id: last_checkup_id } });
      // 检出疾病
      dispatch({ type: 'personal/detectDisease', payload: { checkup_id: last_checkup_id } });
    } else {
      Inform({ user_id }).then((res) => {
        setOpt(res);
        const { checkup_id } = res[0];
        dispatch({ type: 'personal/personal_init', payload: { user_id, checkup_id } });
        dispatch({ type: 'personal/detectDisease', payload: { checkup_id } });
      });
      setInstrument(true);
    }
    return () => {
      dispatch({ type: 'personal/destroy_value', payload: { user_id: 'null', checkup_id: 'null' } });
    };
  }, [last_checkup_id]);
  // 下拉框的事件
  const handleChange = (value: any, data: any) => {
    setSelectYear(value)
    if (data === undefined) {
      return;
    }
    setCheckUpId(data.key)
    dispatch({ type: 'personal/detectDisease', payload: { checkup_id: data.key } });
    dispatch({ type: 'personal/changeId', payload: { checkup_id: data.key } });
  };
  // if (instrument) {
  //   return <div className={styles.personal}>222</div>;
  // }
  return (
    <div style={{ margin: '16px' }}>
      <Spin tip="导出中..." size="large" spinning={IsSpin}>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          {instrument ? '' : <Header flag={'personal'} title={'返回个人列表'} />}
          <Button type='primary' onClick={() => {
            setIsSpin(true)
            exportWordList({ user_id_list: [location.query.user_id], checkup_id_list: [CheckUpId] }).then(res => {
              const elink = document.createElement("a");
              const date = new Date();
              const dates = date.toLocaleDateString().replaceAll('/', '') + '' + date.toLocaleTimeString().replaceAll(':', '')
              const fileName = `躯体健康量化分析报告(个人)_${dates}`;
              const blob = new Blob([res], { type: "application/zip" });
              elink.download = fileName;
              elink.style.display = "none";
              elink.href = window.URL.createObjectURL(blob);
              document.body.appendChild(elink);
              elink.click();
              window.URL.revokeObjectURL(elink.href); // 释放URL 对象
              document.body.removeChild(elink);
              setIsSpin(false)
            })
          }}>导出报告</Button>
        </div>
        <div className={styles.informationMin}>
          <div className={styles.information}>
            <div className={styles.union}>
              个人基本信息
              <span>
                <span style={{ marginRight: '10px' }}>体检时间:</span>
                <Select defaultValue={'请选择'} placeholder={'请选择'} style={{ width: 130 }} allowClear onChange={handleChange}>
                  {option.length > 0 ? option.map((v: any) => {
                    return (<Select.Option key={v.checkup_id} value={v.checkup_time}>{v.checkup_time}</Select.Option>);
                  }) : ''}
                </Select>
              </span>
            </div>
            <div style={{ borderBottom: '1px solid #ebeef5', paddingBottom: '1px' }} />
            <div className={styles.FenYI}>
              <Instrument select_year={SelectYear} />
              <Cover />
            </div>
            {/* <div className={styles.comparison}>数据比较</div>
            <div className={styles.IndustryHealthL}><IndustryHealth /></div> */}
            <div className={styles.xinXI}>
              信息列表
              <div className={styles.Asymmetric}>
                <Asymmetric />
              </div>
            </div>
          </div>
          <div className={styles.informationRight}>
            <div className={styles.leiDa}>
              <div className={styles.informationTop}>
                <div className={styles.Calendar}>
                  <div className={styles.CalendarTop}>
                    历年躯体健康得分趋势图
                    {/* <Popover placement="bottomRight" content={content}>
                    <img src={exclamatory} alt="提示" />
                  </Popover> */}
                  </div>
                  <Stressful />
                </div>
                <div className={styles.right_grid_photo}>
                  <Sort />
                </div>
              </div>
              <div className={styles.informationBottom}>
                <ScoreDetail />
              </div>
            </div>
          </div>
          <div className={styles.radar}>
            <div className={styles.radarTOp}>
              <div className={styles.radar_Top}>
                <span style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  六大功能系统得分分布图
                  <Popover placement="bottomRight" content={Note}><img src={exclamatory} alt="提示" /></Popover>
                </span>
              </div>
              <Antenna />
            </div>
            <div className={styles.radarZ}>
              <div className={styles.Abnormal}>
                所有异常项目详情
                <Popover placement="bottomRight" content={content}><img src={exclamatory} alt="提示" /></Popover>
              </div>
              <Abnormal />
            </div>
            <div className={styles.LookJb}>
              <div className={styles.disease}>检出疾病</div>
              <div className={styles.TeamCheck}>
                <TeamCheck />
              </div>
            </div>
          </div>
        </div>
        <div />
      </Spin>
    </div>
  );
};

export default connect((state: any) => {
  return state.personal;
})(PersonalReport);
