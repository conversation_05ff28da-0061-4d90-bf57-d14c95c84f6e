@import '~antd/es/style/themes/default.less';
.information {
  flex: 1.075;
  min-width: 410px;
  height: 951px;
  margin-right: 16px;
  border-radius: 4px;
  background: #fff;
  opacity: 1;
  .Asymmetric {
    font-weight: 500;
    font-size: 16px;
  }
  .union {
    display: flex;
    margin-left: 10px;
    margin-right: 10px;
    justify-content: space-between;
    align-items: center;
    height:3.25rem;
    font-size: 18px;
    font-family: PingFang SC;
  }
  .FenYI {
    width: 400px;
    // height: 300px;
    margin: auto;
    padding-top: 12px;
  }
  .comparison {
    // margin-top: -45px;
    margin-left: 17px;
    // font-weight: 600;
    font-size: 18px;
    font-family: PingFang SC;
  }
  .xinXI {
    margin-top: 16px;
    margin-left: 17px;
    // font-weight: 600;
    font-size: 18px;
    font-family: PingFang SC;
  }
  .IndustryHealthL {
    padding: 16px 16px 0 16px;
  }
}
.stressful{
  height: 260px;
}
.informationMin {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.informationRight {
  display: flex;
  // flex: 0.825;
  width: 760px;
  margin-right: 16px;
  // .leiDa {
  //   width: 760px;
  //   // margin-right: 32px;
  // }
  .informationTop {
    width: 760px;
    display: flex;
    .Calendar{
      width: 415px;
      height: 330px;
      background: #fff;
      opacity: 1;
    }
   .right_grid_photo{
      width:332px;
      height: 330px;
      margin-left: 15px;
      background: #fff;
      border: 0.071429rem solid rgba(211, 211, 211, 0.4);
      border-radius: 0.3rem;
      }
  }
  
  .informationBottom {
    :global {
      .ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab {
        margin-left: 0;
      }
      .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab {
        background: #f7f9ff;
        border: none;
      }
      .ant-tabs-tab-active {
        background: #fff !important;
      }
    }

    width: 760px;
    height: 587px;
    margin-top: 32px;
    // margin-left: 32px;
    background: #fff;
    border-radius: 4px;
    opacity: 1;
  }
}

.DatePicker {
  :global {
    .ant-picker {
      width: 20px;
      height: 30px;
      padding: 0 0 0;
      background: none;
      border: none;
    }
    .ant-picker-suffix {
      color: #bbc8db;
    }
    .ant-picker-input > input {
      color: #bbc8db;
    }
    .ant-picker-focused {
      box-shadow: 0 0 0 0.125rem transparent;
    }
  }

  position: relative;
  right: -92%;
  bottom: 57.9625rem;
  cursor: pointer;
}
.main {
  width: 100%;
  padding: 16px 16px 16px 16px;
}
.Industry {
  display: flex;
  width: 366px;
  height: 106px;
  margin-top: 17px;
  margin-left: 16px;
  background: #f5f9ff;
  border-radius: 4px;
  opacity: 1;
}
.army {
  display: flex;
}
.CalendarTop {
  width: 100%;
  height: 52px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 16px 16px;
  // font-weight: 600;
  font-size: 18px;
  border-bottom: 1px solid #ebeef5;
}
.CalendarCharts {
  padding-top: 21px;
  padding-right: 16px;
  padding-bottom: 16px;
  padding-left: 16px;
}
.radar {
  flex: 1;
  min-width: 372px;
  height: 960px;

  .radar_Top {
    min-width: 372px;
    height: 52px;
    padding: 16px 16px 16px 16px;
    font-size: 18px;
    border-bottom: 1px solid #ebeef5;
  }
  .AbnormalProject {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 372px;
    height: 52px;
    padding: 16px 16px 16px 16px;
    font-size: 18px;
    border-bottom: 1px solid #ebeef5;
  }
  .disease {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-width: 372px;
    height: 52px;
    padding: 16px 16px 16px 16px;
    font-size: 18px;
    border-bottom: 1px solid #ebeef5;
  }
  .Abnormal {
    width: 100%;
    min-width: 372px;
    height: 52px;
    color: red;
    padding: 16px 16px 16px 16px;
    font-size: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #ebeef5;
  }
  .radarTOp {
    min-width: 372px;
    height: 20.625rem;
    // margin-left: 32px;
    background: #fff;
    border-radius: 4px;
    opacity: 1;
  }
  .LookJb {
    min-width: 372px;
    height: 363px;
    margin-top: 32px;
    background: #fff;
    border-radius: 4px;
    opacity: 1;
  }
  .radarZ {
    min-width: 372px;
    height: 192px;
    margin-top: 32px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid #e7e8ec;
  }
}
.img {
  width: 20px;
  height: 20px;
}
.img > img {
  position: relative;
  right: 0;
  bottom: 909px;
  width: 20px;
  height: 20px;
  margin-left: 23.625rem;
  cursor: pointer;
}
// .stresse{
//   width: 100%;
//   height: 220px;
// }
